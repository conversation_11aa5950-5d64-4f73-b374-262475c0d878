import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { formatBlogDate } from '@/services/blog';
import type { BlogPost } from '@/types/blog';
import { Clock, User, Tag, Calendar } from 'lucide-react';

interface BlogCardProps {
  post: BlogPost;
  featured?: boolean;
}

export function BlogCard({ post, featured = false }: BlogCardProps) {
  const categoryColors = {
    'model-analysis': 'bg-blue-100 text-blue-800 border-blue-200',
    'release-notes': 'bg-green-100 text-green-800 border-green-200',
    'benchmark-analysis': 'bg-purple-100 text-purple-800 border-purple-200',
    'industry-news': 'bg-orange-100 text-orange-800 border-orange-200'
  };

  const categoryLabels = {
    'model-analysis': 'Modell-Analyse',
    'release-notes': 'Release Notes',
    'benchmark-analysis': 'Benchmark-Analyse',
    'industry-news': 'Branchen-News'
  };

  return (
    <Card className={`h-full transition-all hover:shadow-lg ${featured ? 'border-2 border-blue-200' : ''}`}>
      <div className="aspect-video overflow-hidden rounded-t-lg">
        <img
          src={post.featuredImage || '/images/blog/blog-default.png'}
          alt={post.title}
          className="w-full h-full object-cover transition-transform hover:scale-105"
          onError={(e) => {
            // Fallback to default blog image if featured image fails to load
            e.currentTarget.src = '/images/blog/blog-default.png';
          }}
        />
      </div>
      
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2 mb-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${categoryColors[post.category]}`}>
            {categoryLabels[post.category]}
          </span>
          {featured && (
            <span className="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
              Featured
            </span>
          )}
        </div>
        
        <h3 className={`font-semibold leading-tight line-clamp-2 ${featured ? 'text-lg' : 'text-base'}`}>
          <a 
            href={`/blog/${post.slug}`}
            className="hover:text-blue-600 transition-colors"
          >
            {post.title}
          </a>
        </h3>
      </CardHeader>
      
      <CardContent className="pt-0">
        <p className="text-sm text-gray-600 mb-4 line-clamp-3">
          {post.excerpt}
        </p>
        
        <div className="space-y-2 text-xs text-gray-500">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              <span>{formatBlogDate(post.publishDate)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>{post.readingTime} Min.</span>
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            <User className="w-3 h-3" />
            <span>{post.author.name}</span>
          </div>
          
          {post.tags.length > 0 && (
            <div className="flex items-start gap-1">
              <Tag className="w-3 h-3 mt-0.5 flex-shrink-0" />
              <div className="flex flex-wrap gap-1">
                {post.tags.slice(0, 3).map((tag, index) => (
                  <span 
                    key={tag}
                    className="inline-block px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded text-xs"
                  >
                    {tag}
                    {index < Math.min(post.tags.length, 3) - 1 && ','}
                  </span>
                ))}
                {post.tags.length > 3 && (
                  <span className="text-gray-400">+{post.tags.length - 3}</span>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface BlogCardListProps {
  posts: BlogPost[];
  showFeaturedFirst?: boolean;
}

export function BlogCardList({ posts, showFeaturedFirst = false }: BlogCardListProps) {
  const sortedPosts = showFeaturedFirst 
    ? [...posts].sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0))
    : posts;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {sortedPosts.map((post) => (
        <BlogCard 
          key={post.id} 
          post={post} 
          featured={showFeaturedFirst && post.featured}
        />
      ))}
    </div>
  );
}