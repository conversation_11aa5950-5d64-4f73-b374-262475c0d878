# Benchmark-Übersicht

Diese Datei enthält eine umfassende Übersicht aller in den Modelldaten verwendeten Benchmarks, basierend auf den Daten aus `src/data/models`.

## Kategorien und Benchmarks

### Reasoning & Knowledge

#### MMLU (Massive Multitask Language Understanding)
- **Beschreibung**: Umfassende Bewertung des Allgemeinwissens und der Reasoning-Fähigkeiten
- **Metrik**: Accuracy
- **Verwendung**: <PERSON> 3.5 Sonnet v2, Mistral Large 2411
- **Varianten**: 5-shot vs 0-shot CoT

#### GPQA Diamond (Graduate-Level Google-Proof Q&A)
- **Beschreibung**: Graduate-level Reasoning in Naturwissenschaften
- **Metrik**: Accuracy / Pass Rate
- **Verwendung**: Claude 3.5 Sonnet v2, <PERSON> 4, <PERSON> 3.7 <PERSON><PERSON>, <PERSON> 4, Gemini 2.5 Pro, GPT-4.1, o3-mini, o3
- **Besonderheiten**: 0-shot CoT, Extended Thinking verfügbar

#### DROP (Discrete Reasoning Over Paragraphs)
- **Beschreibung**: Reading Comprehension mit numerischem und diskretem Reasoning
- **Metrik**: F1 Score
- **Verwendung**: Claude 3.5 Sonnet v2
- **Konfiguration**: 3-shot

#### BIG-Bench-Hard
- **Beschreibung**: Schwierige Aufgaben aus dem BIG-Bench für komplexes Reasoning
- **Metrik**: Accuracy
- **Verwendung**: Claude 3.5 Sonnet v2
- **Konfiguration**: 3-shot CoT

#### Humanity's Last Exam
- **Beschreibung**: Extrem schwieriger Reasoning-Test
- **Metrik**: Pass Rate
- **Verwendung**: Gemini 2.5 Pro
- **Besonderheiten**: Keine Tools erlaubt

### Science

#### GPQA Diamond
- **Beschreibung**: Graduate-level wissenschaftliche Fragen
- **Metrik**: Pass Rate
- **Verwendung**: Siehe "Reasoning & Knowledge" Sektion
- **Kategorisierung**: Auch als Science-Benchmark verwendet

### Mathematics

#### MATH
- **Beschreibung**: Mathematische Problemlösung auf Hochschulniveau
- **Metrik**: Accuracy
- **Verwendung**: Claude 3.5 Sonnet v2
- **Konfiguration**: 0-shot CoT

#### GSM8K (Grade School Math 8K)
- **Beschreibung**: Grundschul-Mathematikaufgaben
- **Metrik**: Accuracy
- **Verwendung**: Claude 3.5 Sonnet v2
- **Konfiguration**: 0-shot CoT

#### MGSM (Multilingual Grade School Math)
- **Beschreibung**: Mehrsprachige Grundschul-Mathematik
- **Metrik**: Accuracy
- **Verwendung**: Claude 3.5 Sonnet v2
- **Konfiguration**: 0-shot CoT

#### AIME (American Invitational Mathematics Examination)
- **Beschreibung**: High School Mathematik-Wettbewerb
- **Metrik**: Pass Rate
- **Verwendung**: 
  - AIME 2024: Claude 3.7 Sonnet, Gemini 2.5 Pro
  - AIME 2025: Claude Opus 4, Claude Sonnet 4, Gemini 2.5 Pro, o3-mini, o3
- **Besonderheiten**: Extended Thinking verfügbar, nucleus sampling top_p=0.95

### Code Generation

#### HumanEval
- **Beschreibung**: Python Code-Generierung Benchmark
- **Metrik**: Pass Rate
- **Verwendung**: Claude 3.5 Sonnet v2, GPT-4o, Mistral Large 2411
- **Konfiguration**: 0-shot, pass@1

#### LiveCodeBench v5
- **Beschreibung**: Aktuelle Code-Generierungsaufgaben
- **Metrik**: Pass Rate
- **Verwendung**: Gemini 2.5 Pro
- **Besonderheiten**: Regelmäßig aktualisierte Aufgaben

#### Codeforces
- **Beschreibung**: Competitive Programming Platform
- **Metrik**: Elo Rating
- **Verwendung**: o3-mini
- **Besonderheiten**: Ranking-basierte Bewertung

### Code Editing

#### Aider-Polyglot
- **Beschreibung**: Code-Bearbeitung in verschiedenen Programmiersprachen
- **Metrik**: Pass Rate
- **Verwendung**: Gemini 2.5 Pro
- **Varianten**: 
  - Whole file editing
  - Diff-based editing

### Agentic Coding

#### SWE-bench Verified
- **Beschreibung**: Reale Software-Engineering-Aufgaben
- **Metrik**: Pass Rate / Success Rate
- **Verwendung**: Claude Opus 4, Claude 3.7 Sonnet, Claude Sonnet 4, Gemini 2.5 Pro, GPT-4.1, o3-mini, o3
- **Tools**: Bash/editor tools
- **Besonderheiten**: Parallel test-time compute verfügbar

#### Terminal-bench
- **Beschreibung**: Agentic terminal-basierte Coding-Aufgaben
- **Metrik**: Pass Rate
- **Verwendung**: Claude Opus 4, Claude 3.7 Sonnet, Claude Sonnet 4, GPT-4.1, o3
- **Framework**: Claude Code als Agent Framework

#### TAU-bench (Tool-Augmented Understanding)
- **Beschreibung**: Agentic Tool Use Benchmark
- **Metrik**: Success Rate
- **Domänen**:
  - **Retail**: Claude Opus 4, Claude 3.7 Sonnet, Claude Sonnet 4, GPT-4.1, o3
  - **Airline**: Claude Opus 4, Claude 3.7 Sonnet, Claude Sonnet 4, GPT-4.1, o3
- **Besonderheiten**: Extended Thinking und Tool Use

#### Agentic Coding Evaluation
- **Beschreibung**: Interne Bewertung für Bug-Fixing und Funktionalitätserweiterung
- **Metrik**: Success Rate
- **Verwendung**: Claude 3.5 Sonnet v2
- **Besonderheiten**: Interne Evaluation

### Factuality

#### SimpleQA
- **Beschreibung**: Einfache Fragen zur Faktentreue
- **Metrik**: Accuracy
- **Verwendung**: Gemini 2.5 Pro
- **Fokus**: Korrektheit der Antworten

### Visual Reasoning

#### MMMU (Massive Multi-discipline Multimodal Understanding)
- **Beschreibung**: Multimodales Verständnis und Reasoning
- **Metrik**: Accuracy
- **Verwendung**: Claude Opus 4, Claude 3.7 Sonnet, Claude Sonnet 4, Gemini 2.5 Pro, GPT-4.1, GPT-4o, o3-mini, o3
- **Besonderheiten**: Validation set, Extended Thinking verfügbar

### Image Understanding

#### Vibe-Eval (Reka)
- **Beschreibung**: Bildverständnis-Benchmark von Reka
- **Metrik**: Accuracy
- **Verwendung**: Gemini 2.5 Pro
- **Fokus**: Grundlegendes Bildverständnis

### Long Context

#### MRCR (Multi-hop Reading Comprehension with Reasoning)
- **Beschreibung**: Verarbeitung langer Texte und Kontexte
- **Metrik**: Accuracy
- **Verwendung**: Gemini 2.5 Pro
- **Kontext-Längen**: 
  - 128k average: 94.5%
  - 1M pointwise: 83.1%
- **Varianten**: Average vs. Pointwise Bewertung

### Multilingual Performance

#### MMLU (Multilingual MMLU)
- **Beschreibung**: Mehrsprachige Q&A über 14 nicht-englische Sprachen
- **Metrik**: Accuracy
- **Verwendung**: Claude Opus 4, Claude 3.7 Sonnet, Claude Sonnet 4, GPT-4.1, o3
- **Besonderheiten**: Extended Thinking verfügbar

#### Global MMLU (Lite)
- **Beschreibung**: Globale mehrsprachige Wissensbewertung
- **Metrik**: Accuracy
- **Verwendung**: Gemini 2.5 Pro
- **Fokus**: Internationale Perspektiven

## Bewertungsarten

### Attempt Types
- **single attempt**: Ein Versuch pro Aufgabe
- **multiple attempts**: Mehrere Versuche erlaubt, bester Wert zählt
- **pass@1**: Erfolgsrate beim ersten Versuch
- **average**: Durchschnittswert über mehrere Tests
- **pointwise**: Punkt-für-Punkt-Bewertung

### Alternative Scores
- **multipleAttempts**: Score bei mehreren Versuchen
- **whole**: Vollständige Datei-Bearbeitung (Code-Benchmarks)
- **diff**: Nur geänderte Teile bewertet (Code-Benchmarks)
- **average**: Durchschnittswert
- **pointwise**: Detaillierte Punkt-Bewertung

## Metriken-Übersicht

- **Accuracy**: Genauigkeit der Antworten (0-100%)
- **Pass Rate**: Erfolgsrate bei Tests (0-100%)
- **Success Rate**: Erfolgsrate bei komplexen Aufgaben (0-100%)
- **F1 Score**: Harmonisches Mittel aus Precision und Recall
- **Elo Rating**: Ranking-basierte Bewertung (z.B. Codeforces)

## Besondere Konfigurationen

### Chain-of-Thought (CoT)
- **0-shot CoT**: Ohne Beispiele, mit Reasoning-Aufforderung
- **3-shot CoT**: Mit drei Beispielen und Reasoning
- **5-shot**: Mit fünf Beispielen

### Extended Thinking
- Verfügbar für Claude-Modelle
- Bis zu 64k Tokens für komplexes Reasoning
- Nucleus sampling mit top_p=0.95

### Tool Usage
- **bash/editor tools**: Für SWE-bench
- **parallel test-time compute**: Für verbesserte Ergebnisse
- **Extended Thinking und Tool Use**: Für TAU-bench

## Modell-spezifische Benchmarks

### Claude-Familie
- Starke Leistung bei SWE-bench, TAU-bench, GPQA Diamond
- Extended Thinking für komplexe Aufgaben
- Agentic Coding Evaluation (intern)

### Gemini-Familie
- Umfassende Benchmark-Abdeckung
- Humanity's Last Exam (exklusiv)
- Vibe-Eval, MRCR für spezielle Fähigkeiten

### GPT-Familie
- Fokus auf praktische Anwendungen
- MMMU, HumanEval als Kernbenchmarks
- Codeforces für o3-mini

### OpenAI o3-Familie
- Reasoning-fokussierte Benchmarks
- Hohe AIME-Scores
- Starke mathematische Fähigkeiten

---

*Letzte Aktualisierung: 8. Juni 2025*
*Datenquelle: Model Cards aus src/data/models*