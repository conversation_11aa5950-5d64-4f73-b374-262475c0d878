# i18n Content Guidelines für Astro + GitLab Pages

## Übersicht

Diese Regeln gelten für alle neuen Inhalte im LLM Browser Projekt, das Astro Framework mit i18n-Unterstützung und GitLab Pages CI/CD-Pipeline verwendet.

## 🌍 Unterstützte Sprachen

- **Deutsch (de)**: Standard-Sprache
- **Englisch (en)**: Vollständige Übersetzungsunterstützung  
- **Polnisch (pl)**: Vollständige Übersetzungsunterstützung

## 📁 Dateistruktur für Übersetzungen

### Translation Files
```

static/locales/           # Backup-Verzeichnis
├── de/i18n.json
├── en/i18n.json
└── pl/i18n.json
```

## 🔧 Technische Implementierung

### Client-side Only i18n System
Das Projekt verwendet ausschließlich **Client-side i18n**:

- **Alle Übersetzungen** werden client-side geladen und verwaltet
- **Keine Server-side Translation Loading** in Astro-Seiten
- **TranslationContext** für React-Komponenten
- **data-translate Attribute** für statische HTML-Elemente
- **Automatische Spracherkennung** über localStorage/Cookies client-side


## 📝 Regeln für neue Inhalte

### 1. Translation Keys

#### ✅ DO: Konsistente Key-Struktur
```json
{
  "section": {
    "subsection": {
      "key": "Übersetzter Text"
    }
  }
}
```

#### ✅ DO: Aussagekräftige Key-Namen
```json
{
  "models": {
    "filters": {
      "search_placeholder": "Suche nach Name, Anbieter oder Modellgruppe..."
    }
  }
}
```

#### ❌ DON'T: Flache oder unklare Keys
```json
{
  "text1": "Irgendein Text",
  "btn": "Klick mich"
}
```

### 2. Astro-Seiten (.astro Dateien)

#### ✅ DO: Client-side Translation mit data-translate
```astro
---
// Keine Server-side Translation Loading!
// Alle Übersetzungen werden client-side geladen
---

<Layout title="Page Title">
  <h1 data-translate="page.title">Fallback Title</h1>
  <p data-translate="page.description">Fallback description</p>
</Layout>
```

#### ✅ DO: Statische Fallback-Texte verwenden
```astro
<h1 data-translate="models.header">Models</h1>
<p data-translate="models.description">Browse and compare models</p>
```

#### ❌ DON'T: Server-side Translation Loading
```astro
---
// NIEMALS Server-side Übersetzungen laden!
import fs from "fs";
const translations = JSON.parse(fs.readFileSync(...)); // ❌ FALSCH
---

<h1>{translations.models?.header}</h1>  <!-- ❌ FALSCH -->
```

### 3. React-Komponenten

#### ✅ DO: TranslationContext verwenden
```tsx
import { useTranslation } from '../contexts/TranslationContext';

function MyComponent() {
  const { getTranslation } = useTranslation();
  
  return (
    <div>
      <h1>{getTranslation('models.header')}</h1>
      <p>{getTranslation('models.description')}</p>
    </div>
  );
}
```

#### ✅ DO: Parameter-Ersetzung
```tsx
// Translation file
{
  "models": {
    "count": "Zeige {{count}} von {{total}} Modellen"
  }
}

// Component
const text = getTranslation('models.count', { count: 10, total: 50 });
```

#### ✅ DO: TranslationProvider wrappen
```astro
<TranslationProvider
  initialTranslations={translations}
  initialLang={currentLang}
  client:only="react"
>
  <MyReactComponent />
</TranslationProvider>
```

### 4. Neue Translation Keys hinzufügen

#### ✅ DO: Alle Sprachen synchron halten
1. Key in `static/locales/de/i18n.json` hinzufügen (Hauptverzeichnis)
2. Key in `static/locales/en/i18n.json` hinzufügen
3. Key in `static/locales/pl/i18n.json` hinzufügen
4. **Wichtig**: Astro kopiert `static/` nach `public/` beim Build

#### ✅ DO: Konsistente Struktur
```json
// Alle drei Dateien sollten dieselbe Struktur haben
{
  "new_section": {
    "title": "...",
    "description": "...",
    "actions": {
      "save": "...",
      "cancel": "..."
    }
  }
}
```

#### ✅ DO: data-translate Attribute verwenden
```astro
<h1 data-translate="new_section.title">Fallback Title</h1>
<button data-translate="new_section.actions.save">Save</button>
```

### 5. GitLab CI/CD Kompatibilität

#### ✅ DO: Client-side Loading für Static Sites
- Übersetzungen werden client-side zur Laufzeit geladen
- Keine Server-side Translation Loading beim Build
- Statische HTML-Generierung mit Fallback-Texten
- Client-side Spracherkennung und -wechsel

#### ✅ DO: Asset-Pfade beachten
```javascript
// astro.config.mjs
export default defineConfig({
  site: 'https://iteratec-llm-browser-b6a964.pages.iteratec.de',
  base: '/',
  output: 'static',
  outDir: 'public',        // GitLab Pages erwartet 'public/'
  publicDir: 'static',     // Astro Assets aus 'static/'
});
```

#### ✅ DO: Translation Files im static/ Verzeichnis
- `static/locales/` für Astro Assets (wird zu `public/locales/` kopiert)
- Client-side fetch von `/locales/{lang}/i18n.json`
- Automatische Verfügbarkeit nach Build

## 🚀 CI/CD Pipeline Überlegungen

### Build-Prozess
```yaml
# .gitlab-ci.yml
build:
  stage: build
  script:
    - npm ci
    - npm run generate:data    # Statische Daten generieren
    - npm run build           # Astro Build mit i18n
  artifacts:
    paths:
      - public/               # Enthält gebaute Site + Übersetzungen
```

### ✅ DO: Pre-Build Validierung
```bash
# Vor dem Build prüfen
npm run lint:check           # ESLint für Code-Qualität
npm run test:run            # Tests ausführen
```

## 🔍 Testing & Validierung

### ✅ DO: Übersetzungen testen
1. Alle drei Sprachen im Browser testen
2. Fallback-Verhalten prüfen
3. Parameter-Ersetzung validieren
4. Build-Prozess lokal testen

### ✅ DO: Lokale Entwicklung
```bash
npm run dev                 # Entwicklungsserver
# Sprache über Browser-DevTools oder localStorage ändern
localStorage.setItem('preferredLanguage', 'en');
```

## ⚠️ Häufige Fehler vermeiden

### ❌ DON'T: Server-side Translation Loading
```astro
---
// NIEMALS Server-side Übersetzungen laden!
import fs from "fs";
import path from "path";
const translations = JSON.parse(fs.readFileSync(...)); // ❌ FALSCH
---
```

### ❌ DON'T: Inkonsistente Translation Files
- Fehlende Keys in einer Sprache
- Unterschiedliche Struktur zwischen Sprachen
- Vergessene Updates in `static/locales/`

### ❌ DON'T: Hardcodierte Texte ohne data-translate
```astro
<h1>Models</h1>  <!-- ❌ FALSCH: Nicht übersetzbar -->
<h1 data-translate="models.header">Models</h1>  <!-- ✅ RICHTIG -->
```

### ❌ DON'T: Server-side Spracherkennung
```astro
---
// ❌ FALSCH: Server-side Spracherkennung
const cookieLang = Astro.cookies?.get("preferredLanguage")?.value;
---
```

## 📋 Checkliste für neue Inhalte

- [ ] Translation Keys in allen drei Sprachen hinzugefügt (`static/locales/`)
- [ ] `data-translate` Attribute für statische HTML-Elemente gesetzt
- [ ] Fallback-Texte in HTML-Elementen definiert
- [ ] React-Komponenten verwenden TranslationContext
- [ ] **Keine Server-side Translation Loading** verwendet
- [ ] Lokale Tests in allen Sprachen durchgeführt
- [ ] Build-Prozess lokal getestet (client-side Spracherkennung)
- [ ] CI/CD Pipeline erfolgreich durchlaufen

## 🔗 Referenzen

- **i18n Implementation Guide**: `memory-bank/i18n.md`
- **Feature Documentation**: `docs/feature-i18n-recommendations/`
- **GitLab CI/CD**: `.gitlab-ci.yml`
- **Astro Config**: `astro.config.mjs`
