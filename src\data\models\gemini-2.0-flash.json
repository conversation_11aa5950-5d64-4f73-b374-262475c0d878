{"basicInfo": {"modelId": "gemini-2.0-flash-001", "displayName": "Gemini 2.0 Flash", "provider": "Google", "modelFamily": "Gemini", "version": "001", "description": "Funktionen der nächsten Generation und verbesserte Fähigkeiten, darunter höhere Geschwindigkeit, integrierte Tools und multimodale Generierung", "releaseDate": "2025-02-05", "status": "Deprecated", "knowledgeCutoff": "Juni 2024", "deprecationDate": "2025-06-20", "shutdownDate": "2025-06-20"}, "technicalSpecs": {"contextWindow": 1048576, "maxOutputTokens": 8192, "supportedInputTypes": ["text", "image", "audio", "video", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"maxImages": 3000, "maxImageSize": "7 MB", "maxAudioLength": "8.4 Stunden", "maxVideoLength": "45 Minuten", "supportedMimeTypes": {"image": ["image/png", "image/jpeg", "image/webp"], "audio": ["audio/x-aac", "audio/flac", "audio/mp3", "audio/m4a", "audio/mpeg", "audio/opus", "audio/wav"], "video": ["video/mp4", "video/webm", "video/quicktime", "video/mpeg"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": true, "audioOutput": false, "imageGeneration": false, "codeExecution": true, "systemInstructions": true, "promptCaching": false, "batchProcessing": true, "reasoning": false, "thinking": false, "grounding": true, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"tokensPerMinute": 40000000}, "temperature": {"min": 0, "max": 2, "default": 1.0}, "topP": 0.95, "topK": 64}, "pricing": {"inputCostPer1MTokens": 0.1, "inputCostPer1MTokensAudio": 0.7, "outputCostPer1MTokens": 0.4, "cachingCosts": {"cacheWrites": 0.025, "cacheWritesAudio": 0.175, "cacheStorage": 1.0}, "currency": "USD", "notes": {"inputPricing": "Text/Image/Video: $0.10/1M, Audio: $0.70/1M", "outputPricing": "$0.40/1M tokens", "cachePricing": "Text/Image/Video: $0.025/1M, Audio: $0.175/1M, Storage: $1.00/1M tokens per hour"}}, "availability": {"regions": [{"region": "global", "availability": "GA"}, {"region": "us-central1", "availability": "GA"}, {"region": "us-east1", "availability": "GA"}, {"region": "us-east4", "availability": "GA"}, {"region": "us-east5", "availability": "GA"}, {"region": "us-south1", "availability": "GA"}, {"region": "us-west1", "availability": "GA"}, {"region": "us-west4", "availability": "GA"}, {"region": "europe-central2", "availability": "GA"}, {"region": "europe-north1", "availability": "GA"}, {"region": "europe-southwest1", "availability": "GA"}, {"region": "europe-west1", "availability": "GA"}, {"region": "europe-west4", "availability": "GA"}, {"region": "europe-west8", "availability": "GA"}, {"region": "europe-west9", "availability": "GA"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI"]}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": true, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 71.9, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "Multimodal multiple-choice questions with images"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 62.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-06-06", "notes": "Graduate-level physics, chemistry, and biology questions"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 89.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-15", "notes": "MATH 500 benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Code generation", "score": 1039.93, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition for web development challenges"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 22.2, "metric": "Pass Rate", "attemptType": "Whole file editing", "date": "2024-12-22", "notes": "Code-Bearbeitung in verschiedenen Programmiersprachen mit verschiedenen Editing-Modi"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 44.2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Reale Software-Engineering-Aufgaben mit verifizierten GitHub Issues"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 100, "metric": "Percent Cases Well Formed", "attemptType": "Whole file editing", "date": "2024-12-22", "notes": "Prozentsatz der wohlgeformten Antworten bei der mehrsprachigen Code-Bearbeitung"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 31.8, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Bewertung von Large Language Models für Code-Generierung mit kontinuierlich aktualisierten Problemen aus aktuellen Programmierwettbewerben"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 6.56, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Gemini 2.0 Flash Thinking (January 2025): 6.56±0.97"}], "metadata": {"lastUpdated": "2025-06-21T10:24:00Z", "dataSource": "Google Cloud Vertex AI Documentation, Google Gemini API Pricing (2025-06-18), VALS.ai, Vellum.ai, WebDev Arena, MATH 500 Benchmark Update", "version": "1.2"}}