# Gesamt-Prompt für neues Modell: Benchmark-Daten sammeln

Dieser Prompt-Guide führt schrittweise durch die Sammlung aller relevanten Benchmark-Daten für ein neues Modell. 

**Beispiel-Modell**: `gemini-2.0-flash`  
**Zieldatei**: `src/data/models/gemini-2.0-flash.json`

## Übersicht der zu sammelnden Benchmarks

Basierend auf `src/data/benchmark-descriptions.json` müssen folgende Benchmark-Kategorien abgedeckt werden:

### 🧠 Reasoning & Knowledge
- MMLU
- DROP  
- BIG-Bench-Hard
- Humanity's Last Exam

### 📐 Mathematics
- MATH
- MGSM
- AIME 2024
- AIME 2025

### 🔬 Science
- GPQA Diamond

### 💻 Code generation
- LiveCodeBench v2025

### ✏️ Code editing
- Aider-Polyglot

### 🤖 Agentic coding
- SWE-bench Verified
- Terminal-bench
- TAU-bench Retail
- TAU-bench Airline
- WebDev-Arena

### 👁️ Visual reasoning
- MathVista
- CharXiv-Reasoning
- MMMU

### 🖼️ Image understanding
- Vibe-Eval (Reka)

### 📄 Long context
- Graphwalks BFS <128k
- MRCR

### 🌍 Multilingual performance
- MMLU (multilingual)
- MMLU
- Global MMLU (Lite)

### 📋 Instruction following
- Internal API instruction following (hard)
- MultiChallenge
- IFEval
- Multi-IF

### 🔧 Function calling
- ComplexFuncBench

### ✅ Factuality
- SimpleQA

---

## Schritt-für-Schritt Prompts

### Schritt 1: Grundstruktur erstellen

```
Erstelle eine neue Modelkarte für "gemini-2.0-flash" unter src/data/models/gemini-2.0-flash.json mit der Grundstruktur auf Basis des Schemas unter @/src/data/models/model-card-json-schema.md 

Model-Card-Basis-Infos:
- @https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-0-flash?hl=de

Nutze als Grunddaten folgende Informationen:
- Benchmark: "AIME": @https://www.vals.ai/benchmarks/aime-2025-05-30 
- Benchmark: "MMMU": @https://www.vals.ai/benchmarks/mmmu-05-30-2025 
- Benchmark: "SWE-bench Verified": @https://www.swebench.com/index.html 
- Benchmark: "Terminal-Bench": @https://www.tbench.ai/leaderboard 
- Benchmark: "Webdev-Arena": @https://web.lmarena.ai/leaderboard 
- Benchmark: "GPQA-Diamond": @https://www.vellum.ai/llm-leaderboard 
- Benchmark: "LiveCodeBench v2025": @https://livecodebench.github.io/leaderboard.html 
- Benchmark: "Humanity's Last Exam": @https://scale.com/leaderboard/humanitys_last_exam

Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md .

```

### Updates durchführen

```
Aktualisiere auch die Polyglot-Benchmarks entsprechend @/src/data/benchmarks/benchmark-descriptions.json auf Basis von @/src/data/polyglot_benchmarks.json in @/src/data/models/gemini-2.0-flash.json . Achte darauf dass folgende WErte findest und schreibst:

- "Aider-Polyglot-Wellformated"
- "Aider-Polyglot"

```

```
Ergänze die Werte 
- "SWE-bench Verified" mit 44.20
```

### 2. Beispiel mit Deekseek V3

```
Erstelle eine neue Modelkarte für "Deekseek V3" unter src/data/models/deepseek-v3.json mit der Grundstruktur auf Basis des Schemas unter @/src/data/models/model-card-json-schema.md . Achte auf die exakte Schreibweise der Benchmarks aus @/src/data/benchmarks/benchmark-descriptions.json! Andernfalls ist ein Matching nicht möglich! Achte zudem darauf das entsprechend Schema die Kosten korrekt übernommen werden!


**Step 1:**
Model-Card-Basis-Infos:
- @https://huggingface.co/deepseek-ai/DeepSeek-V3


Nutze als Referenzbeispiel: 
<example>
@/src/data/models/claude-sonnet-4.json 
</example>

**Step 2:**
Prüfe ob weitere Benchmarkdaten aktualisiert werden können. 

- Benchmark: "AIME": @https://www.vals.ai/benchmarks/aime-2025-05-30 
- Benchmark: "MMMU": @https://www.vals.ai/benchmarks/mmmu-05-30-2025 
- Benchmark: "SWE-bench Verified": @https://www.swebench.com/index.html 
- Benchmark: "Terminal-Bench": @https://www.tbench.ai/leaderboard 
- Benchmark: "Webdev-Arena": @https://web.lmarena.ai/leaderboard 
- Benchmark: "GPQA-Diamond": @https://www.vellum.ai/llm-leaderboard 
- Benchmark: "LiveCodeBench v2025": @https://livecodebench.github.io/leaderboard.html 
- Benchmark: "Humanity's Last Exam": @https://scale.com/leaderboard/humanitys_last_exam_text_only

**Step 3:**
Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md .

**Step 4:**
Aktualisiere auch die Polyglot-Benchmarks entsprechend @/src/data/benchmarks/benchmark-descriptions.json auf Basis von @/src/data/polyglot_benchmarks.json in @/src/data/models/gemini-2.0-flash.json . Achte darauf dass folgende WErte findest und schreibst:

- "Aider-Polyglot-Wellformated"
- "Aider-Polyglot"
```

## Beispiel 3: Grok 3 Beta


```
Erstelle eine neue Modelkarte für "Grok 3 Beta" unter src/data/models/grok-v3-beta.json mit der Grundstruktur auf Basis des Schemas unter @/src/data/models/model-card-json-schema.md . Achte auf die exakte Schreibweise der Benchmarks aus @/src/data/benchmarks/benchmark-descriptions.json! Andernfalls ist ein Matching nicht möglich! Achte zudem darauf das entsprechend Schema die Kosten korrekt übernommen werden!


**Step 1:**
Model-Card-Basis-Infos:
- @https://docs.x.ai/docs/models


Nutze als Referenzbeispiel: 
<example>
@/src/data/models/claude-sonnet-4.json 
</example>

**Step 2:**
Prüfe ob weitere Benchmarkdaten aktualisiert werden können. 

- Benchmark: "AIME": @https://www.vals.ai/benchmarks/aime-2025-05-30 
- Benchmark: "MMMU": @https://www.vals.ai/benchmarks/mmmu-05-30-2025 
- Benchmark: "SWE-bench Verified": @https://www.swebench.com/index.html 
- Benchmark: "Terminal-Bench": @https://www.tbench.ai/leaderboard 
- Benchmark: "Webdev-Arena": @https://web.lmarena.ai/leaderboard 
- Benchmark: "GPQA-Diamond": @https://www.vellum.ai/llm-leaderboard 
- Benchmark: "LiveCodeBench v2025": @https://livecodebench.github.io/leaderboard.html 
- Benchmark: "Humanity's Last Exam": @https://scale.com/leaderboard/humanitys_last_exam_text_only

Achte auf die exakte Schreibweise der Benchmarks aus @/src/data/benchmarks/benchmark-descriptions.json! Andernfalls ist ein Matching nicht möglich!

**Step 3:**
Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md .

**Step 4:**
Aktualisiere auch die Polyglot-Benchmarks entsprechend @/src/data/benchmarks/benchmark-descriptions.json auf Basis von @/src/data/polyglot_benchmarks.json in @/src/data/models/grok-v3.json  . Achte darauf dass folgende WErte findest und schreibst:

- "Aider-Polyglot-Wellformated"
- "Aider-Polyglot"
```

## Beispiel 4: o3-pro


```
Erstelle eine neue Modelkarte für "o3-pro" unter src/data/models/o3-pro.json mit der Grundstruktur auf Basis des Schemas unter @/src/data/models/model-card-json-schema.md . Achte auf die exakte Schreibweise der Benchmarks aus @/src/data/benchmarks/benchmark-descriptions.json! Andernfalls ist ein Matching nicht möglich! Achte zudem darauf das entsprechend Schema die Kosten korrekt übernommen werden!


**Step 1:**
Model-Card-Basis-Infos:
- @https://platform.openai.com/docs/models/o3-pro
- @https://openai.com/index/introducing-o3-and-o4-mini/


Nutze als Referenzbeispiel: 
<example>
@/src/data/models/claude-sonnet-4.json 
</example>

**Step 2:**
Prüfe ob weitere Benchmarkdaten aktualisiert werden können. 

- Benchmark: "AIME": @https://www.vals.ai/benchmarks/aime-2025-05-30 
- Benchmark: "MMMU": @https://www.vals.ai/benchmarks/mmmu-05-30-2025 
- Benchmark: "SWE-bench Verified": @https://www.swebench.com/index.html 
- Benchmark: "Terminal-Bench": @https://www.tbench.ai/leaderboard 
- Benchmark: "Webdev-Arena": @https://web.lmarena.ai/leaderboard 
- Benchmark: "GPQA-Diamond": @https://www.vellum.ai/llm-leaderboard 
- Benchmark: "LiveCodeBench v2025": @https://livecodebench.github.io/leaderboard.html 
- Benchmark: "Humanity's Last Exam": @https://scale.com/leaderboard/humanitys_last_exam_text_only

Achte auf die exakte Schreibweise der Benchmarks aus @/src/data/benchmarks/benchmark-descriptions.json! Andernfalls ist ein Matching nicht möglich!

**Step 3:**
Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md .

**Step 4:**
Aktualisiere auch die Polyglot-Benchmarks entsprechend @/src/data/benchmarks/benchmark-descriptions.json auf Basis von @/src/data/polyglot_benchmarks.json in @/src/data/models/o3-pro.json  . Achte darauf dass folgende WErte findest und schreibst:

- "Aider-Polyglot-Wellformated"
- "Aider-Polyglot"
```

## Beispiel 4: magistral medium


```
Erstelle eine neue Modelkarte für "magistral medium" unter src/data/models/magistral-medium.json mit der Grundstruktur auf Basis des Schemas unter @/src/data/models/model-card-json-schema.md . Achte auf die exakte Schreibweise der Benchmarks aus @/src/data/benchmarks/benchmark-descriptions.json! Andernfalls ist ein Matching nicht möglich! Achte zudem darauf das entsprechend Schema die Kosten korrekt übernommen werden!


**Step 1:**
Model-Card-Basis-Infos:
- @https://mistral.ai/news/magistral
- @https://ollama.com/library/magistral


Nutze als Referenzbeispiel: 
<example>
@/src/data/models/claude-sonnet-4.json 
</example>

**Step 2:**
Prüfe ob weitere Benchmarkdaten aktualisiert werden können. 

- Benchmark: "AIME": @https://www.vals.ai/benchmarks/aime-2025-05-30 
- Benchmark: "MMMU": @https://www.vals.ai/benchmarks/mmmu-05-30-2025 
- Benchmark: "SWE-bench Verified": @https://www.swebench.com/index.html 
- Benchmark: "Terminal-Bench": @https://www.tbench.ai/leaderboard 
- Benchmark: "Webdev-Arena": @https://web.lmarena.ai/leaderboard 
- Benchmark: "GPQA-Diamond": @https://www.vellum.ai/llm-leaderboard 
- Benchmark: "LiveCodeBench v2025": @https://livecodebench.github.io/leaderboard.html 
- Benchmark: "Humanity's Last Exam": @https://scale.com/leaderboard/humanitys_last_exam_text_only

Achte auf die exakte Schreibweise der Benchmarks aus @/src/data/benchmarks/benchmark-descriptions.json! Andernfalls ist ein Matching nicht möglich!

**Step 3:**
Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md .

**Step 4:**
Aktualisiere auch die Polyglot-Benchmarks entsprechend @/src/data/benchmarks/benchmark-descriptions.json auf Basis von @/src/data/polyglot_benchmarks.json in @/src/data/models/magistral-medium.json  . Achte darauf dass folgende WErte findest und schreibst:

- "Aider-Polyglot-Wellformated"
- "Aider-Polyglot"
```


Quelle: https://mistral.ai/news/magistral , Benchmark-Daten

```
Aktualisiere die Benchmarkdante