{"basicInfo": {"modelId": "mistral-large-2411", "displayName": "<PERSON><PERSON><PERSON> Large (24.11)", "provider": "<PERSON><PERSON><PERSON>", "modelFamily": "<PERSON><PERSON><PERSON>", "version": "2411", "description": "Die nächste Version des Modells Mistral Large (24.07) mit verbesserten Funktionen für Schlussfolgerungen und Funktionsaufrufe", "releaseDate": "2024-11-21", "status": "GA", "knowledgeCutoff": "Oktober 2024"}, "technicalSpecs": {"contextWindow": 128000, "maxOutputTokens": 8192, "architecture": "Transformer", "parameterCount": "<PERSON><PERSON> ver<PERSON><PERSON><PERSON>", "supportedInputTypes": ["text"], "supportedOutputTypes": ["text"]}, "capabilities": {"functionCalling": true, "vision": false, "pdfSupport": false, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": false, "batchProcessing": false, "reasoning": true, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 60, "tokensPerMinute": 400000, "contextLength": 128000}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 2.0, "outputCostPer1MTokens": 6.0, "currency": "USD"}, "availability": {"regions": [{"region": "us-central1", "availability": "GA"}, {"region": "europe-west4", "availability": "GA"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI"], "platformSpecificIds": {"vertexAi": "mistral-large-2411"}}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 84.0, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-11-21", "notes": "Allgemeine Wissensbewertung"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 35.1, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 4.52, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Mistral Medium 3: 4.52±0.81 (estimated for Mistral Large 2411)"}], "metadata": {"lastUpdated": "2025-06-08T15:22:00Z", "dataSource": "Google Cloud Vertex AI Documentation", "version": "1.0"}}