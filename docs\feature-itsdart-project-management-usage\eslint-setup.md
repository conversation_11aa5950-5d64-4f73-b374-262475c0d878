# ESLint Setup Documentation

## Overview

This project uses ESLint for code quality and consistency across the Astro + React + TypeScript codebase. The configuration is designed to be practical and developer-friendly while maintaining high code standards.

## Configuration Files

### `eslint.config.mjs`
The main ESLint configuration file using the new flat config format (ESLint 9+). This replaces the legacy `.eslintrc` format.

### Key Features

1. **Multi-Framework Support**:
   - Astro files (`.astro`)
   - React/TSX components (`.tsx`)
   - TypeScript files (`.ts`)
   - JavaScript files (`.js`)

2. **Integrated Plugins**:
   - `@typescript-eslint` - TypeScript-specific rules
   - `eslint-plugin-astro` - Astro-specific linting
   - `eslint-plugin-react` - React best practices
   - `eslint-plugin-react-hooks` - React Hooks rules
   - `eslint-plugin-jsx-a11y` - Accessibility rules

3. **Smart Rule Configuration**:
   - Stricter rules for components and core logic
   - Relaxed rules for services (allows console.log for debugging)
   - Lenient rules for configuration files
   - Astro-specific adjustments

## Available Scripts

```bash
# Lint all files
npm run lint

# Lint and auto-fix issues
npm run lint:fix

# Lint with zero warnings tolerance (for CI)
npm run lint:check
```

## Rule Categories

### Errors (Must Fix)
- Unused variables and imports
- TypeScript type errors
- React key prop issues
- Accessibility violations
- Syntax errors

### Warnings (Should Fix)
- Console statements in components
- Non-null assertions
- Array index as keys
- Missing dependencies in hooks
- `any` type usage

## File-Specific Rules

### Astro Files (`*.astro`)
- Allows unused props (may be used in templates)
- Relaxed `any` type restrictions
- Disabled React-specific rules

### Services (`src/services/**`)
- Allows console statements for debugging
- Standard TypeScript rules apply

### Configuration Files
- Allows console statements
- Relaxed import rules
- No strict typing requirements

### Scripts Directory
- Very relaxed rules
- Allows console statements and `any` types
- Focuses on functionality over strict typing

## Integration with VS Code

The setup includes VS Code integration for:
- Real-time linting as you type
- Auto-fix on save
- Proper syntax highlighting for all file types

## Common Issues and Solutions

### 1. Unused Variables
**Problem**: Variables prefixed with underscore still trigger warnings
**Solution**: Use the pattern `_variableName` for intentionally unused variables

### 2. Import Order
**Problem**: Imports are not in the expected order
**Solution**: Run `npm run lint:fix` to auto-sort imports

### 3. Type Imports
**Problem**: Regular imports used for types only
**Solution**: Use `import type { Type } from 'module'` for type-only imports

### 4. Array Index Keys
**Problem**: Using array index as React keys
**Solution**: Use unique identifiers when available, or add `// eslint-disable-next-line react/no-array-index-key` for exceptions

## Customization

To modify rules, edit `eslint.config.mjs`:

```javascript
// Example: Make console warnings into errors
{
  files: ['src/components/**/*.{ts,tsx}'],
  rules: {
    'no-console': 'error', // Changed from 'warn'
  }
}
```

## CI/CD Integration

For continuous integration, use:
```bash
npm run lint:check
```

This command fails if there are any warnings or errors, ensuring code quality in production.

## Performance Notes

- The configuration excludes build outputs and generated files
- Type-aware rules are limited to source files for better performance
- Import resolution is optimized for the project structure

## Troubleshooting

### ESLint Not Working in VS Code
1. Install the ESLint VS Code extension
2. Reload VS Code window
3. Check the ESLint output panel for errors

### Performance Issues
1. Ensure `.astro` directory is in gitignore
2. Restart the ESLint server in VS Code
3. Check for conflicting ESLint configurations

### Rule Conflicts
1. Check for multiple ESLint configs in the project
2. Ensure using the flat config format
3. Verify plugin compatibility versions

## Future Improvements

Potential enhancements to consider:
- Add Prettier integration for formatting
- Include more accessibility rules
- Add custom rules for project-specific patterns
- Integrate with pre-commit hooks
- Add performance budgets for bundle size