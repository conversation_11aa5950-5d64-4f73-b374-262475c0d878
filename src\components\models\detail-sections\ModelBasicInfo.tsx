import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../../ui/card";
import type { EnrichedModelData } from "../types";
import { useTranslation, t } from "../../../contexts/TranslationContext";
import type { Translations } from "../../../contexts/TranslationContext";

// Define global window interface
declare global {
  interface Window {
    __TRANSLATIONS__?: Translations;
    __CURRENT_LANG__?: string;
  }
}

interface ModelBasicInfoProps {
  model: EnrichedModelData;
}

export function ModelBasicInfo({ model }: ModelBasicInfoProps) {
  const { translations } = useTranslation();
  const modelCard = model.modelCard;

  // Use global translations as fallback if context translations are empty
  const getTranslation = (key: string, replacements: Record<string, string | number> = {}) => {
    // Check if context translations have data
    if (translations && Object.keys(translations).length > 0) {
      return t(translations, key, replacements);
    }
    
    // Fallback to global translations if available
    if (typeof window !== 'undefined' && window.__TRANSLATIONS__) {
      return t(window.__TRANSLATIONS__ as Translations, key, replacements);
    }
    
    // Last resort - return the key itself
    return key;
  };

  return (
    <div className="space-y-6">
      {/* Grundlegende Informationen */}
      <Card>
        <CardHeader>
          <CardTitle>{getTranslation('models.detail_dialog.basic_info.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.basic_info.provider')}</h3>
              <p className="text-lg">{model.provider}</p>
            </div>
            <div>
              <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.basic_info.model_group')}</h3>
              <p className="text-lg">{modelCard?.basicInfo.modelFamily || model.modelGroup || "N/A"}</p>
            </div>
            <div>
              <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.basic_info.availability')}</h3>
              <p className={`text-lg font-medium ${model.isAvailable ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"}`}>
                {model.isAvailable ? `✅ ${getTranslation('models.detail_dialog.basic_info.available')}` : `❌ ${getTranslation('models.detail_dialog.basic_info.unavailable')}`}
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-muted-foreground">Sicherheitsstufe</h3>
              <p className="text-lg capitalize">{model.confidentiality || "N/A"}</p>
            </div>
            {modelCard && (
              <>
                <div>
                  <h3 className="font-semibold text-muted-foreground">Version</h3>
                  <p className="text-lg">{modelCard.basicInfo.version}</p>
                </div>
                <div>
                  <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.basic_info.status')}</h3>
                  <span className={`inline-block px-2 py-1 text-sm rounded ${
                    modelCard.basicInfo.status === 'GA'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : modelCard.basicInfo.status === 'Preview'
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                      : 'bg-muted text-muted-foreground'
                  }`}>
                    {modelCard.basicInfo.status}
                  </span>
                </div>
                <div>
                  <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.basic_info.release_date')}</h3>
                  <p className="text-lg">{modelCard.basicInfo.releaseDate}</p>
                </div>
                <div>
                  <h3 className="font-semibold text-muted-foreground">Knowledge Cutoff</h3>
                  <p className="text-lg">{modelCard.basicInfo.knowledgeCutoff}</p>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Beschreibung */}
      {modelCard?.basicInfo.description && (
        <Card>
          <CardHeader>
            <CardTitle>{getTranslation('models.detail_dialog.basic_info.description')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed">{modelCard.basicInfo.description}</p>
          </CardContent>
        </Card>
      )}

      {/* Model ID und interne Referenzen */}
      <Card>
        <CardHeader>
          <CardTitle>{getTranslation('models.detail_dialog.basic_info.technical_id')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.basic_info.model_id')}</h3>
              <p className="text-sm font-mono bg-muted px-2 py-1 rounded">{model.id}</p>
            </div>
            {modelCard?.basicInfo.modelId && (
              <div>
                <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.basic_info.model_card_id')}</h3>
                <p className="text-sm font-mono bg-muted px-2 py-1 rounded">{modelCard.basicInfo.modelId}</p>
              </div>
            )}
            {model.key && (
              <div>
                <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.basic_info.internal_key')}</h3>
                <p className="text-sm font-mono bg-muted px-2 py-1 rounded">{model.key}</p>
              </div>
            )}
            {model.base_model && (
              <div>
                <h3 className="font-semibold text-muted-foreground">Base Model</h3>
                <p className="text-sm">{model.base_model}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}