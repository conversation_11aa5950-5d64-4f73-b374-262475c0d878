{"site": {"title": "<PERSON><PERSON>", "description": "LLM Browser - Porównuj i oceniaj modele językowe"}, "nav": {"models": "Models", "blog": "Blog", "recommendations": "Rekomendacje", "benchmarks": "Ben<PERSON>mark<PERSON>", "api_usage": "Użycie API@iteratec"}, "footer": {"copyright": "© 2025 LLM Browser - Vise-Coding", "built_with": "Zbudowane z użyciem Astro, React, TypeScript i Tailwind CSS"}, "language_selector": {"label": "<PERSON><PERSON>bierz język", "de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "pl": "<PERSON><PERSON>"}, "language": "Język", "redirect": "Przekierowanie do przeglądu modeli...", "meta_description": "LLM Browser - Porównuj i oceniaj modele językowe", "vise_coding": "Vise-Coding", "loading": "Ładowanie...", "models": {"header": "Przeglądarka modeli LLM", "description": "Od<PERSON><PERSON><PERSON> {{count}} modeli AI z szczegółowymi informacjami o cenach, możliwościach i wynikach benchmarków. Celem jest uczynienie wyboru odpowiednich modeli do codziennej pracy bardziej interaktywnym, łatwiejszym i bardziej przejrzystym. W przeciwnym razie informacje trzeba zbierać z wielu źródeł. Na podstawie danych na żywo z iteraGPT (LiteLLM) i kart modeli generowanych przez AI z różnych źródeł.", "filters": {"all": "Wszystkie", "security": "Bezpieczeństwo", "mode": "<PERSON><PERSON>", "chat": "Cha<PERSON>", "completion": "Completion", "embedding": "Embedding", "image_generation": "Generowanie obrazów", "search_placeholder": "<PERSON><PERSON><PERSON> nazwy, dostawcy lub grupy modeli...", "of": "z", "models": "modeli"}, "stats": {"total_models": "Łączna liczba modeli", "benchmarks": "Ben<PERSON>mark<PERSON>", "average_score": "Średni wynik", "top_performer": "Najlepszy wykonawca", "filtered_count": "{{filtered}} z {{total}} modeli"}, "comparison": {"title": "Porównanie modeli ({{count}} modeli)", "reset_selection": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>ó<PERSON>", "property": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "provider": "Dostawca", "litellm_availability": "Dostępność LiteLLM", "available": "Dostępny", "model_card_only": "Tylko karta modelu", "context_window": "Okno kontekstowe", "max_output_tokens": "Maks. <PERSON> wyjściowych", "input_cost": "Koszt wejścia (za 1M tokenów)", "output_cost": "Koszt wyjścia (za 1M tokenów)", "yes": "Tak", "no": "<PERSON><PERSON>", "capabilities": "<PERSON><PERSON><PERSON><PERSON>ści", "supported_platforms": "Wspierane platformy", "metric": "Metryka", "range": "<PERSON><PERSON><PERSON>", "no_details_available": "Brak szczegółowych informacji.", "other_benchmarks": "Inne benchmarki", "at": "przy", "and": "i", "well_formed_code": "poprawnie sformatowanego kodu", "category": "Kategoria", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variants": "Warianty", "not_available": "N/D", "aider_polyglot_short": "Aider-Polyglot (o)", "website": "Strona internetowa", "paper": "Publikacja", "aider_benchmark": {"title": "Benchmark polyglot Aidera", "description": "<PERSON><PERSON><PERSON> zdolność modeli do edycji i ulepszania kodu w różnych językach programowania.", "metric": "Wskaźnik powodzenia (2. pr<PERSON>ba)", "range": "0-100%", "fallback_description": "Benchmark kodowania Aidera - mierzy zdolność modeli do edycji i ulepszania kodu."}}, "capabilities": {"vision": "<PERSON><PERSON><PERSON><PERSON>", "pdf_input": "Wejście PDF", "audio_input": "Wejście audio", "audio_output": "Wyjście audio", "embedding_image": "Osadzanie obrazów", "function_calling": "Wywoływanie funkcji", "prompt_caching": "Buforowanie promptów", "reasoning": "Rozumowanie", "system_messages": "Komunikaty systemowe"}, "table": {"pagination": {"showing": "Wyświ<PERSON>lanie {{start}} do {{end}} z {{total}} wpisów", "previous": "Poprzednia", "next": "Następna"}, "headers": {"select": "<PERSON><PERSON><PERSON><PERSON>", "security": "Bezpieczeństwo", "model_card": "Karta modelu", "litellm_status": "Status LiteLLM", "name": "Nazwa", "provider": "Dostawca", "mode": "<PERSON><PERSON>", "context": "Kontekst", "max_output": "Maks. wyjście", "input_cost_per_million": "Koszt wejścia/1M", "output_cost_per_million": "Koszt wyjścia/1M", "polyglot_score": "Wynik Polyglot", "support": "<PERSON><PERSON><PERSON><PERSON>", "details": "Szczegóły", "fullscreen": "Pełny ekran", "show_filters": "Pokaż filtry"}, "tooltips": {"confidential": "<PERSON><PERSON><PERSON>", "internal": "Wewnętrzny", "public": "Publiczny", "model_card_available": "Karta modelu dos<PERSON>ę<PERSON>", "deprecated": "Przestarzały", "shutdown_date": "Wyłączenie: {{date}}", "litellm_available": "Dostępny przez LiteLLM", "model_card_only": "Tylko karta modelu", "chat": "Cha<PERSON>", "embedding": "Osadzanie", "image": "<PERSON><PERSON><PERSON>", "vision_processing": "Widzenie/Przetwarzanie obrazów", "pdf_input": "Wejście PDF", "audio_input": "Wejście audio", "audio_output": "Wyjście audio", "embedding_image_input": "Wejście obrazów do osadzania", "function_calling": "Wywoływanie funkcji", "prompt_caching": "Buforowanie promptów", "reasoning": "Rozumowanie", "system_messages": "Komunikaty systemowe", "capability_vision": "Widzenie/Przetwarzanie obrazów", "capability_pdf_input": "Wejście PDF", "capability_audio_input": "Wejście audio", "capability_audio_output": "Wyjście audio", "capability_embedding_image_input": "Wejście obrazów do osadzania", "capability_function_calling": "Wywoływanie funkcji", "capability_prompt_caching": "Buforowanie promptów", "capability_reasoning": "Rozumowanie", "capability_system_messages": "Komunikaty systemowe"}, "empty_state": "Nie znaleziono modeli pasujących do kryteriów wyszukiwania.", "select_model": "W<PERSON>bierz model {{name}}", "details_button": "Szczegóły"}, "detail_dialog": {"tabs": {"overview": "Przegląd", "technical": "Techniczne", "capabilities": "<PERSON><PERSON><PERSON><PERSON>ści", "performance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pricing": "<PERSON><PERSON>", "benchmarks": "Ben<PERSON>mark<PERSON>", "availability": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recommendations": "Rekomendacje"}, "basic_info": {"title": "Podstawowe informacje", "provider": "Dostawca", "model_group": "Grupa modeli", "availability": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "available": "Dostępny", "unavailable": "Niedostępny", "status": "Status", "release_date": "Data wydania", "description": "Opis", "technical_id": "Identyfikacja techniczna", "model_id": "ID modelu", "model_card_id": "ID karty modelu", "internal_key": "Klucz wewnętrzny"}, "capabilities": {"core_title": "Podstawowe możliwości", "modality_title": "<PERSON><PERSON><PERSON>", "technical_title": "Możliwości techniczne", "additional_title": "Dodatkowe funkcje", "openai_params_title": "Obsługiwane parametry OpenAI", "vision": "<PERSON><PERSON><PERSON><PERSON>", "pdf_input": "Wejście PDF", "audio_input": "Wejście audio", "audio_output": "Wyjście audio", "function_calling": "Wywoływanie funkcji", "prompt_caching": "Buforowanie promptów", "reasoning": "Rozumowanie", "system_messages": "Komunikaty systemowe", "parallel_function_calling": "Równoległe wywoływanie funkcji", "response_schema": "Schemat odpowiedzi", "tool_choice": "Wybór narzędzi", "native_streaming": "<PERSON><PERSON><PERSON><PERSON> strumi<PERSON>nie", "assistant_prefill": "Wstępne wypełnienie asystenta", "embedding_image_input": "Wejście obrazów do osadzania", "multilingual_support": "Wsparcie wielojęzyczne", "image_generation": "Generowanie obrazów", "litellm_provisioning": "Provisioning LiteLLM", "recommended": "Rekomendowany"}, "technical_specs": {"title": "Specyfikacje techniczne", "token_limits": "Limity tokenów", "context_window": "Okno kontekstowe", "max_output_tokens": "Maks. <PERSON> wyjściowych", "max_input_tokens": "Maks. <PERSON> wejściowych", "max_reasoning_tokens": "Maks. <PERSON> rozumowania", "architecture_params": "Architektura i parametry", "architecture": "Architektura", "parameter_count": "Liczba parametrów", "supported_modalities": "Obsługiwane modalności", "input_types": "<PERSON><PERSON>", "output_types": "<PERSON><PERSON> w<PERSON>", "input_limitations": "Ograniczenia wejściowe", "max_images": "Maks. obrazów", "max_image_size": "Maks. roz<PERSON><PERSON> obrazu", "max_audio_length": "Maks. dług<PERSON> audio", "max_video_length": "Maks<PERSON><PERSON><PERSON><PERSON>o", "supported_mime_types": "Obsługiwane typy MIME", "legacy_api_data": "Dane legacy API", "modalities": "<PERSON><PERSON><PERSON>", "output_modalities": "Modalności wyjściowe", "vector_size": "Roz<PERSON>r wektora"}, "pricing": {"title": "<PERSON><PERSON>", "standard_pricing": "Standardowe ceny", "input_cost_per_1m": "Koszt wejścia (za 1M tokenów)", "output_cost_per_1m": "Koszt wyjścia (za 1M tokenów)", "currency": "<PERSON><PERSON><PERSON>", "caching_costs": "Koszty buforowania", "cache_hits": "Trafienia cache (za 1M tokenów)", "cache_writes": "Zapisy cache (za 1M tokenów)", "cache_hits_description": "Znacznie tańsze dla powtarzających się treści", "cache_writes_description": "Jednorazowy koszt tworzenia cache", "reasoning_costs": "Koszty rozumowania", "reasoning_tokens": "Tokeny rozumowania (za 1M)", "completion_tokens": "Tokeny zakończenia (za 1M)", "reasoning_description": "Wewnętrzne procesy <PERSON>lowe", "completion_description": "<PERSON><PERSON> <PERSON><PERSON>", "reasoning_note": "W modelach rozumowania naliczane są zarówno tokeny rozumowania, jak i zakończenia. Rzeczywiste koszty mogą się różnić w zależności od złożoności zapytania.", "batch_processing": "Przetwarzanie wsadowe (50% zniżki)", "batch_input": "Wejście (za 1M tokenów)", "batch_output": "Wyjście (za 1M tokenów)", "standard_label": "Standard", "legacy_pricing": "Ceny legacy API (za 1K tokenów)", "alternative_pricing": "Alternatywne modele cenowe", "per_character_input": "Za znak (wejście)", "per_character_output": "Za znak (wyjście)", "per_query": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "per_second_input": "<PERSON><PERSON> seku<PERSON> (wejście)", "per_second_output": "<PERSON><PERSON> seku<PERSON> (wyjście)", "per_image": "<PERSON>a obraz", "audio_pricing": "Ceny audio", "audio_input_token": "Wejście audio (za token)", "audio_output_token": "Wyjście audio (za token)", "cache_pricing_api": "Ceny cache (API)", "cache_creation": "Tworzenie cache (za token)", "cache_read": "Odczyt cache (za token)", "no_pricing_info": "Brak informacji o cenach. Sprawdź dokumentację dostawcy dla aktualnych cen."}, "benchmarks": {"title": "Ben<PERSON>mark<PERSON>", "aider_polyglot": "Benchmark Aider-Polyglot", "polyglot_score": "Wynik Polyglot", "tests_passed": "testów zaliczonych", "benchmark_details": "Szczegóły benchmarku", "test_cases": "Przypadki testowe", "pass_rate_2": "Wskaźnik powodzenia 2", "pass_rate_1": "Wskaźnik powodzenia 1", "well_formed": "<PERSON><PERSON><PERSON> s<PERSON>", "total_cost": "Całkowity koszt", "edit_format": "Format edycji", "command_used": "Użyte <PERSON>nie", "detailed_stats": "Szczegółowe statystyki", "model_card_benchmarks": "Benchmarki karty modelu", "other_benchmarks": "Inne benchmarki", "no_benchmark_data": "<PERSON>rak danych benchmarkowych", "no_benchmark_description": "Dla tego modelu nie są obecnie dostępne wyniki benchmarków.", "benchmark_info": "Informacje o benchmarkach", "aider_description": "Testuje możliwości edycji kodu w różnych językach programowania. Wynik wskazuje procent pomyślnie edytowanych zadań.", "model_card_description": "Kompleksowe oceny w ró<PERSON><PERSON><PERSON>, takich jak roz<PERSON>, mate<PERSON><PERSON><PERSON>, generowanie kodu i inne. Wyniki są skalowane różnie w zależności od typu benchmarku.", "benchmark_note": "Wyniki benchmarków mogą się różnić w zależności od warunków testowych, wersji i konfiguracji. Stanowią one wskazówkę, ale rzeczywista wydajność może się różnić w zależności od przypadku użycia."}, "availability": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "no_availability_data": "Brak danych o dostępności", "no_availability_description": "Informacje o dostępności są dostępne tylko dla modeli z kompletnymi kartami modeli.", "supported_platforms": "Wspierane platformy", "platform_specific_ids": "Identyfikatory specyficzne dla platform", "regional_availability": "Dostępność regionalna", "global_available": "Dostępne globalnie", "data_processing_regions": "Regiony przetwarzania danych", "data_processing_description": "Te regiony są używane do przetwarzania ML i obsługi danych.", "security_features": "Funkcje bezpieczeństwa", "data_residency": "<PERSON>zy<PERSON><PERSON><PERSON> da<PERSON>", "cmek_support": "Wsparcie CMEK", "vpc_support": "Wsparcie VPC", "access_transparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "available": "Dostępne", "not_available": "Niedostępne", "compliance_standards": "Standardy zgodności", "usage_types": "<PERSON><PERSON>", "dynamic_shared_quota": "Dynamiczny udział kwoty", "provisioned_throughput": "Prz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zapewniona", "fixed_quota": "Stała kwota", "additional_info": "Dodatkowe informacje", "availability_note": "Dost<PERSON><PERSON><PERSON><PERSON>ć może się zmieniać w zależności od regionu, platformy i czasu. Sprawdź aktualną dostępność u wybranego dostawcy.", "data_source": "Źródło danych", "last_updated": "Ostatnia aktualizacja"}}}, "blog": {"title": "LLM Blog", "description": "Najnowsze informacje o modelach AI, notatki o wydaniach i analizy benchmarków. Bądź na bieżąco z najnowszymi wydarzeniami w świecie LLM.", "sectionModelAnalysis": "<PERSON><PERSON><PERSON> <PERSON>i", "sectionModelAnalysisDesc": "Szczegółowe recenzje najnowszych modeli AI", "sectionReleaseNotes": "Notatki o wydaniach", "sectionReleaseNotesDesc": "Najnowsze aktualizacje i zmiany w modelach", "sectionBenchmarkAnalysis": "<PERSON><PERSON><PERSON> benchmarków", "sectionBenchmarkAnalysisDesc": "Dogłębne oceny testów wydajności", "sectionIndustryNews": "Wiadomości branżowe", "sectionIndustryNewsDesc": "Ważne wydarzenia na rynku AI", "stats": {"articles": "Artykuły", "categories": "<PERSON><PERSON><PERSON>", "featured": "Polecane posty", "tags": "Tagi"}, "featured_articles": "Polecane artykuły", "loading_articles": "Ładowanie artykułów...", "articles": "<PERSON><PERSON><PERSON>ł<PERSON>", "found": "znaleziono", "clear_all_filters": "<PERSON><PERSON><PERSON><PERSON>ść wszystkie filtry", "no_articles_found": "Nie znaleziono artykułów", "try_different_search": "Spróbuj innych słów kluczowych lub filtrów.", "no_articles_available": "Brak dostępnych artykułów na blogu.", "reset_filters": "<PERSON><PERSON><PERSON><PERSON> filtry", "previous": "Poprzedni", "next": "Następny", "back_to_blog": "Powrót do bloga", "available_in": "Dostępne w:", "available_languages": "Dostępne języki:", "reading_time": "min czytania", "related_information": "Powiązane informacje", "related_models": "Powiązane modele", "related_benchmarks": "Powiązan<PERSON> benchmarki", "similar_articles": "Podobne artykuły"}, "recommendations": {"title": "Rekomendacje dla firm", "description": "Inteligentny system rekomendacji dla {{totalModels}} modeli LLM od {{totalProviders}} dostawców. Na podstawie standardowych przypadków użycia w firmach rekomendowane są optymalne modele do różnych scenariuszy. Uwzględniana jest wydaj<PERSON>ść w benchmarkach, m<PERSON><PERSON><PERSON><PERSON>ś<PERSON>, koszty i inne czynniki dla świadomych decyzji.", "availableModels": "Dostępne modele", "providers": "<PERSON><PERSON><PERSON><PERSON>", "gaStatus": "Status GA", "avgInputCost": "Śr. k<PERSON><PERSON><PERSON>", "avgOutputCost": "Śr. kos<PERSON>t wyj<PERSON>", "perMillion": "za 1M tokenów", "pageTitle": "Rekomendacje modeli dla firm", "pageDescription": "Inteligentne rekomendacje wyboru optymalnego modelu LLM na podstawie standardowych przypadków użycia w firmach. Uwzględnia wydajność, koszty, możliwości i inne czynniki.", "topUseCases": "Najlepsze przypadki użycia:", "benchmarksUsed": "Użyte benchmarki:", "requiredCapabilities": "Wymagane możliwości:", "category": "Kategoria:", "priority": {"high": "<PERSON><PERSON><PERSON>", "medium": "Średni", "low": "<PERSON><PERSON>", "label": "Priorytet"}, "topRecommendations": "Najlepsze rekomendacje:", "costEffectiveness": {"high": "Opłacalne", "medium": "Standardowe", "low": "<PERSON><PERSON>ie"}, "qualityCheck": "<PERSON><PERSON><PERSON><PERSON>", "stats": {"useCases": "Przypadki użycia", "models": "<PERSON>e", "recommendations": "Rekomendacje", "excellent": "Doskonałe", "avgPerUseCase": "Śr. na przypadek"}, "tabs": {"overview": "Przegląd", "topModels": "Najlepsze modele", "highPriority": "Krytyczne przypadki użycia", "allUseCases": "Wszystkie przypadki użycia"}, "sections": {"bestOverallModels": "Najlepsze modele ogólne", "bestOverallDescription": "Modele z najlepszą średnią wydajnością we wszystkich przypadkach użycia.", "criticalUseCases": "Krytyczne przypadki użycia", "criticalDescription": "Rekomendacje dla biznesowo krytycznych przypadków użycia o wysokim priorytecie.", "allUseCasesTitle": "Wszystkie przypadki użycia", "allUseCasesDescription": "Szczegółowe rekomendacje dla każdego standardowego przypadku użycia."}}, "benchmark": {"title": "Wyniki benchmarków", "description": "Szczegółowa analiza wyników benchmarku Polyglot dla {{totalBenchmarks}} testów benchmarkowych.", "testedModels": "Przetestowane modele", "averageScore": "Średni wynik", "highestScore": "Najwyższy wynik", "testCases": "Przypadki testowe", "about": "O benchmarku Polyglot:", "aboutText1": "Ten benchmark opiera się na zadaniach programistycznych Exercism i testuje zdolność modeli językowych do rozwiązywania złożonych problemów programistycznych w 6 różnych językach:", "languages": "C++, Go, Java, JavaScript, Python i Rust", "aboutText2": "Benchmark obejmuje {{hardest}} najtrudniejszych zadań z łącznie {{total}} dostępnych problemów Exercism i został zaprojektowany tak, aby był znacznie trudniejszy niż wcześniejsze benchmarki. Wyniki opierają się na liczbie pomyślnie rozwiązanych problemów i zapewniają precyzyjną ocenę możliwości edycji kodu przez nowoczesne LLM."}, "qc": {"title": "Porównanie benchmarków modeli LLM", "header": "Porównanie benchmarków", "description": "Szczegółowa analiza benchmarków dla {{modelCount}} modeli LLM. Porównaj wydajność różnych modeli w {{benchmarkCount}} różnych benchmarkach. Ten przegląd umożliwia bezpośrednie porównanie rzeczywistych wartości benchmarków z kart modeli.", "availableBenchmarks": "<PERSON><PERSON><PERSON><PERSON><PERSON> benchmarki", "avgBenchmarksPerModel": "Śr. benchmarków/model", "mostCommonBenchmark": "Na<PERSON><PERSON><PERSON><PERSON><PERSON> benchmark", "modelsWithBenchmarks": "Modele z benchmarkami", "topBenchmarks": "Top 5 benchmarków (wg dostępności)", "models": "<PERSON>e"}, "components": {"collapsible_header": {"show_info": "Pokaż informacje", "hide_info": "<PERSON><PERSON><PERSON><PERSON>"}}, "calculation_methodology": {"title": "Metodologia obliczeń", "dialog_title": "Metodologia obliczeń rekomendacji", "dialog_description": "Szczegółowe wyjaśnienie inteligentnego systemu rekomendacji i używanych algorytmów", "scoring_overview": "Przegląd algorytmu punktacji", "scoring_description": "Nasz inteligentny system rekomendacji ocenia każdy model dla każdego przypadku użycia za pomocą systemu 100-punktowego. Całkowity wynik składa się z pięciu ważonych czynników:", "benchmark_performance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> benchmarków", "required_capabilities": "Wymagane możliwości", "cost_efficiency": "Efekt<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>ztowa", "latency_speed": "Opóźnienie/Szybkość", "availability": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "benchmark_calculation": "<PERSON><PERSON><PERSON><PERSON>ść benchmarków (45% wagi)", "benchmark_description": "Wyniki benchmarków są inteligentnie normalizowane i ważone specyficznie dla przypadków użycia:", "critical_coding_benchmarks": "Krytyczne benchmarki kodowania (1.5x waga):", "score_normalization": "Normalizacja wyników:", "arena_scores": "Wyniki Arena: 1300=60pkt, 1350=70pkt, 1400=85pkt, 1450+=95pkt", "elo_ratings": "Oceny ELO: 1200=30pkt, 1800=70pkt, 2400+=90pkt", "standard_benchmarks": "Standardowe benchmarki: Bezpośrednie przyjęcie 0-100%", "other_factors": "Inne czynniki oceny", "capabilities_score": "<PERSON><PERSON><PERSON><PERSON>ś<PERSON> (25%)", "capabilities_description": "Proporcjonalnie: (spełnione możliwości / wymagane możli<PERSON>ści) × 100", "cost_score": "Wynik kosztów (10%)", "budget_range": "Budżet (0-1$): 85-95 punktów", "standard_range": "Standard (1-5$): 70-85 punktów", "premium_range": "Premium (5$+): 50-70 punktów", "latency_score": "Wynik opóźnienia (15%)", "fastest": "Najszybszy: 100pkt", "fast": "Szybki: 85pkt", "moderately_fast": "Umiarkowanie szybki: 70pkt", "slow_slowest": "Wolny/Najwolniejszy: 50/30pkt", "context_window_score": "Wynik okna kontekstowego", "availability_score": "<PERSON><PERSON><PERSON> (5%)", "ga_score": "GA: 100pkt", "preview_score": "Podgląd: 80pkt", "other_score": "Inne: 60pkt", "quality_factors": "Czynniki jakości i optymalizacje kodowania (2025)", "benchmark_penalties": "<PERSON><PERSON> benchmarkowe", "penalty_description": "-5 punktów za benchmark z <40% w kategoriach Faktyczność/Wiedza", "multimodal_bonus": "Bonus multimodalny", "multimodal_description": "+5 punktów za możliwości Vision w przypadkach użycia Analiza danych/Dokumentacja", "coding_adjustments": "Specjalne dostosowania kodowania", "coding_use_cases": "Przypadki użycia kodowania: +10% dodatkowej wagi benchmarków", "context_bonus": "Bonus kontekstu: +3 punkty za >500k/200k, +2 punkty za >128k okno kontekstowe", "affected_use_cases": "Dotknięte przypadki użycia: generowanie kodu, prz<PERSON><PERSON><PERSON><PERSON> kodu, debugowanie, refaktoryzacja, testowanie, integracja API, automatyzacja DevOps", "final_calculation": "Końcowe obliczenie wyniku", "total_score_formula": "Całkowity wynik = (<PERSON><PERSON><PERSON> × 45%) + (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> × 25%) + (<PERSON><PERSON><PERSON> × 10%) + (<PERSON><PERSON><PERSON><PERSON><PERSON> × 15%) + (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> × 5%) + Bonus multimodalny + Dostosowania kodowania - <PERSON><PERSON> benchmarkowe", "use_case_mappings": "Mapowania benchmarków przypadków użycia", "mappings_description": "Każdy przypadek użycia używa określonych benchmarków do oceny. Najważniejsze benchmarki kodu są priorytetowe:", "code_generation": "Generowanie/przegląd/debugowanie/refaktoryzacja/testowanie kodu:", "api_integration": "Integracja API:", "devops_automation": "Automatyzacja DevOps:", "data_analysis": "<PERSON><PERSON><PERSON>:", "learning_documentation": "Nauka/Dokumentacja:", "rating_scale": "Skala ocen i kategoryzacja", "suitability_categories": "Kategorie przydatności:", "excellent": "Doskonały", "good": "<PERSON><PERSON><PERSON>", "acceptable": "Akceptowalny", "limited": "Ograniczony", "recommendation_categories": "<PERSON><PERSON><PERSON>:", "recommended": "Rekomendowany", "alternative": "Alternatywny", "not_recommended": "Nierekomendowany", "cost_effectiveness": "Efekt<PERSON><PERSON><PERSON><PERSON><PERSON> kosztowa:", "high_cost_eff": "Wysoka: <PERSON><PERSON><PERSON> + <PERSON><PERSON><PERSON> >65", "medium_cost_eff": "Średnia: Standard + Wynik >70", "low_cost_eff": "Niska: Modele premium", "disclaimer_title": "Ważna uwaga", "disclaimer_text": "Te rekomendacje są oparte na obliczeniach algorytmicznych i służą jako wskazówka. W przypadku aplikacji produkcyjnych zawsze przeprowadzaj własne testy i oceny. Ceny i dostępność mogą się zmieniać. Dane na dzień:"}, "use_case_dashboard": {"title": "Dashboard rekomendacji przypadków użycia", "description": "Przegląd najlepszych rekomendacji modeli dla standardowych przypadków użycia w przedsiębiorstwach. Oparte na wydajności benchmarków, możliwościach, kosztach i innych czynnikach.", "use_cases": "Przypadki użycia", "available_models": "Dostępne modele", "total_recommendations": "Łączne rekomendacje", "avg_recommendations": "Śr. re<PERSON><PERSON><PERSON><PERSON><PERSON>/przypad<PERSON> uży<PERSON>", "search_placeholder": "Szukaj przypadków użycia...", "select_category": "<PERSON><PERSON><PERSON><PERSON>", "all_categories": "Wszystkie kategorie", "sort_by_name": "Według nazwy", "sort_by_recommended": "Według rekomendacji", "recommended_count": "rekomendowane", "cost_effective": "Opłacalne", "standard_cost": "Standardowe", "expensive": "<PERSON><PERSON>ie", "no_suitable_models": "Nie znaleziono odpowiednich modeli", "additional_models": "dodatkowe modele dostępne", "no_use_cases_found": "Nie znaleziono przypadków użycia", "try_different_search": "Spróbuj innych terminów wyszukiwania lub filtrów."}}