---
title: "LLM Browser: More Transparency and Comparability"
excerpt: "Originated from a Vise-Coding demo for _NEXT25, the idea was further developed within the FreiDay framework to provide more transparency, overview and comparability regarding current developments in AI models"
category: "model-analysis"
tags: ["launch", "model-comparison", "benchmarks", "astro", "react", "performance"]
publishDate: "2025-06-11T09:00:00Z"
lastUpdated: "2025-06-11T09:00:00Z"
author:
  name: "LLM Browser Development Team"
  role: "Product & Engineering"
readingTime: 8
featured: true
relatedModelIds: ["o3", "claude-opus-4", "gemini-2.5-pro", "deepseek-v3", "deepseek-r1"]
releaseVersion: "0.1"

# i18n-specific fields
lang: "en"
translationKey: "llm-browser-release-initversion-2025"
availableLanguages: ["de", "en", "pl"]
changelog:
  - type: "removed"
    description: "Aider Polyglot benchmarks are no longer highlighted as much and therefore removed from navigation"
    impact: "patch"
    technicalDetails: "They are still accessible via the Aider Polyglot benchmark links or directly via /benchmark"
  - type: "added"
    description: "Complete model comparison platform with all current relevant LLM models from various hosting providers"
    impact: "major"
    technicalDetails: "AI-supported model card generation (not yet 100% automated)"
  - type: "added"
    description: "Consolidated benchmark collection in a mix of availability and added value"
    impact: "major"
    technicalDetails: "Aider-Polyglot Leaderboard, MMLU, MATH, Code Generation, Visual Reasoning and more"
  - type: "added"
    description: "Recommendation system for model selection in various scenarios in a 1st iteration"
    impact: "major"
    technicalDetails: "Use-case-based recommendations with cost-benefit analysis, however still limitations due to data basis that still needs to be verified"
  - type: "added"
    description: "Introduction of model cards with information categories per model"
    impact: "major"
    technicalDetails: "General, Technical, Modalities, Benchmarks, Capabilities, Prices, Availability"
  - type: "added"
    description: "Quality check system for data validation and consistency via the benchmark page"
    impact: "minor"
    technicalDetails: "Automated quality checking with visual representation of statistics"
metaDescription: "The new LLM Browser - comprehensive platform for analysis and comparison of 38 AI models with over 55 benchmark categories"
metaKeywords: ["LLM Browser", "AI Models", "Benchmarks", "Model Comparison", "Performance Analysis"]
featuredImage: "/images/blog/2025-06-1st-release.png"
---

The LLM Browser continues to be a FreiDay project to learn working with Vise-Coding while simultaneously generating added value in the use, planning and verification of AI models at iteratec.

**The LLM Browser is currently unofficial!** The goal is for this setup in this or reduced form to be taken over by the ExG-R&D team.

## The Motivation

The idea was to get a better overview of the rapidly developing LLM landscape. With the current setup, **20 different models from 7 leading providers** are now available combined with model cards and basic information. From OpenAI's latest o3 and o4-Mini models to Anthropic's Claude Opus 4 to Google's Gemini 2.5 Pro and the impressive DeepSeek models - all major players are represented.

The core challenge today is that the data basis, especially for benchmarks on the models, is extremely scattered and inhomogeneous, making simple comparison hardly possible. The harmonization with a data basis thus also creates the prerequisites for building concrete recommendations for AI models in various use cases. Data-driven, not felt.

## Data Foundations

The information shown is based on LiteLLM data regarding costs and supported features, i.e., the LLM proxy behind api.iteragpt.com.

The model cards and benchmark values, on the other hand, are AI-guided and manually curated.

Sometimes benchmarks are ONLY maintained on special pages, sometimes also only provided as image materials in comparison and currently practically never really easily accessible.

The model cards are created AI-supported and manually curated. A fully automatic way would be conceivable, but the effort would not currently cover the benefit.

A schematic workflow for the o3-pro model card:

```
Create a new model card for "o3-pro" under src/data/models/o3-pro.json with the basic structure based on the schema under @/src/data/models/model-card-json-schema.md. Pay attention to the exact spelling of the benchmarks from @/src/data/benchmarks/benchmark-descriptions.json! Otherwise matching is not possible! Also make sure that the costs are correctly adopted according to the schema!

**Step 1:**
Model card basic info:
- @https://platform.openai.com/docs/models/o3-pro
- @https://openai.com/index/introducing-o3-and-o4-mini/

Use as reference example: 
<example>
@/src/data/models/claude-sonnet-4.json 
</example>

**Step 2:**
Check if additional benchmark data can be updated.

- Benchmark: "AIME": @https://www.vals.ai/benchmarks/aime-2025-05-30 
- Benchmark: "MMMU": @https://www.vals.ai/benchmarks/mmmu-05-30-2025 
- Benchmark: "SWE-bench Verified": @https://www.swebench.com/index.html 
- Benchmark: "Terminal-Bench": @https://www.tbench.ai/leaderboard 
- Benchmark: "Webdev-Arena": @https://web.lmarena.ai/leaderboard 
- Benchmark: "GPQA-Diamond": @https://www.vellum.ai/llm-leaderboard 
- Benchmark: "LiveCodeBench v2025": @https://livecodebench.github.io/leaderboard.html 
- Benchmark: "Humanity's Last Exam": @https://scale.com/leaderboard/humanitys_last_exam_text_only

Pay attention to the exact spelling of the benchmarks from @/src/data/benchmarks/benchmark-descriptions.json! Otherwise matching is not possible!

**Step 3:**
Then update the mapping file @/src/data/models/model-mappings.json based on the existing @/src/data/models/model-ids-reference.md.

**Step 4:**
Also update the Polyglot benchmarks accordingly @/src/data/benchmarks/benchmark-descriptions.json based on @/src/data/polyglot_benchmarks.json in @/src/data/models/grok-v3.json. Make sure you find and write the following values:

- "Aider-Polyglot-Wellformated"
- "Aider-Polyglot"
```

## Benchmark Spectrum

The following benchmarks are integrated and available with further details:

### Reasoning & Knowledge
- **MMLU** (Massive Multitask Language Understanding)
- **DROP** (Discrete Reasoning Over Paragraphs)
- **BIG-Bench-Hard** for complex thinking tasks
- **Humanity's Last Exam** - the ultimate knowledge test

### Code Excellence
- **Aider-Polyglot Leaderboard** - our flagship for code editing
- **LiveCodeBench v2025** for code generation
- **SWE-bench Verified** for software engineering
- **Terminal-bench** and **TAU-bench** for agentic coding

### Mathematics & Science
- **MATH** for mathematical problem solving
- **AIME 2024/2025** for advanced mathematics
- **GPQA Diamond** for scientific expertise

### Multimodal Capabilities
- **MathVista** for visual mathematical understanding
- **CharXiv-Reasoning** for diagram interpretation
- **MMMU** for multimodal understanding

## Recommendations for Every Use Case

The **recommendation system** should help analyze specific requirements and suggests optimal models.

The recommendations are composed of different areas that are weighted to give an overall score. The relevant benchmarks and capabilities differ depending on the use case, e.g., code editing or documentation.

![Scoring System Overview](/images/blog/2025-06-scoring.png)

Currently, the data basis is still under construction and not final.
The current recommendations nevertheless match those from the ExG at the current time.

## Further Development

If you have wishes for further development, just let us know!

**Useful Links:**
- **[GitLab Repository](https://gitlab.com/iteratec/llm-browser)** - Source Code