import type { ModelData, BenchmarkData, StaticModelData, StaticBenchmarkData } from "@/types/api";


// Hilfsfunktion zur Generierung einer modelid aus dem Modellnamen
export function extractModelId(modelName: string): string {
  // Entferne Klammern und deren Inhalt
  let cleanName = modelName.replace(/\s*\([^)]*\)/g, '');
  
  // Konvertiere zu Kleinbuchstaben und ersetze Leerzeichen durch Bindestriche
  cleanName = cleanName.toLowerCase().trim().replace(/\s+/g, '-');
  
  // Ersetze Punkte durch Bindestriche (für Versionsnummern wie 3.5)
  cleanName = cleanName.replace(/\./g, '-');
  
  // Entferne spezielle Zeichen außer Bindestrichen und Unterstrichen
  cleanName = cleanName.replace(/[^\w-]/g, '');
  
  return cleanName;
}

// Process benchmark data to ensure all entries have a modelid
export function processBenchmarkData(data: BenchmarkData[]): BenchmarkData[] {
  return data.map((item: Partial<BenchmarkData> & { model: string }) => {
    // Wenn modelid bereits vorhanden ist, verwende diese
    if (item.modelid) {
      return item as BenchmarkData;
    }
    
    // Ansonsten extrahiere eine modelid aus dem Modellnamen
    const generatedModelId = extractModelId(item.model);
    console.log(`[BUILD] Generated modelid "${generatedModelId}" for model "${item.model}"`);
    
    return {
      ...item,
      modelid: generatedModelId
    } as BenchmarkData;
  });
}

// Transform model info from API response to standardized format
export function transformModelInfo(rawModelData: any[]): ModelData[] {
  return rawModelData.map((item: Record<string, unknown>) => {
    // Extract model_info if it exists
    const modelInfo = (item.model_info || {}) as Record<string, unknown>;
    
    // Ensure name and key are the same to avoid duplicates
    const modelName = (item.model_name as string) || (modelInfo.key as string) || "Unknown Model";
    
    // Create a ModelData object with proper ID and map ALL support properties
    const transformedModel: ModelData = {
      id: (modelInfo.id as string) || modelName || `model-${Math.random().toString(36).substring(2, 9)}`,
      name: modelName,
      provider: (modelInfo.litellm_provider as string) || "Unknown",
      description: (modelInfo.description as string) || "",
      capabilities: (modelInfo.capabilities as string[]) || [],
      modelGroup: (modelInfo.model_group as string) || "",
      isAvailable: modelInfo.isAvailable !== false,
      confidentiality: (modelInfo.confidentiality as string) || "",
      mode: (modelInfo.mode as string) || "unknown",
      maxTokens: (modelInfo.max_tokens as number) || null,
      maxInputTokens: (modelInfo.max_input_tokens as number) || null,
      maxOutputTokens: (modelInfo.max_output_tokens as number) || null,
      inputCostPerToken: (modelInfo.input_cost_per_token as number) || null,
      outputCostPerToken: (modelInfo.output_cost_per_token as number) || null,
      
      // Map ALL support properties from snake_case to camelCase
      supportsFunctionCalling: (modelInfo.supports_function_calling as boolean) ?? null,
      supportsParallelFunctionCalling: (modelInfo.supports_parallel_function_calling as boolean) ?? null,
      supportsResponseSchema: (modelInfo.supports_response_schema as boolean) ?? null,
      supportsVision: (modelInfo.supports_vision as boolean) ?? null,
      supportsPromptCaching: (modelInfo.supports_prompt_caching as boolean) ?? null,
      supportsSystemMessages: (modelInfo.supports_system_messages as boolean) ?? null,
      supportsToolChoice: (modelInfo.supports_tool_choice as boolean) ?? null,
      supportsWebSearch: (modelInfo.supports_web_search as boolean) ?? null,
      supportsNativeStreaming: (modelInfo.supports_native_streaming as boolean) ?? null,
      supportsPdfInput: (modelInfo.supports_pdf_input as boolean) ?? null,
      supportsAudioInput: (modelInfo.supports_audio_input as boolean) ?? null,
      supportsAudioOutput: (modelInfo.supports_audio_output as boolean) ?? null,
      supportsAssistantPrefill: (modelInfo.supports_assistant_prefill as boolean) ?? null,
      supportsEmbeddingImageInput: (modelInfo.supports_embedding_image_input as boolean) ?? null,
      supportsReasoning: (modelInfo.supports_reasoning as boolean) ?? null,
      
      // Map additional properties
      supportedEndpoints: (modelInfo.supported_endpoints as string[]) || undefined,
      supportedModalities: (modelInfo.supported_modalities as string[]) || undefined,
      supportedOutputModalities: (modelInfo.supported_output_modalities as string[]) || undefined,
      supported_openai_params: (modelInfo.supported_openai_params as string[]) || null,
      
      // Cache and batch costs
      cache_read_input_token_cost: (modelInfo.cache_read_input_token_cost as number) || null,
      cache_creation_input_token_cost: (modelInfo.cache_creation_input_token_cost as number) || null,
      input_cost_per_token_batches: (modelInfo.input_cost_per_token_batches as number) || null,
      output_cost_per_token_batches: (modelInfo.output_cost_per_token_batches as number) || null,
      
      // Rate limits
      tpm: (modelInfo.tpm as number) || null,
      rpm: (modelInfo.rpm as number) || null,
      
      key: modelName, // Ensure key is the same as name
      recommendation: (modelInfo.recommendation as boolean) || false,
      db_model: (modelInfo.db_model as boolean) || false,
      base_model: (modelInfo.base_model as string) || "",
      contextWindow: (modelInfo.context_window as number) || (modelInfo.max_input_tokens as number) || (modelInfo.max_tokens as number) || null,
      
      // Convert cost data from per-token to per-1k-tokens for consistency
      inputCostPer1kTokens: (modelInfo.input_cost_per_token as number) ? (modelInfo.input_cost_per_token as number) * 1000 : undefined,
      outputCostPer1kTokens: (modelInfo.output_cost_per_token as number) ? (modelInfo.output_cost_per_token as number) * 1000 : undefined,
    };

    return transformedModel;
  });
}

// Ensure all models have consistent properties
export function normalizeModelData(models: ModelData[]): ModelData[] {
  return models.map(model => {
    // Ensure name and key are the same
    const modelName = model.name || model.key || model.id;
    
    // Ensure ID exists
    const modelId = model.id || modelName || `model-${Date.now()}-${Math.random().toString(36).substring(2, 5)}`;
    
    return {
      ...model,
      id: modelId,
      name: modelName,
      key: modelName,
      
      // Ensure context window data consistency
      contextWindow: model.contextWindow || model.maxInputTokens || model.maxTokens,
      maxTokens: model.maxTokens || model.maxOutputTokens,
      maxInputTokens: model.maxInputTokens,
      maxOutputTokens: model.maxOutputTokens,
      
      // Normalize support properties (prefer snake_case as primary)
      supportsFunctionCalling: model.supports_function_calling ?? model.supportsFunctionCalling,
      supportsParallelFunctionCalling: model.supports_parallel_function_calling ?? model.supportsParallelFunctionCalling,
      supportsResponseSchema: model.supports_response_schema ?? model.supportsResponseSchema,
      supportsVision: model.supports_vision ?? model.supportsVision,
      supportsPromptCaching: model.supports_prompt_caching ?? model.supportsPromptCaching,
      supportsSystemMessages: model.supports_system_messages ?? model.supportsSystemMessages,
      supportsToolChoice: model.supports_tool_choice ?? model.supportsToolChoice,
      supportsWebSearch: model.supports_web_search ?? model.supportsWebSearch,
      supportsNativeStreaming: model.supports_native_streaming ?? model.supportsNativeStreaming,
      supportsPdfInput: model.supports_pdf_input ?? model.supportsPdfInput,
      supportsAudioInput: model.supports_audio_input ?? model.supportsAudioInput,
      supportsAudioOutput: model.supports_audio_output ?? model.supportsAudioOutput,
      supportsAssistantPrefill: model.supports_assistant_prefill ?? model.supportsAssistantPrefill,
      supportsEmbeddingImageInput: model.supports_embedding_image_input ?? model.supportsEmbeddingImageInput,
      supportsReasoning: model.supports_reasoning ?? model.supportsReasoning,
      
      // Normalize endpoint and modality data
      supportedEndpoints: model.supported_endpoints ?? model.supportedEndpoints,
      supportedModalities: model.supported_modalities ?? model.supportedModalities,
      supportedOutputModalities: model.supported_output_modalities ?? model.supportedOutputModalities,
    };
  });
}

// Create static data structure for build output
export function createStaticModelData(models: ModelData[], source: 'api' | 'mock' | 'cache' = 'api'): StaticModelData {
  return {
    models,
    lastUpdated: new Date().toISOString(),
    source
  };
}

// Create static benchmark data structure
export function createStaticBenchmarkData(benchmarks: BenchmarkData[], version: string = 'latest'): StaticBenchmarkData {
  return {
    benchmarks,
    lastUpdated: new Date().toISOString(),
    version
  };
}

// Filter models by various criteria
export function filterModels(models: ModelData[], filters: {
  provider?: string;
  hasVision?: boolean;
  hasFunctionCalling?: boolean;
  hasContextWindow?: boolean;
  isAvailable?: boolean;
  minContextWindow?: number;
  maxCostPerToken?: number;
}) {
  return models.filter(model => {
    if (filters.provider && model.provider !== filters.provider) return false;
    if (filters.hasVision !== undefined && model.supportsVision !== filters.hasVision) return false;
    if (filters.hasFunctionCalling !== undefined && model.supportsFunctionCalling !== filters.hasFunctionCalling) return false;
    if (filters.hasContextWindow !== undefined) {
      const hasContext = Boolean(model.contextWindow && model.contextWindow > 0);
      if (hasContext !== filters.hasContextWindow) return false;
    }
    if (filters.isAvailable !== undefined && model.isAvailable !== filters.isAvailable) return false;
    if (filters.minContextWindow && (!model.contextWindow || model.contextWindow < filters.minContextWindow)) return false;
    if (filters.maxCostPerToken && model.inputCostPerToken && model.inputCostPerToken > filters.maxCostPerToken) return false;
    
    return true;
  });
}

// Sort models by various criteria
export function sortModels(models: ModelData[], sortBy: 'name' | 'provider' | 'contextWindow' | 'cost' | 'passRate' = 'name', direction: 'asc' | 'desc' = 'asc') {
  const sorted = [...models].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'provider':
        comparison = a.provider.localeCompare(b.provider);
        break;
      case 'contextWindow': {
        const aContext = a.contextWindow || 0;
        const bContext = b.contextWindow || 0;
        comparison = aContext - bContext;
        break;
      }
      case 'cost': {
        const aCost = a.inputCostPerToken || 0;
        const bCost = b.inputCostPerToken || 0;
        comparison = aCost - bCost;
        break;
      }
      case 'passRate': {
        const aRate = a.benchmarkData?.pass_rate_2 || 0;
        const bRate = b.benchmarkData?.pass_rate_2 || 0;
        comparison = aRate - bRate;
        break;
      }
    }
    
    return direction === 'desc' ? -comparison : comparison;
  });
  
  return sorted;
}

// Group models by provider
export function groupModelsByProvider(models: ModelData[]): Record<string, ModelData[]> {
  return models.reduce((groups, model) => {
    const provider = model.provider || 'Unknown';
    if (!groups[provider]) {
      groups[provider] = [];
    }
    groups[provider].push(model);
    return groups;
  }, {} as Record<string, ModelData[]>);
}

// Generate summary statistics for models
export function generateModelStatistics(models: ModelData[]) {
  const providers = new Set(models.map(m => m.provider));
  const modelsWithVision = models.filter(m => m.supportsVision);
  const modelsWithFunctionCalling = models.filter(m => m.supportsFunctionCalling);
  const modelsWithBenchmarks = models.filter(m => m.benchmarkData);
  
  const contextWindows = models
    .map(m => m.contextWindow)
    .filter(cw => cw && cw > 0) as number[];
  
  const costs = models
    .map(m => m.inputCostPerToken)
    .filter(cost => cost && cost > 0) as number[];

  return {
    totalModels: models.length,
    totalProviders: providers.size,
    providers: Array.from(providers),
    modelsWithVision: modelsWithVision.length,
    modelsWithFunctionCalling: modelsWithFunctionCalling.length,
    modelsWithBenchmarks: modelsWithBenchmarks.length,
    contextWindowStats: contextWindows.length > 0 ? {
      min: Math.min(...contextWindows),
      max: Math.max(...contextWindows),
      average: Math.round(contextWindows.reduce((sum, cw) => sum + cw, 0) / contextWindows.length)
    } : null,
    costStats: costs.length > 0 ? {
      min: Math.min(...costs),
      max: Math.max(...costs),
      average: costs.reduce((sum, cost) => sum + cost, 0) / costs.length
    } : null
  };
}