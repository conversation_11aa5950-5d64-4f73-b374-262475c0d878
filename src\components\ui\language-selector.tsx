"use client"

import * as React from "react"
import { Languages } from "lucide-react"
import { useTranslation } from "@/contexts/TranslationContext"

import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

const languages = [
  { code: "de", name: "<PERSON><PERSON><PERSON>", flag: "🇩🇪" },
  { code: "en", name: "English", flag: "🇺🇸" },
  { code: "pl", name: "<PERSON><PERSON>", flag: "🇵🇱" },
]

export function LanguageSelector() {
  const { currentLang, changeLanguage } = useTranslation()
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  const handleLanguageChange = async (newLang: string) => {
    try {
      await changeLanguage(newLang)
      // changeLanguage already handles the reload
    } catch (error) {
      console.error("Failed to change language:", error)
    }
  }

  if (!mounted) {
    return (
      <Button variant="ghost" size="sm" className="w-9 px-0">
        <Languages className="h-[1.2rem] w-[1.2rem]" />
        <span className="sr-only">Sprache wählen</span>
      </Button>
    )
  }

  const currentLanguage = languages.find(lang => lang.code === currentLang) || languages[0]

  return (
    <Select value={currentLang} onValueChange={handleLanguageChange}>
      <SelectTrigger className="w-9 h-9 p-0 border-0 bg-transparent hover:bg-accent hover:text-accent-foreground">
        <SelectValue>
          <span className="text-base" title={currentLanguage.name}>
            {currentLanguage.flag}
          </span>
        </SelectValue>
        <span className="sr-only">Sprache wählen: {currentLanguage.name}</span>
      </SelectTrigger>
      <SelectContent align="end">
        {languages.map((language) => (
          <SelectItem key={language.code} value={language.code}>
            <div className="flex items-center gap-2">
              <span className="text-base">{language.flag}</span>
              <span>{language.name}</span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}