# GitLab CI/CD Pipeline für Astro Static Site
image: node:18

stages:
  - build
  - deploy

variables:
  NODE_ENV: production

cache:
  paths:
    - node_modules/

before_script:
  - npm ci

# Build Stage
build:
  stage: build
  script:
    - npm run build         # Astro Build (prebuild wird automatisch ausgeführt)
  artifacts:
    paths:
      - public/
    expire_in: 1 hour
  only:
    - main

# Deploy to GitLab Pages
# Hinweis: Astro baut jetzt direkt in das public/ Verzeichnis (outDir: 'public')
# GitLab Pages erwartet die Dateien im public/ Verzeichnis - kein Kopieren nötig
pages:
  stage: deploy
  script:
    - echo "Astro build output is already in public/ directory"
  artifacts:
    paths:
      - public
  dependencies:
    - build
  only:
    - main