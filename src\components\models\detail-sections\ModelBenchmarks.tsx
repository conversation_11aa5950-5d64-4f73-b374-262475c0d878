import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../../ui/card";
import { TrendingUp, Award, BarChart3, AlertCircle, InfoIcon } from "lucide-react";
import type { EnrichedModelData } from "../types";
import type { ModelCardBenchmark } from "../../../types/model-cards";
import { useTranslation, t } from "../../../contexts/TranslationContext";
import type { Translations } from "../../../contexts/TranslationContext";

// Define global window interface
declare global {
  interface Window {
    __TRANSLATIONS__?: Translations;
    __CURRENT_LANG__?: string;
  }
}

interface ModelBenchmarksProps {
  model: EnrichedModelData;
}

interface BenchmarkCardProps {
  benchmark: ModelCardBenchmark;
  index: number;
}

function BenchmarkCard({ benchmark, index }: BenchmarkCardProps) {
  // Get performance color based on score
  const getPerformanceColor = (score: number) => {
    if (score >= 80) return "text-green-600 dark:text-green-400 font-bold";
    if (score >= 65) return "text-blue-600 dark:text-blue-400 font-semibold";
    if (score >= 50) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  // Get category color
  const getCategoryColor = (category: string) => {
    const colors = {
      'Reasoning & Knowledge': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
      'Science': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      'Mathematics': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      'Code generation': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
      'Code editing': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
      'Agentic coding': 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400',
      'Factuality': 'bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400',
      'Visual reasoning': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400',
      'Image understanding': 'bg-violet-100 text-violet-800 dark:bg-violet-900/30 dark:text-violet-400',
      'Long context': 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-400',
      'Multilingual performance': 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400'
    };
    return colors[category as keyof typeof colors] || 'bg-muted text-muted-foreground';
  };

  // Determine if percentage should be shown
  const isPercentageMetric = benchmark.metric === 'Pass Rate' || 
                           benchmark.metric === 'Accuracy' || 
                           benchmark.metric === 'Success Rate';

  return (
    <Card key={index} className="border-l-4 border-l-blue-500">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1">
            <h4 className="font-semibold text-foreground mb-2">{benchmark.benchmarkName}</h4>
            <div className="flex gap-2 mb-2">
              <span className={`text-xs px-2 py-1 rounded-full ${getCategoryColor(benchmark.category)}`}>
                {benchmark.category}
              </span>
              {benchmark.attemptType && (
                <span className="text-xs px-2 py-1 rounded-full bg-muted text-muted-foreground">
                  {benchmark.attemptType}
                </span>
              )}
              {benchmark.toolsUsed && (
                <span className="text-xs px-2 py-1 rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                  Tools Used
                </span>
              )}
            </div>
          </div>
          <div className="text-right ml-4">
            <span className={`text-2xl font-bold ${getPerformanceColor(benchmark.score)}`}>
              {benchmark.score.toFixed(1)}{isPercentageMetric ? '%' : ''}
            </span>
            <p className="text-xs text-muted-foreground">{benchmark.metric}</p>
          </div>
        </div>

        <div className="space-y-2 text-sm text-muted-foreground">
          {benchmark.date && (
            <div className="flex justify-between">
              <span className="font-medium">Datum:</span>
              <span>{benchmark.date}</span>
            </div>
          )}
          {benchmark.contextLength && (
            <div className="flex justify-between">
              <span className="font-medium">Kontext-Länge:</span>
              <span>{benchmark.contextLength}</span>
            </div>
          )}
          {benchmark.notes && (
            <div>
              <span className="font-medium">Notizen:</span>
              <p className="mt-1 text-muted-foreground">{benchmark.notes}</p>
            </div>
          )}
        </div>

        {/* Alternative Scores */}
        {benchmark.alternativeScores && Object.keys(benchmark.alternativeScores).length > 0 && (
          <div className="mt-3 pt-3 border-t">
            <h5 className="text-xs font-semibold text-muted-foreground mb-2">ALTERNATIVE SCORES</h5>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(benchmark.alternativeScores).map(([key, value]) => (
                <div key={key} className="flex justify-between text-xs">
                  <span className="text-muted-foreground capitalize">{key}:</span>
                  <span className="font-medium">
                    {value.toFixed(1)}{isPercentageMetric ? '%' : ''}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export function ModelBenchmarks({ model }: ModelBenchmarksProps) {
  const { translations } = useTranslation();
  const hasPolyglotData = !!model.benchmarkData;
  const hasModelCardBenchmarks = !!(model.modelCard?.benchmarks && model.modelCard.benchmarks.length > 0);

  // Use global translations as fallback if context translations are empty
  const getTranslation = (key: string, replacements: Record<string, string | number> = {}) => {
    // Check if context translations have data
    if (translations && Object.keys(translations).length > 0) {
      return t(translations, key, replacements);
    }
    
    // Fallback to global translations if available
    if (typeof window !== 'undefined' && window.__TRANSLATIONS__) {
      return t(window.__TRANSLATIONS__ as Translations, key, replacements);
    }
    
    // Last resort - return the key itself
    return key;
  };

  // Group benchmarks by category
  const groupedBenchmarks = React.useMemo(() => {
    if (!hasModelCardBenchmarks) return {};
    
    return model.modelCard?.benchmarks?.reduce((groups, benchmark) => {
      const category = benchmark.category;
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(benchmark);
      return groups;
    }, {} as Record<string, ModelCardBenchmark[]>);
  }, [model.modelCard, hasModelCardBenchmarks]);

  const categoryOrder = [
    'Reasoning & Knowledge',
    'Science', 
    'Mathematics',
    'Code generation',
    'Code editing',
    'Agentic coding',
    'Factuality',
    'Visual reasoning',
    'Image understanding',
    'Long context',
    'Multilingual performance'
  ];

  return (
    <div className="space-y-6">
      {/* Polyglot Benchmark */}
      {hasPolyglotData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-blue-600" />
              {getTranslation('models.detail_dialog.benchmarks.aider_polyglot')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="text-center">
                <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-6">
                  <TrendingUp className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-muted-foreground mb-1">{getTranslation('models.detail_dialog.benchmarks.polyglot_score')}</h3>
                  <p className="text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                    {model.benchmarkData?.pass_rate_2?.toFixed(1) ?? 'N/A'}%
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {model.benchmarkData?.details?.pass_num_2 ?? 'N/A'}/{model.benchmarkData?.details?.test_cases || 225} {getTranslation('models.detail_dialog.benchmarks.tests_passed')}
                  </p>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.benchmarks.benchmark_details')}</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">{getTranslation('models.detail_dialog.benchmarks.test_cases')}:</span>
                    <span className="font-medium">{model.benchmarkData?.details?.test_cases || 225}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">{getTranslation('models.detail_dialog.benchmarks.pass_rate_2')}:</span>
                    <span className="font-medium">{model.benchmarkData?.pass_rate_2?.toFixed(1) ?? 'N/A'}%</span>
                  </div>
                  {model.benchmarkData?.details?.pass_rate_1 && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">{getTranslation('models.detail_dialog.benchmarks.pass_rate_1')}:</span>
                      <span className="font-medium">{model.benchmarkData?.details?.pass_rate_1?.toFixed(1) ?? 'N/A'}%</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">{getTranslation('models.detail_dialog.benchmarks.well_formed')}:</span>
                    <span className="font-medium">{model.benchmarkData?.percent_cases_well_formed?.toFixed(1) ?? 'N/A'}%</span>
                  </div>
                  {model.benchmarkData?.total_cost && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">{getTranslation('models.detail_dialog.benchmarks.total_cost')}:</span>
                      <span className="font-medium">${model.benchmarkData?.total_cost?.toFixed(2) ?? 'N/A'}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">{getTranslation('models.detail_dialog.benchmarks.edit_format')}:</span>
                    <span className="font-medium">{model.benchmarkData?.edit_format || "N/A"}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Command Details */}
            <div className="mt-6">
              <h3 className="font-semibold text-muted-foreground mb-3">{getTranslation('models.detail_dialog.benchmarks.command_used')}</h3>
              <code className="block bg-muted p-3 rounded text-sm overflow-x-auto">
                {model.benchmarkData?.command ?? 'N/A'}
              </code>
            </div>

            {/* Additional Details */}
            {model.benchmarkData?.details && Object.keys(model.benchmarkData.details).length > 2 && (
              <div className="mt-6">
                <h3 className="font-semibold text-muted-foreground mb-3">{getTranslation('models.detail_dialog.benchmarks.detailed_stats')}</h3>
                <div className="bg-muted p-4 rounded">
                  <pre className="text-sm overflow-x-auto whitespace-pre-wrap">
                    {JSON.stringify(model.benchmarkData?.details ?? {}, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Model Card Benchmarks */}
      {hasModelCardBenchmarks && (
        <div className="space-y-6">
          <div className="flex items-center gap-2 mb-4">
            <Award className="w-5 h-5 text-yellow-600" />
            <h2 className="text-xl font-semibold">{getTranslation('models.detail_dialog.benchmarks.model_card_benchmarks')}</h2>
          </div>

          {categoryOrder.map(category => {
            const benchmarks = groupedBenchmarks?.[category];
            if (!benchmarks || benchmarks.length === 0) return null;

            return (
              <div key={category}>
                <h3 className="font-semibold text-lg text-foreground mb-3 flex items-center gap-2">
                  <span className={`w-3 h-3 rounded-full bg-blue-500`}></span>
                  {category} ({benchmarks.length})
                </h3>
                <div className="grid gap-4">
                  {benchmarks
                    .sort((a, b) => b.score - a.score)
                    .map((benchmark, index) => (
                      <BenchmarkCard key={`${category}-${benchmark.benchmarkName}-${benchmark.score}`} benchmark={benchmark} index={index} />
                    ))}
                </div>
              </div>
            );
          })}

          {/* Uncategorized benchmarks */}
          {groupedBenchmarks && Object.keys(groupedBenchmarks).some(cat => !categoryOrder.includes(cat)) && (
            <div>
              <h3 className="font-semibold text-lg text-foreground mb-3">{getTranslation('models.detail_dialog.benchmarks.other_benchmarks')}</h3>
              <div className="grid gap-4">
                {groupedBenchmarks && Object.entries(groupedBenchmarks)
                  .filter(([category]) => !categoryOrder.includes(category))
                  .flatMap(([, benchmarks]) => benchmarks)
                  .sort((a, b) => b.score - a.score)
                  .map((benchmark, index) => (
                    <BenchmarkCard key={`other-${benchmark.benchmarkName}-${benchmark.score}`} benchmark={benchmark} index={index} />
                  ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* No benchmark data available */}
      {!hasPolyglotData && !hasModelCardBenchmarks && (
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">{getTranslation('models.detail_dialog.benchmarks.no_benchmark_data')}</h3>
              <p className="text-muted-foreground">
                {getTranslation('models.detail_dialog.benchmarks.no_benchmark_description')}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Benchmark Information */}
      {(hasPolyglotData || hasModelCardBenchmarks) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <InfoIcon className="w-5 h-5 text-blue-600" />
              {getTranslation('models.detail_dialog.benchmarks.benchmark_info')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-muted-foreground">
              {hasPolyglotData && (
                <p>
                  <strong>Aider-Polyglot Benchmark:</strong> {getTranslation('models.detail_dialog.benchmarks.aider_description')}
                </p>
              )}
              {hasModelCardBenchmarks && (
                <p>
                  <strong>Model Card Benchmarks:</strong> {getTranslation('models.detail_dialog.benchmarks.model_card_description')}
                </p>
              )}
              <p>
                <strong>Hinweis:</strong> {getTranslation('models.detail_dialog.benchmarks.benchmark_note')}
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}