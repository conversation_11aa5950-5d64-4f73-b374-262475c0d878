import type { ModelData, BenchmarkData } from "@/types/api";

// Hilfsfunktion zur Normalisierung von Modellnamen für besseres Matching
function normalizeModelName(name: string): string {
  // Entferne Datumsangaben (z.B. 20250219)
  let normalized = name.replace(/[-_]?\d{8}$/g, '');
  
  // Ersetze Punkte durch Bindestriche (z.B. 3.7 -> 3-7)
  normalized = normalized.replace(/\./g, '-');
  
  // Entferne trailing Versionen und Varianten
  normalized = normalized.replace(/-preview.*$/g, '');
  
  return normalized.toLowerCase().trim();
}

// Extract base model name without provider prefix
function extractBaseModelName(fullName: string): string {
  const parts = fullName.split('/');
  return parts.length > 1 ? parts[parts.length - 1] : fullName;
}

// Hilfsfunktion um zu prüfen, ob ein Benchmark ein Kombinationsmodell ist
function isCombinationModel(benchmarkModel: string): boolean {
  return benchmarkModel.includes(' + ') || benchmarkModel.includes(' & ') || benchmarkModel.includes(' and ');
}

// Function to match model data with benchmark data
export function matchModelWithBenchmark(model: ModelData, benchmarkData: BenchmarkData[]): BenchmarkData | null {
  if (!benchmarkData || benchmarkData.length === 0) {
    console.log("[BUILD] No benchmark data available for matching");
    return null;
  }
  
  // Normalisiere IDs für besseres Matching
  const normalizeId = (id: string): string => {
    return id.toLowerCase().trim();
  };
  
  const modelId = model.id?.toLowerCase() || '';
  const modelName = model.name?.toLowerCase() || '';
  const baseModelName = extractBaseModelName(modelName);
  
  // Check if this is a model card with mapping info
  const mappingInfo = (model as { mappingInfo?: { modelCardId?: string; [key: string]: unknown } }).mappingInfo;
  const modelCardId = mappingInfo?.modelCardId?.toLowerCase() || '';
  
  console.log(`[BUILD] Matching model: "${model.name}" (id: ${model.id}), baseModelName: "${baseModelName}", modelCardId: "${modelCardId}"`);
  
  // Filtere zuerst nicht-Kombinationsmodelle für bessere Matches
  const singleModels = benchmarkData.filter(b => !isCombinationModel(b.model));
  const _combinationModels = benchmarkData.filter(b => isCombinationModel(b.model));
  
  // Priorität 1: Exakter Match über modelID in Einzelmodellen
  let match = singleModels.find(benchmark => {
    const normalizedBenchmarkId = normalizeId(benchmark.modelid);
    const normalizedModelId = normalizeId(modelId);
    const normalizedModelCardId = normalizeId(modelCardId);
    
    const exactMatch = normalizedBenchmarkId === normalizedModelId ||
                      (modelCardId && normalizedBenchmarkId === normalizedModelCardId);
    
    if (exactMatch) {
      console.log(`[BUILD] Found exact modelID match: "${benchmark.model}" (id: ${benchmark.modelid})`);
    }
    
    return exactMatch;
  });
  
  // Priorität 1.5: Match über Model Card ID mit partieller Übereinstimmung
  if (!match && modelCardId) {
    // Extrahiere Basis-Namen aus Model Card ID (z.B. "claude-3-7-sonnet" aus "claude-3-7-sonnet@20250219")
    const baseCardId = modelCardId.split('@')[0].split('-preview')[0];
    
    match = singleModels.find(benchmark => {
      const benchmarkId = benchmark.modelid?.toLowerCase() || '';
      const benchmarkModel = benchmark.model?.toLowerCase() || '';
      
      // Überprüfe ob der Basis Model Card ID im Benchmark ID oder Model enthalten ist
      const cardIdMatch = (baseCardId.length > 5) &&
                         (benchmarkId.includes(baseCardId) || benchmarkModel.includes(baseCardId));
      
      if (cardIdMatch) {
        console.log(`[BUILD] Found Model Card ID match: "${benchmark.model}" (baseCardId: "${baseCardId}" matches "${benchmarkId}")`);
      }
      
      return cardIdMatch;
    });
  }
  
  // Priorität 2: Exakter Match über Modellnamen in Einzelmodellen
  if (!match) {
    const normalizedModelName = normalizeModelName(modelName);
    const normalizedBaseModelName = normalizeModelName(baseModelName);
    
    match = singleModels.find(benchmark => {
      const normalizedBenchmarkName = normalizeModelName(benchmark.model);
      
      const exactMatch =
        normalizedBenchmarkName === normalizedModelName ||
        normalizedBenchmarkName === normalizedBaseModelName;
      
      if (exactMatch) {
        console.log(`[BUILD] Found exact model name match: "${benchmark.model}" (normalized: "${normalizedBenchmarkName}" vs "${normalizedModelName}")`);
      }
      
      return exactMatch;
    });
  }
  
  // Priorität 3: Partielle Matches in Einzelmodellen (nur wenn notwendig)
  if (!match) {
    const normalizedModelName = normalizeModelName(modelName);
    const normalizedBaseModelName = normalizeModelName(baseModelName);
    
    match = singleModels.find(benchmark => {
      const normalizedBenchmarkName = normalizeModelName(benchmark.model);
      
      // Nur partielle Matches, die sinnvoll sind (vermeidet falsche Matches)
      const partialMatch =
        (normalizedModelName.length > 3 && normalizedBenchmarkName.includes(normalizedModelName)) ||
        (normalizedBaseModelName.length > 3 && normalizedBenchmarkName.includes(normalizedBaseModelName));
      
      if (partialMatch) {
        console.log(`[BUILD] Found partial model name match: "${benchmark.model}" (normalized: "${normalizedBenchmarkName}" contains "${normalizedModelName}")`);
      }
      
      return partialMatch;
    });
  }
  
  // Priorität 4: Als letzter Ausweg, erweiterte Matching-Logik mit Priorisierung von Einzelmodellen
  if (!match) {
    const normalizedModelName = normalizeModelName(modelName);
    const normalizedBaseModelName = normalizeModelName(baseModelName);
    
    // Sammle alle möglichen Matches mit einer Bewertung
    const possibleMatches = benchmarkData.map(benchmark => {
      const benchmarkModelName = benchmark.model.toLowerCase();
      const benchmarkModelId = benchmark.modelid?.toLowerCase() || '';
      const normalizedBenchmarkName = normalizeModelName(benchmarkModelName);
      
      let score = 0;
      let matchReason = '';
      
      // Starke Abwertung für Kombinationsmodelle
      if (isCombinationModel(benchmark.model)) {
        score -= 50;
      }
      
      // ID-Matches (höchste Priorität)
      if (benchmarkModelId && modelId) {
        if (benchmarkModelId === modelId) {
          score += 100;
          matchReason = 'exact ID match';
        } else if (modelId.length > 3 && benchmarkModelId.includes(modelId)) {
          score += 30;
          matchReason = 'benchmark ID contains model ID';
        } else if (benchmarkModelId.length > 3 && modelId.includes(benchmarkModelId)) {
          score += 25;
          matchReason = 'model ID contains benchmark ID';
        }
      }
      
      // Model Card ID Matches (sehr hohe Priorität)
      if (benchmarkModelId && modelCardId) {
        const baseCardId = modelCardId.split('@')[0].split('-preview')[0];
        
        if (benchmarkModelId === modelCardId) {
          score += 100;
          matchReason = 'exact Model Card ID match';
        } else if (benchmarkModelId === baseCardId) {
          score += 95;
          matchReason = 'exact base Model Card ID match';
        } else if (baseCardId.length > 5 && benchmarkModelId.includes(baseCardId)) {
          score += 60;
          matchReason = 'benchmark ID contains Model Card base ID';
        } else if (baseCardId.length > 5 && benchmarkModelName.includes(baseCardId)) {
          score += 55;
          matchReason = 'benchmark model contains Model Card base ID';
        }
      }
      
      // Name-Matches (zweite Priorität)
      if (normalizedBenchmarkName === normalizedModelName) {
        score += 90;
        matchReason = matchReason || 'exact normalized name match';
      } else if (normalizedBenchmarkName === normalizedBaseModelName) {
        score += 80;
        matchReason = matchReason || 'exact normalized base name match';
      } else if (normalizedModelName.length > 3 && normalizedBenchmarkName.includes(normalizedModelName)) {
        score += 20;
        matchReason = matchReason || 'normalized benchmark name contains model name';
      } else if (normalizedBaseModelName.length > 3 && normalizedBenchmarkName.includes(normalizedBaseModelName)) {
        score += 15;
        matchReason = matchReason || 'normalized benchmark name contains base name';
      }
      
      // Fallback auf nicht-normalisierte Namen (niedrigste Priorität)
      else if (benchmarkModelName === modelName.toLowerCase()) {
        score += 70;
        matchReason = matchReason || 'exact lowercase name match';
      } else if (benchmarkModelName === baseModelName.toLowerCase()) {
        score += 60;
        matchReason = matchReason || 'exact lowercase base name match';
      }
      
      return { benchmark, score, matchReason };
    });
    
    // Sortiere nach Score und wähle den besten Match (nur positive Scores)
    const sortedMatches = possibleMatches
      .filter(m => m.score > 0)
      .sort((a, b) => b.score - a.score);
    
    if (sortedMatches.length > 0) {
      const bestMatch = sortedMatches[0];
      console.log(`[BUILD] Fallback match for "${model.name}": "${bestMatch.benchmark.model}" (score: ${bestMatch.score}, reason: ${bestMatch.matchReason})`);
      match = bestMatch.benchmark;
      
      // Log alternative Matches für Debugging
      if (sortedMatches.length > 1) {
        console.log(`[BUILD] Alternative fallback matches for "${model.name}":`);
        sortedMatches.slice(1, 3).forEach(m => {
          console.log(`[BUILD] - "${m.benchmark.model}" (score: ${m.score}, reason: ${m.matchReason})`);
        });
      }
    }
  }
  
  if (match) {
    console.log(`[BUILD] Final match for "${model.name}": "${match.model}" with score ${match.pass_rate_2.toFixed(1)}%`);
  } else {
    console.log(`[BUILD] No match found for "${model.name}"`);
  }
  
  return match || null;
}

// Function to enrich model data with benchmark data
export function enrichModelDataWithBenchmark(models: ModelData[], benchmarkData: BenchmarkData[]): ModelData[] {
  console.log(`[BUILD] Enriching ${models.length} models with ${benchmarkData.length} benchmark entries`);
  
  const enrichedModels = models.map(model => {
    const benchmarkMatch = matchModelWithBenchmark(model, benchmarkData);
    if (benchmarkMatch) {
      console.log(`[BUILD] Found benchmark match for model "${model.name}" (id: ${model.id}): "${benchmarkMatch.model}" (id: ${benchmarkMatch.modelid})`);
      return {
        ...model,
        benchmarkData: benchmarkMatch
      };
    } else {
      console.log(`[BUILD] No benchmark match found for model "${model.name}" (id: ${model.id})`);
    }
    return model;
  });
  
  const modelsWithBenchmarkData = enrichedModels.filter(model => model.benchmarkData);
  console.log(`[BUILD] ${modelsWithBenchmarkData.length} models enriched with benchmark data`);
  
  return enrichedModels;
}

// Function to find models without benchmark data
export function findModelsWithoutBenchmarks(models: ModelData[]): ModelData[] {
  return models.filter(model => !model.benchmarkData);
}

// Function to get benchmark statistics
export function getBenchmarkStatistics(benchmarkData: BenchmarkData[]) {
  if (!benchmarkData || benchmarkData.length === 0) {
    return {
      totalBenchmarks: 0,
      averagePassRate: 0,
      topPerformers: [],
      combinationModels: 0,
      singleModels: 0
    };
  }

  const singleModels = benchmarkData.filter(b => !isCombinationModel(b.model));
  const combinationModels = benchmarkData.filter(b => isCombinationModel(b.model));
  
  const passRates = benchmarkData.map(b => b.pass_rate_2).filter(rate => rate > 0);
  const averagePassRate = passRates.length > 0 
    ? passRates.reduce((sum, rate) => sum + rate, 0) / passRates.length 
    : 0;

  const topPerformers = [...benchmarkData]
    .sort((a, b) => b.pass_rate_2 - a.pass_rate_2)
    .slice(0, 10);

  return {
    totalBenchmarks: benchmarkData.length,
    averagePassRate: Math.round(averagePassRate * 100) / 100,
    topPerformers,
    combinationModels: combinationModels.length,
    singleModels: singleModels.length
  };
}