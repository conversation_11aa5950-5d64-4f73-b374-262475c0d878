---
title: "LLM Browser: Mehr Transparenz und Vergleichbarkeit"
excerpt: "Entstanden aus einer Vise-Coding-Demo für die _NEXT25 ist die Idee im FreiDay-Rahm<PERSON> weiterentwickelt, um mehr Transparenz, Übersicht und Vergleichbarkeit hinsichtlich aktueller Entwicklungen zu den KI-Modellen zur Verfügung zu stellen"
category: "model-analysis"
tags: ["launch", "model-comparison", "benchmarks", "astro", "react", "performance"]
publishDate: "2025-06-11T09:00:00Z"
lastUpdated: "2025-06-11T09:00:00Z"
author:
  name: "LLM Browser Entwicklungsteam"
  role: "Product & Engineering"
readingTime: 8
featured: true
relatedModelIds: ["o3", "claude-opus-4", "gemini-2.5-pro", "deepseek-v3", "deepseek-r1"]
releaseVersion: "0.1"

# i18n-spezifische Felder
lang: "de"
translationKey: "llm-browser-release-initversion-2025"
availableLanguages: ["de", "en", "pl"]
changelog:
  - type: "removed"
    description: "Aider Polyglot-Benchmarks werden nicht mehr so stark in den Fokus gerückt und daher aus der Navigation genommen"
    impact: "patch"
    technicalDetails: "Sie sind weiterhin über den Aider Polyglot-Benchmark über die Links erreichbar bzw. direkt über /benchmark"
  - type: "added"
    description: "Vollständige Model-Vergleichsplattform mit allen aktuellen relevanten LLM-Modellen von  verschiedenen Hosting-Anbietern"
    impact: "major"
    technicalDetails: "KI gestützte Model-Card-Generierung (noch nicht zu 100% automatisiert)"
  - type: "added"
    description: "Konsolidierte Benchmark-Sammlung in einer Mischung von Verfügbarkeit und Mehrwert"
    impact: "major"
    technicalDetails: "Aider-Polyglot Leaderboard, MMLU, MATH, Code Generation, Visual Reasoning und mehr"
  - type: "added"
    description: "Empfehlungssystem für Modell-Auswahl in verschiedenen Szenarien in einer 1. Iteration"
    impact: "major"
    technicalDetails: "Use-Case-basierte Recommendations mit Kosten-Nutzen-Analyse, allerdings noch Einschränkungen aufgrund noch zu verifizierender Datengrundlage"
  - type: "added"
    description: "Einführung von Model-Cards mit  Informationskategorien pro Modell"
    impact: "major"
    technicalDetails: "Allgemein, Technik, Modalitäten, Benchmarks, Fähigkeiten, Preise, Verfügbarkeit"
  - type: "added"
    description: "Quality Check System für Datenvalidierung und -konsistenz über die Benchmark-Seite"
    impact: "minor"
    technicalDetails: "Automatisierte Qualitätsprüfung mit visueller Darstellung von Statistiken"
metaDescription: "Der neue LLM Browser - umfassende Plattform für die Analyse und den Vergleich von 38 KI-Modellen mit über 55 Benchmark-Kategorien"
metaKeywords: ["LLM Browser", "KI-Modelle", "Benchmarks", "Model Comparison", "Performance Analysis"]
featuredImage: "/images/blog/2025-06-1st-release.png"
---

Der LLM-Browser ist weiterhin ein FreiDay-Projekt, um zu lernen mit Vise-Coding zu arbeiten und gleichzeitig einen Mehrwert zu generieren in der Nutzung, Planung und Verifikation der KI-Modelle bei iteratec.

**Der LLM Browser ist vorerst inoffiziell!** Ziel ist es, das das Setup in dieser oder reduzierten Form durch das ExG-R&D-Team übernommen wird. 

## Die Motivation

Die Idee war es einen besseren Überblick über die sich rasant entwickelnde LLM-Landschaft zu erhalten. Mit dem aktuellen Setup stehen damit **20 verschiedene Modellen von 7 führenden Anbietern** vereint mit Model-Cards und grundlegenden Informationen zur Verfügung. Von OpenAIs neuesten o3 und o4-Mini Modellen über Anthropics Claude Opus 4 bis hin zu Googles Gemini 2.5 Pro und den beeindruckenden DeepSeek-Modellen - alle wichtigen Player sind vertreten.

Kernherausforderung ist heute, dass die Datenbasis insb. zu Benchmarks zu den Modellen extrem verstreut und inhomogen zur Verfügung stehen, so dass ein einfacher Vergleich kaum möglich ist. Die Harmonisierung mit einer Datenbasis schafft so auch die Voraussetzungen, konkrete Empfehlungen für KI-Modell in verschiedenen Use-Cases aufzubauen. Datengetrieben, nicht gefühlt.

## Datengrundlagen

Die gezeigten Informationen basieren bgzl. Kosten und Supported-Features auf den LiteLLM-Daten d.h. dem LLM-Proxy der hinter api.iteragpt.com steht.

Die Model-Cards und Benchmark-Werte sind dagegen KI geführt und manuell kuratiert zusammengetragen.

Teilweise werden Benchmarks hier NUR auf speziellen Seiten geführt, teilweise auch nur als Bild-Materialien im Vergleich bereitgestellt und aktuell praktisch nie wirklich einfach abgreifbar.

Die Model-Cards werden KI-gestützt erstellt und manuell kuratiert. Ein vollautomatischer Weg wäre denkbar, der Aufwand würde den Nutzen aktuell aber nicht abdecken.

Ein schematischer 
Workflow für die o3-pro Model-Card:

```
Erstelle eine neue Modelkarte für "o3-pro" unter src/data/models/o3-pro.json mit der Grundstruktur auf Basis des Schemas unter @/src/data/models/model-card-json-schema.md . Achte auf die exakte Schreibweise der Benchmarks aus @/src/data/benchmarks/benchmark-descriptions.json! Andernfalls ist ein Matching nicht möglich! Achte zudem darauf das entsprechend Schema die Kosten korrekt übernommen werden!


**Step 1:**
Model-Card-Basis-Infos:
- @https://platform.openai.com/docs/models/o3-pro
- @https://openai.com/index/introducing-o3-and-o4-mini/


Nutze als Referenzbeispiel: 
<example>
@/src/data/models/claude-sonnet-4.json 
</example>

**Step 2:**
Prüfe ob weitere Benchmarkdaten aktualisiert werden können. 

- Benchmark: "AIME": @https://www.vals.ai/benchmarks/aime-2025-05-30 
- Benchmark: "MMMU": @https://www.vals.ai/benchmarks/mmmu-05-30-2025 
- Benchmark: "SWE-bench Verified": @https://www.swebench.com/index.html 
- Benchmark: "Terminal-Bench": @https://www.tbench.ai/leaderboard 
- Benchmark: "Webdev-Arena": @https://web.lmarena.ai/leaderboard 
- Benchmark: "GPQA-Diamond": @https://www.vellum.ai/llm-leaderboard 
- Benchmark: "LiveCodeBench v2025": @https://livecodebench.github.io/leaderboard.html 
- Benchmark: "Humanity's Last Exam": @https://scale.com/leaderboard/humanitys_last_exam_text_only

Achte auf die exakte Schreibweise der Benchmarks aus @/src/data/benchmarks/benchmark-descriptions.json! Andernfalls ist ein Matching nicht möglich!

**Step 3:**
Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md .

**Step 4:**
Aktualisiere auch die Polyglot-Benchmarks entsprechend @/src/data/benchmarks/benchmark-descriptions.json auf Basis von @/src/data/polyglot_benchmarks.json in @/src/data/models/grok-v3.json  . Achte darauf dass folgende WErte findest und schreibst:

- "Aider-Polyglot-Wellformated"
- "Aider-Polyglot"
```

## Benchmark-Spektrum

Folgende Benchmarks sind integriert und stehen mit weiteren Details zur Verfügung:

### Reasoning & Wissen
- **MMLU** (Massive Multitask Language Understanding)
- **DROP** (Discrete Reasoning Over Paragraphs)
- **BIG-Bench-Hard** für komplexe Denkaufgaben
- **Humanity's Last Exam** - der ultimative Wissenstest

### Code-Exzellenz
- **Aider-Polyglot Leaderboard** - unser Flaggschiff für Code-Editing
- **LiveCodeBench v2025** für Code Generation
- **SWE-bench Verified** für Software Engineering
- **Terminal-bench** und **TAU-bench** für agentic coding

### Mathematik & Wissenschaft
- **MATH** für mathematische Problemlösung
- **AIME 2024/2025** für fortgeschrittene Mathematik
- **GPQA Diamond** für wissenschaftliche Expertise

### Multimodale Fähigkeiten
- **MathVista** für visuelles mathematisches Verständnis
- **CharXiv-Reasoning** für Diagramm-Interpretation
- **MMMU** für multimodales Verstehen

## Empfehlungen für jeden Use-Case

Das **Empfehlungssystem** soll spezifische Anforderungen analyisieren helfen und schlägt die optimalen Modelle vor. 

Dabei setzen sich die Empfehlungen aus verschiedenen Bereichen zusammen die gewichtet dann einen Gesamt-Score ergeben. Die jeweils relevanten Benchmarks und Capabilities unterscheiden sich dabei je nach Anwendungsfall z.B. Code-Editing oder Dokumentation.

![Scoring-System Übersicht](/images/blog/2025-06-scoring.png)

Aktuell ist die Datenbasis noch im Aufbau und nicht final. 
Die aktuellen Empfehlungen decken sich dennoch mit denen aus der ExG zum jetzigen Zeitpunkt.


## Weiterentwicklung

Ihr habt Wünsche zur Weiterentwicklung, einfach melden!

**Nützliche Links:**
- **[GitLab Repository](https://gitlab.com/iteratec/llm-browser)** - Source Code
