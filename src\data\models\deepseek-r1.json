{"basicInfo": {"modelId": "deepseek-r1-0528", "displayName": "DeepSeek-R1", "provider": "DeepSeek", "modelFamily": "DeepSeek", "version": "0528", "description": "Ein fortschrittliches Reasoning-Modell mit verbesserter Denktiefe und Inferenz-Fähigkeiten, das mit führenden Modellen wie O3 und Gemini 2.5 Pro konkurriert", "releaseDate": "2025-01-20", "status": "GA", "knowledgeCutoff": "Oktober 2024"}, "technicalSpecs": {"contextWindow": 65536, "maxOutputTokens": 65536, "architecture": "MoE", "parameterCount": "671B", "supportedInputTypes": ["text"], "supportedOutputTypes": ["text"]}, "capabilities": {"functionCalling": true, "vision": false, "pdfSupport": false, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": false, "promptCaching": true, "batchProcessing": false, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Slow", "reasoningPerformance": {"averageReasoningTime": "Variable, a<PERSON><PERSON><PERSON><PERSON><PERSON> von Komplexität", "reasoningEfficiency": "High"}, "temperature": {"min": 0, "max": 2, "default": 0.6}, "topP": 0.95}, "pricing": {"inputCostPer1MTokens": 0.55, "outputCostPer1MTokens": 2.19, "cachingCosts": {"cacheHits": 0.14}, "currency": "USD"}, "availability": {"supportedPlatforms": ["DeepSeek API"]}, "security": {"complianceStandards": ["MIT License"]}, "benchmarks": [{"benchmarkName": "GPQA Diamond", "category": "Science", "score": 71.5, "metric": "Pass@1", "attemptType": "pass@1", "date": "2025-01-20"}, {"benchmarkName": "SimpleQA", "category": "Factuality", "score": 30.1, "metric": "Correct", "attemptType": "single attempt", "date": "2025-01-20"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 8.5, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "DeepSeek-R1 score not in the current leaderboard data, keeping previous value"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 62.8, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 49.2, "metric": "Resolved", "attemptType": "single attempt", "date": "2025-01-20"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 56.9, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-20", "notes": "Aider polyglot benchmark with diff edit format"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 96.9, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-01-20", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 79.8, "metric": "Pass@1", "attemptType": "pass@1", "date": "2025-01-20"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 70.0, "metric": "Pass@1", "attemptType": "pass@1", "date": "2025-01-20"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 97.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-15", "notes": "MATH 500 benchmark"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 74.0, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "AIME 2024 and 2025 combined benchmark from vals.ai"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 5.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-15", "notes": "Terminus agent framework, ±0.7% confidence interval"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1200.0, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #8"}], "metadata": {"lastUpdated": "2025-06-09T18:22:00Z", "dataSource": "DeepSeek API Documentation, vals.ai AIME Benchmark, Terminal-Bench Leaderboard, MATH 500 Benchmark Update", "version": "1.3"}}