---
import Layout from "../../layouts/Layout.astro";
import { RecommendationsPageWrapper } from "../../components/recommendations/RecommendationsPageWrapper";
import CollapsibleHeaderIsland from "../../components/models/CollapsibleHeaderIsland";
import type { ModelCardsData } from "../../types/model-cards";
import * as fs from "node:fs/promises";
import * as path from "node:path";

// Client-side i18n only - no server-side translation loading

// Statische Daten zur Build-Zeit laden (GitLab Pages kompatibel)
// Verwende absoluten Pfad basierend auf dem Projekt-Root
const projectRoot = path.resolve(process.cwd());
const dataPath = path.join(projectRoot, "src", "data");

const modelCardsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "model-cards.json"), "utf-8")
) as ModelCardsData;

// Extrahiere nur die Model Cards
const modelCards = modelCardsData.modelCards;

// Berechne Statistiken
const totalModels = modelCards.length;
const providersSet = new Set(modelCards.map((card) => card.basicInfo.provider));
const totalProviders = providersSet.size;

// Zähle verfügbare Modelle (GA Status)
const availableModels = modelCards.filter(
  (card) => card.basicInfo.status === "GA"
).length;

// Berechne durchschnittliche Kosten
const avgInputCost =
  modelCards.reduce((sum, card) => sum + card.pricing.inputCostPer1MTokens, 0) /
  totalModels;
const avgOutputCost =
  modelCards.reduce(
    (sum, card) => sum + card.pricing.outputCostPer1MTokens,
    0
  ) / totalModels;
---

<Layout title="Model Recommendations - LLM Browser">
  <main class="container mx-auto px-4 py-8">
    <!-- Kollabierbare Header-Komponente -->
    <CollapsibleHeaderIsland
      titleKey="recommendations.title"
      descriptionKey="recommendations.description"
      totalModels={totalModels}
      totalProviders={totalProviders}
      client:load
    >
      <!-- Statistik-Übersicht -->
      <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="recommendations.availableModels">
            Verfügbare Modelle
          </h3>
          <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">
            {totalModels}
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="recommendations.providers">
            Anbieter
          </h3>
          <p class="text-3xl font-bold text-green-600 dark:text-green-400">
            {totalProviders}
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="recommendations.gaStatus">
            GA Status
          </h3>
          <p class="text-3xl font-bold text-purple-600 dark:text-purple-400">
            {availableModels}
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="recommendations.avgInputCost">
            ⌀ Input Kosten
          </h3>
          <p class="text-2xl font-bold text-orange-600 dark:text-orange-400">
            ${avgInputCost.toFixed(2)}
          </p>
          <p class="text-xs text-muted-foreground" data-translate="recommendations.perMillion">
            pro 1M Tokens
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="recommendations.avgOutputCost">
            ⌀ Output Kosten
          </h3>
          <p class="text-2xl font-bold text-red-600 dark:text-red-400">
            ${avgOutputCost.toFixed(2)}
          </p>
          <p class="text-xs text-muted-foreground" data-translate="recommendations.perMillion">
            pro 1M Tokens
          </p>
        </div>
      </div>
    </CollapsibleHeaderIsland>

    <!-- React Island für die Empfehlungs-Seite -->
    <RecommendationsPageWrapper modelCards={modelCards} client:load />
  </main>
</Layout>

<style>
  .container {
    max-width: 1400px;
  }
</style>
