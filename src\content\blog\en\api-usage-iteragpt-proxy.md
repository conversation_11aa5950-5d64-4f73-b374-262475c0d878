---
title: "API Usage: iteraGPT Proxy for LLM Integration"
excerpt: "The iteraGPT API Proxy provides an OpenAI-compatible interface for all models available in the LLM Browser. Learn how to configure the API and integrate it into your applications."
category: "industry-news"
tags: ["api", "integration", "iteragpt", "proxy", "development", "openai-compatible"]
publishDate: "2025-06-11T17:30:00Z"
lastUpdated: "2025-06-11T17:30:00Z"
author:
  name: "LLM Browser Development Team"
  role: "Product & Engineering"
readingTime: 6
changelog:
  - type: "added"
    description: "Initial version"
    impact: "minor"
    technicalDetails: "First initial description added"
featured: true

# i18n-specific fields
lang: "en"
translationKey: "api-usage-iteragpt-proxy"
availableLanguages: ["de", "en", "pl"]

metaDescription: "Complete guide to using the iteraGPT API Proxy - OpenAI-compatible interface for all LLM models with configuration, authentication and example code"
metaKeywords: ["iteraGPT API", "LLM API", "OpenAI compatible", "API Integration", "Proxy Configuration"]
featuredImage: "/images/blog/2025-06-api.png"
---

The iteraGPT API Proxy provides a central, OpenAI-compatible interface through which all models listed in the LLM Browser can be used. This unified API significantly simplifies the integration of various LLM providers.

## Basic Configuration

The following configuration variables are required to use the iteraGPT API Proxy:

| Variable | Value | Description |
|----------|-------|-------------|
| **Base URL** | `https://api.iteragpt.iteratec.de/v1` | API endpoint of the proxy |
| **API Key** | user-defined, `sk-****************` | Valid API key from iteraGPT |
| **Model** | user-defined, usually `<provider>/<model>` | Name of the model to use, e.g. `azure/gpt-4o` (see tables below) |

### API Key Authentication

For **all** API requests, the `Authorization` header must be filled accordingly with a valid API key from iteraGPT: `Bearer sk-****************`.

## Practical Integration

### Python with OpenAI SDK

Example for using an API key with the official Python `openai` package:

```python
import openai

# Configuration of the iteraGPT Proxy
client = openai.OpenAI(
    api_key="sk-****************",  # Your iteraGPT API key
    base_url="https://api.iteragpt.iteratec.de/v1"
)

# Example request with Claude Sonnet 4
response = client.chat.completions.create(
    model="gcp/claude-3-7-sonnet",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Explain the benefits of LLM APIs."}
    ],
    max_tokens=1000,
    temperature=0.7
)

print(response.choices[0].message.content)
```

### JavaScript/TypeScript Integration

```typescript
import OpenAI from 'openai';

const client = new OpenAI({
  apiKey: 'sk-****************', // Your iteraGPT API key
  baseURL: 'https://api.iteragpt.iteratec.de/v1'
});

async function generateResponse() {
  const completion = await client.chat.completions.create({
    model: 'azure/gpt-4o',
    messages: [
      { role: 'system', content: 'You are an expert in API integration.' },
      { role: 'user', content: 'How do I implement a robust LLM API integration?' }
    ],
    max_tokens: 800,
    temperature: 0.5
  });

  return completion.choices[0].message.content;
}
```

## Further Resources

- **[LLM Browser Model Overview](/models)**: Complete list of all available models
- **[Benchmark Comparisons](/benchmark)**: Performance data for model selection, especially based on Aider Polyglot benchmark data
- **[Recommendation System](/recommendations)**: Use-case-based model recommendations
- **[OpenAI API Documentation](https://platform.openai.com/docs/api-reference)**: Complete API reference

The iteraGPT API provides a unified, reliable interface for integrating various LLM models into your applications. Through OpenAI compatibility, existing integrations can easily be migrated to the iteraGPT infrastructure.