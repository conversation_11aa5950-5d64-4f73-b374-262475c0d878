import * as React from "react";
import { useState, useMemo, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "../ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "../ui/tabs";
import type { ModelData, BenchmarkData } from "../../types/api";
import type {
  EnrichedModelData,
  SortField,
  SortDirection,
  SecurityFilter,
  ModeFilter,
} from "./types";
import { enrichModelWithCard } from "./utils";
import { ModelTable } from "./ModelTable";
import { ModelDetailDialog } from "./ModelDetailDialog";
import { ModelComparison } from "./ModelComparison";
import {
  TranslationProvider,
  useTranslation,
  t,
  type Translations,
} from "../../contexts/TranslationContext";

// Define global window interface
declare global {
  interface Window {
    translations?: any;
    currentLang?: string;
  }
}

interface ModelTableIslandProps {
  models: ModelData[];
  benchmarks: BenchmarkData[];
  averageScore?: number;
}

function ModelTableIslandContent({
  models,
  benchmarks,
  averageScore: _averageScore = 42.1,
}: ModelTableIslandProps) {
  const { translations } = useTranslation();

  // Helper function with fallback logic (same pattern as CollapsibleHeader)
  const getTranslation = (
    key: string,
    replacements?: Record<string, string | number>
  ) => {
    // Try context translations first
    let result = t(translations, key, replacements);

    // Fallback to global window translations
    if (
      result === key &&
      typeof window !== "undefined" &&
      window.translations
    ) {
      result = t(window.translations, key, replacements);
    }

    return result;
  };

  const [sortField, setSortField] = useState<SortField>("name");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedModel, setSelectedModel] = useState<EnrichedModelData | null>(
    null
  );
  const [selectedModelIds, setSelectedModelIds] = useState<string[]>([]);
  const [searchFilter, setSearchFilter] = useState("");
  const [securityFilter, setSecurityFilter] =
    useState<SecurityFilter>("internal");
  const [modeFilter, setModeFilter] = useState<ModeFilter>("Chat");
  const [isFullscreen, setIsFullscreen] = useState(false);
  const itemsPerPage = 23;

  // Enrich models with model card data
  const enrichedModels = useMemo(() => {
    return models.map((model) => enrichModelWithCard(model));
  }, [models]);

  // Get unique security levels from models
  const securityLevels = useMemo(() => {
    const uniqueLevels = new Set<string>();
    enrichedModels.forEach((model) => {
      if (model.confidentiality) {
        uniqueLevels.add(model.confidentiality);
      }
    });
    return [
      getTranslation("models.filters.all"),
      ...Array.from(uniqueLevels).sort(),
    ];
  }, [enrichedModels, translations]);

  // Filter models based on search, security, and mode
  const filteredModels = useMemo(() => {
    let filtered = enrichedModels;

    // Apply search filter
    if (searchFilter) {
      const filter = searchFilter.toLowerCase();
      filtered = filtered.filter(
        (model) =>
          model.name?.toLowerCase().includes(filter) ||
          model.provider?.toLowerCase().includes(filter) ||
          model.modelGroup?.toLowerCase().includes(filter)
      );
    }

    // Apply security filter
    if (securityFilter !== getTranslation("models.filters.all")) {
      filtered = filtered.filter(
        (model) => model.confidentiality === securityFilter
      );
    }

    // Apply mode filter
    if (modeFilter !== getTranslation("models.filters.all")) {
      const modeMapping: Record<string, string> = {
        [getTranslation("models.filters.chat")]: "chat",
        [getTranslation("models.filters.completion")]: "completion",
        [getTranslation("models.filters.embedding")]: "embedding",
        [getTranslation("models.filters.image_generation")]: "image",
      };
      const targetMode = modeMapping[modeFilter];
      filtered = filtered.filter((model) => model.mode === targetMode);
    }

    return filtered;
  }, [enrichedModels, searchFilter, securityFilter, modeFilter, translations]);

  // Calculate top performer based on current filters
  const topPerformer = useMemo(() => {
    if (filteredModels.length === 0) return null;

    const modelsWithScores = filteredModels.filter(
      (model) =>
        model.benchmarkData?.pass_rate_2 !== undefined &&
        model.benchmarkData?.pass_rate_2 !== null
    );

    if (modelsWithScores.length === 0) return null;

    return modelsWithScores.reduce((best, current) => {
      const bestScore = best.benchmarkData?.pass_rate_2 ?? -1;
      const currentScore = current.benchmarkData?.pass_rate_2 ?? -1;
      return currentScore > bestScore ? current : best;
    });
  }, [filteredModels]);

  // Calculate average score based on current filters
  const dynamicAverageScore = useMemo(() => {
    const modelsWithScores = filteredModels.filter(
      (model) =>
        model.benchmarkData?.pass_rate_2 !== undefined &&
        model.benchmarkData?.pass_rate_2 !== null
    );

    if (modelsWithScores.length === 0) return 0;

    const totalScore = modelsWithScores.reduce((sum, model) => {
      return sum + (model.benchmarkData?.pass_rate_2 ?? 0);
    }, 0);

    return totalScore / modelsWithScores.length;
  }, [filteredModels]);

  // Sort models
  const sortedModels = useMemo(() => {
    return [...filteredModels].sort((a, b) => {
      // Special case for benchmark score
      if (sortField === "benchmarkScore") {
        const aScore = a.benchmarkData?.pass_rate_2 ?? -1;
        const bScore = b.benchmarkData?.pass_rate_2 ?? -1;

        if (aScore < bScore) return sortDirection === "asc" ? -1 : 1;
        if (aScore > bScore) return sortDirection === "asc" ? 1 : -1;
        return 0;
      }

      // For other fields
      let aValue: unknown =
        a[sortField as keyof Omit<ModelData, "benchmarkScore">];
      let bValue: unknown =
        b[sortField as keyof Omit<ModelData, "benchmarkScore">];

      // Handle undefined values
      if (aValue === undefined && bValue === undefined) return 0;
      if (aValue === undefined) return 1;
      if (bValue === undefined) return -1;

      // Compare values
      if (typeof aValue === "string" && typeof bValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      // Type-safe comparison for numbers and strings
      if (typeof aValue === "number" && typeof bValue === "number") {
        if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
        if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      } else if (typeof aValue === "string" && typeof bValue === "string") {
        if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
        if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      } else {
        // Fallback for mixed types - convert to string
        const aStr = String(aValue);
        const bStr = String(bValue);
        if (aStr < bStr) return sortDirection === "asc" ? -1 : 1;
        if (aStr > bStr) return sortDirection === "asc" ? 1 : -1;
      }
      return 0;
    });
  }, [filteredModels, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(sortedModels.length / itemsPerPage);
  const _paginatedModels = sortedModels.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Handle sort
  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Handle model selection
  const toggleModelSelection = (modelId: string) => {
    if (!modelId) return;

    setSelectedModelIds((prev) => {
      if (prev.includes(modelId)) {
        return prev.filter((id) => id !== modelId);
      } else {
        return [...prev, modelId];
      }
    });
  };

  // Format currency for millions of tokens
  const formatCurrency = (value: number | null | undefined) => {
    if (value === undefined || value === null) return "N/A";
    // Convert from cost per token to cost per 1M tokens
    const costPerMillion = value * 1000000;
    return `$${costPerMillion.toFixed(2)}`;
  };

  // Reset page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchFilter, securityFilter, modeFilter]);

  return (
    <div className="space-y-6">
      {/* Statistics Overview with Dynamic Top Performer - Hidden in fullscreen */}
      {!isFullscreen && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-sm font-medium text-muted-foreground">
              {getTranslation("models.stats.total_models")}
            </h3>
            <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {filteredModels.length}
            </p>
          </div>
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-sm font-medium text-muted-foreground">
              {getTranslation("models.stats.benchmarks")}
            </h3>
            <p className="text-3xl font-bold text-green-600 dark:text-green-400">
              {benchmarks.length}
            </p>
          </div>
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-sm font-medium text-muted-foreground">
              {getTranslation("models.stats.average_score")}
            </h3>
            <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">
              {dynamicAverageScore.toFixed(1)}%
            </p>
          </div>
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-sm font-medium text-muted-foreground">
              {getTranslation("models.stats.top_performer")}
            </h3>
            {topPerformer ? (
              <div>
                <p className="text-lg font-semibold text-orange-600 dark:text-orange-400">
                  {topPerformer.name}
                </p>
                <p className="text-sm text-muted-foreground">
                  {topPerformer.benchmarkData?.pass_rate_2?.toFixed(1)}% Score
                </p>
              </div>
            ) : (
              <p className="text-lg font-semibold text-orange-600 dark:text-orange-400">
                N/A
              </p>
            )}
          </div>
        </div>
      )}

      {/* Security and Mode Filter Tabs - Hidden in fullscreen */}
      {!isFullscreen && (
        <div className="space-y-4">
          {/* Security Filter */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">
                {getTranslation("models.filters.security")}:
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <Tabs
                value={securityFilter}
                onValueChange={(value) =>
                  setSecurityFilter(value as SecurityFilter)
                }
              >
                <TabsList
                  className={`grid w-full grid-cols-${securityLevels.length}`}
                >
                  {securityLevels.map((level) => (
                    <TabsTrigger key={level} value={level}>
                      {level.toLowerCase()}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </CardContent>
          </Card>

          {/* Mode Filter */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">
                {getTranslation("models.filters.mode")}:
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <Tabs
                value={modeFilter}
                onValueChange={(value) => setModeFilter(value as ModeFilter)}
              >
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value={getTranslation("models.filters.all")}>
                    {getTranslation("models.filters.all")}
                  </TabsTrigger>
                  <TabsTrigger value={getTranslation("models.filters.chat")}>
                    {getTranslation("models.filters.chat")}
                  </TabsTrigger>
                  <TabsTrigger
                    value={getTranslation("models.filters.completion")}
                  >
                    {getTranslation("models.filters.completion")}
                  </TabsTrigger>
                  <TabsTrigger
                    value={getTranslation("models.filters.embedding")}
                  >
                    {getTranslation("models.filters.embedding")}
                  </TabsTrigger>
                  <TabsTrigger
                    value={getTranslation("models.filters.image_generation")}
                  >
                    {getTranslation("models.filters.image_generation")}
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </CardContent>
          </Card>

          {/* Search and Filter Controls */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex gap-4 items-center">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder={getTranslation(
                      "models.filters.search_placeholder"
                    )}
                    value={searchFilter}
                    onChange={(e) => setSearchFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring placeholder:text-muted-foreground"
                  />
                </div>
                <div className="text-sm text-muted-foreground">
                  {getTranslation("models.stats.filtered_count", {
                    filtered: filteredModels.length.toString(),
                    total: enrichedModels.length.toString(),
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Model Table Component */}
      <ModelTable
        models={sortedModels}
        sortField={sortField}
        sortDirection={sortDirection}
        selectedModelIds={selectedModelIds}
        currentPage={currentPage}
        totalPages={totalPages}
        itemsPerPage={itemsPerPage}
        averageScore={dynamicAverageScore}
        onSort={handleSort}
        onModelSelect={setSelectedModel}
        onToggleSelection={toggleModelSelection}
        onPageChange={setCurrentPage}
        formatCurrency={formatCurrency}
        isFullscreen={isFullscreen}
        onToggleFullscreen={() => setIsFullscreen(!isFullscreen)}
        getTranslation={getTranslation}
      />

      {/* Model Details Dialog Component */}
      {selectedModel && (
        <ModelDetailDialog
          model={selectedModel}
          isOpen={!!selectedModel}
          onClose={() => setSelectedModel(null)}
          formatCurrency={formatCurrency}
        />
      )}

      {/* Model Comparison Component */}
      {selectedModelIds.length >= 2 && (
        <ModelComparison
          models={enrichedModels.filter(
            (model) => model.id && selectedModelIds.includes(model.id)
          )}
          selectedModelIds={selectedModelIds}
          formatCurrency={formatCurrency}
          onClearSelection={() => setSelectedModelIds([])}
        />
      )}
    </div>
  );
}

export default function ModelTableIsland(props: ModelTableIslandProps) {
  const [initialTranslations, setInitialTranslations] = useState<Translations>(
    {}
  );
  const [initialLang, setInitialLang] = useState("de");

  useEffect(() => {
    // Get initial translations from global variables
    if (typeof window !== "undefined") {
      const globalTranslations =
        (window as any).__TRANSLATIONS__ || (window as any).translations || {};
      const globalLang =
        (window as any).__CURRENT_LANG__ || (window as any).currentLang || "de";

      setInitialTranslations(globalTranslations);
      setInitialLang(globalLang);
    }
  }, []);

  return (
    <TranslationProvider
      initialTranslations={initialTranslations}
      initialLang={initialLang}
    >
      <ModelTableIslandContent {...props} />
    </TranslationProvider>
  );
}
