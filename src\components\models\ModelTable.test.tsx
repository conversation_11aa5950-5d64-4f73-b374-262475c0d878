import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import { ModelTable } from './ModelTable';
import type { EnrichedModelData } from './types';

// Mock-Daten für Tests
const mockModels: EnrichedModelData[] = [
  {
    id: 'test-model-1',
    name: 'Test Model 1',
    provider: 'TestProvider',
    description: 'A test model',
    capabilities: [],
    modelGroup: 'test',
    isAvailable: true,
    confidentiality: 'public',
    mode: 'chat',
    maxTokens: 4096,
    maxInputTokens: 4000,
    maxOutputTokens: 4096,
    inputCostPerToken: 0.001,
    outputCostPerToken: 0.002,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsToolChoice: true,
    supportsReasoning: false,
    contextWindow: 4096,
    recommendation: false,
    db_model: false,
    base_model: 'test-base',
    inputCostPer1kTokens: 1.0,
    outputCostPer1kTokens: 2.0,
    modelCard: undefined,
  },
  {
    id: 'test-model-2',
    name: 'Test Model 2',
    provider: 'TestProvider',
    description: 'Another test model',
    capabilities: [],
    modelGroup: 'test',
    isAvailable: false,
    confidentiality: 'internal',
    mode: 'completion',
    maxTokens: 8192,
    maxInputTokens: 8000,
    maxOutputTokens: 8192,
    inputCostPerToken: 0.002,
    outputCostPerToken: 0.004,
    supportsFunctionCalling: false,
    supportsVision: true,
    supportsToolChoice: false,
    supportsReasoning: true,
    contextWindow: 8192,
    recommendation: true,
    db_model: true,
    base_model: 'test-base-2',
    inputCostPer1kTokens: 2.0,
    outputCostPer1kTokens: 4.0,
    modelCard: undefined,
  },
];

const defaultProps = {
  models: mockModels,
  currentPage: 1,
  totalPages: 1,
  itemsPerPage: 10,
  sortField: 'name' as const,
  sortDirection: 'asc' as const,
  selectedModelIds: [],
  averageScore: 88.9,
  onSort: vi.fn(),
  onPageChange: vi.fn(),
  onModelSelect: vi.fn(),
  onToggleSelection: vi.fn(),
  formatCurrency: vi.fn((value) => value ? `$${(value * 1000000).toFixed(2)}` : 'N/A'),
};

describe('ModelTable Component', () => {
  it('renders model data correctly', () => {
    render(<ModelTable {...defaultProps} />);
    
    // Check if model names are displayed
    expect(screen.getByText('Test Model 1')).toBeInTheDocument();
    expect(screen.getByText('Test Model 2')).toBeInTheDocument();
    
    // Check if providers are displayed
    expect(screen.getAllByText('TestProvider')).toHaveLength(2);
  });

  it('displays model capabilities correctly', () => {
    render(<ModelTable {...defaultProps} />);
    
    // Check for capability icons/indicators
    const functionCallingIcons = screen.getAllByTestId(/function-calling/i);
    const visionIcons = screen.getAllByTestId(/vision/i);
    
    expect(functionCallingIcons.length).toBeGreaterThan(0);
    expect(visionIcons.length).toBeGreaterThan(0);
  });

  it('handles model selection', async () => {
    const user = userEvent.setup();
    const onModelSelect = vi.fn();
    
    render(<ModelTable {...defaultProps} onModelSelect={onModelSelect} />);
    
    // Click on first model row
    const modelRow = screen.getByText('Test Model 1').closest('tr');
    expect(modelRow).toBeInTheDocument();
    
    if (modelRow) {
      await user.click(modelRow);
      expect(onModelSelect).toHaveBeenCalledWith(mockModels[0]);
    }
  });

  it('handles sorting', async () => {
    const user = userEvent.setup();
    const onSort = vi.fn();
    
    render(<ModelTable {...defaultProps} onSort={onSort} />);
    
    // Click on a sortable column header
    const nameHeader = screen.getByText(/name/i);
    await user.click(nameHeader);
    
    expect(onSort).toHaveBeenCalledWith('name');
  });

  it('displays pricing information', () => {
    render(<ModelTable {...defaultProps} />);
    
    // Check if formatCurrency was called
    expect(defaultProps.formatCurrency).toHaveBeenCalled();
    
    // Check if pricing values are displayed
    expect(screen.getByText('$1000.00')).toBeInTheDocument();
    expect(screen.getByText('$2000.00')).toBeInTheDocument();
  });

  it('shows availability status', () => {
    render(<ModelTable {...defaultProps} />);
    
    // Check for availability indicators
    const availabilityElements = screen.getAllByTestId(/availability/i);
    expect(availabilityElements.length).toBeGreaterThan(0);
  });

  it('handles model selection toggle', async () => {
    const user = userEvent.setup();
    const onToggleSelection = vi.fn();
    
    render(<ModelTable {...defaultProps} onToggleSelection={onToggleSelection} />);
    
    // Find and click selection checkbox
    const checkboxes = screen.getAllByRole('checkbox');
    if (checkboxes.length > 0) {
      await user.click(checkboxes[0]);
      expect(onToggleSelection).toHaveBeenCalled();
    }
  });

  it('displays benchmark scores', () => {
    render(<ModelTable {...defaultProps} />);
    
    // Check if benchmark scores are displayed
    expect(screen.getByText('85.5')).toBeInTheDocument();
    expect(screen.getByText('92.3')).toBeInTheDocument();
  });

  it('handles empty model list', () => {
    render(<ModelTable {...defaultProps} models={[]} />);
    
    // Should show empty state or no crash
    expect(screen.getByRole('table')).toBeInTheDocument();
  });

  it('applies correct CSS classes for fullscreen mode', () => {
    render(<ModelTable {...defaultProps} isFullscreen={true} />);
    
    const table = screen.getByRole('table');
    expect(table.closest('.relative')).toBeInTheDocument();
  });
});