# Basis

```
Wie kann die aktuelle Code-Basis für das Projekt optimiert werden. Erstelle einen Plan unter \docs\Optimierungsplan.md

@/README.md
```


# Dart-AI

```
<PERSON><PERSON><PERSON> in "DART" entsprechende Aufgaben-Pakete!
```

# Erweiteurng der .roorules

```
# Usage of Dart with MCP for project and task tracking

- Update all actions to the Dart AI via MCP
- Make sure to always move tasks from to do, doing, and complete.
- Make sure to update the description as tasks get moved from to do to doing and also update the description With What has been done once the task is complete.
- Don't create new subtasks on the Dartboard
```
