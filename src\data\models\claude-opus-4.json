{"basicInfo": {"modelId": "claude-opus-4@20250514", "displayName": "<PERSON> 4", "provider": "Anthropic", "modelFamily": "<PERSON>", "version": "20250514", "description": "Das bisher leistungsstärkste Modell von Anthropic und das modernste Codierungsmodell. Claude Opus 4 bietet eine konstante Leistung bei langwierigen Aufgaben, die konzentrierte Anstrengungen und Tausende von Schritten erfordern.", "releaseDate": "2025-05-22", "status": "GA", "knowledgeCutoff": "November 2024"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 32000, "architecture": "Transformer", "supportedInputTypes": ["text", "image", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Moderately Fast", "rateLimits": {"queriesPerMinute": 25, "inputTokensPerMinute": 60000, "outputTokensPerMinute": 6000}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 15.0, "outputCostPer1MTokens": 75.0, "cachingCosts": {"cacheWrites": 18.75, "cacheHits": 1.5}, "currency": "USD"}, "availability": {"regions": [{"region": "us-east5", "availability": "GA"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI", "AWS Bedrock", "Anthropic API"]}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": true, "fixedQuota": true}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 72.5, "alternativeScores": {"multipleAttempts": 79.4}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Mit bash/editor tools. Alternative Score mit parallel test-time compute."}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 43.2, "alternativeScores": {"multipleAttempts": 50.0}, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Agentic terminal coding mit Claude Code als agent framework."}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 79.6, "alternativeScores": {"multipleAttempts": 83.3}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Alternative Score mit extended thinking."}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic coding", "score": 81.4, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Agentic tool use benchmark - Retail domain."}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic coding", "score": 59.6, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Agentic tool use benchmark - Airline domain."}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 88.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Multilingual Q&A - Durchschnitt über 14 nicht-englische Sprachen."}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 73.4, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "MMMU benchmark from vals.ai (Claude Opus 4 Nonthinking)"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 75.5, "alternativeScores": {"multipleAttempts": 90.0}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "High school math competition. Alternative Score mit extended thinking."}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 72.0, "alternativeScores": {"withThinking": 72.0, "noThinking": 70.7}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-25", "notes": "Aider polyglot benchmark with diff edit format (32k thinking tokens vs no thinking)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 97.3, "alternativeScores": {"withThinking": 97.3, "noThinking": 98.7}, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-05-25", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1411.98, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #1 (tied)"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 77.8, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 6.68, "alternativeScores": {"thinking": 10.72}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "<PERSON> 4: 6.68±0.98, <PERSON> 4 (Thinking): 10.72±1.21"}], "metadata": {"lastUpdated": "2025-06-09T12:47:00Z", "dataSource": "Google Cloud Vertex AI Documentation, Anthropic Blog Post, Anthropic Documentation, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard", "version": "1.2"}}