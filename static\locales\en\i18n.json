{"site": {"title": "<PERSON><PERSON>", "description": "LLM Browser - Compare and evaluate language models"}, "nav": {"models": "Models", "blog": "Blog", "recommendations": "Recommendations", "benchmarks": "Benchmarks", "api_usage": "API Usage@iteratec"}, "footer": {"copyright": "© 2025 LLM Browser - Vise-Coding", "built_with": "Built with Astro, React, TypeScript & Tailwind CSS"}, "language_selector": {"label": "Select language", "de": "German", "en": "English", "pl": "Polish"}, "language": "Language", "redirect": "Redirecting to model overview...", "meta_description": "LLM Browser - Compare and evaluate language models", "vise_coding": "Vise-Coding", "loading": "Loading...", "models": {"header": "LLM Model Browser", "description": "Discover {{count}} AI models with detailed information on pricing, capabilities, and benchmark results. The goal is to make the selection of suitable models for everyday work more interactive, easier, and more transparent. Otherwise, the information must be gathered from many sources. Based on live data from iteraGPT (LiteLLM) and AI-generated model cards from various sources.", "filters": {"all": "All", "security": "Security", "mode": "Mode", "chat": "Cha<PERSON>", "completion": "Completion", "embedding": "Embedding", "image_generation": "Image Generation", "search_placeholder": "Search by name, provider or model group...", "of": "of", "models": "models"}, "stats": {"total_models": "Total Models", "benchmarks": "Benchmarks", "average_score": "Average Score", "top_performer": "Top Performer", "filtered_count": "{{filtered}} of {{total}} models"}, "comparison": {"title": "Model Comparison ({{count}} models)", "reset_selection": "Reset Selection", "property": "Property", "provider": "Provider", "litellm_availability": "LiteLLM Availability", "available": "Available", "model_card_only": "Model Card Only", "context_window": "Context Window", "max_output_tokens": "<PERSON> Output Tokens", "input_cost": "Input Cost (per 1M tokens)", "output_cost": "Output Cost (per 1M tokens)", "yes": "Yes", "no": "No", "capabilities": "Capabilities", "supported_platforms": "Supported Platforms", "metric": "Metric", "range": "Range", "no_details_available": "No detailed information available.", "other_benchmarks": "Other Benchmarks", "at": "at", "and": "and", "well_formed_code": "well formed code", "category": "Category", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variants": "Variants", "not_available": "N/A", "aider_polyglot_short": "Aider-Polyglot (o)", "website": "Website", "paper": "Paper", "aider_benchmark": {"title": "Aider's polyglot benchmark", "description": "Measures the ability of models to edit and improve code in various programming languages.", "metric": "Pass Rate (2nd attempt)", "range": "0-100%", "fallback_description": "Aider's coding benchmark - measures the ability of models to edit and improve code."}}, "capabilities": {"vision": "Vision", "pdf_input": "PDF Input", "audio_input": "Audio Input", "audio_output": "Audio Output", "embedding_image": "Embedding Image", "function_calling": "Function Calling", "prompt_caching": "Prompt Caching", "reasoning": "Reasoning", "system_messages": "System Messages"}, "table": {"pagination": {"showing": "Showing {{start}} to {{end}} of {{total}} models", "previous": "Previous", "next": "Next"}, "headers": {"select": "Select", "security": "Security", "model_card": "Model Card", "litellm_status": "LiteLLM/Status", "name": "Name", "provider": "Provider", "mode": "Mode", "context": "Context", "max_output": "Max Output", "input_cost_per_million": "Input Cost/1M", "output_cost_per_million": "Output Cost/1M", "polyglot_score": "Polyglot Score", "support": "Support", "details": "Details", "show_filters": "Show Filters", "fullscreen": "Fullscreen"}, "tooltips": {"confidential": "Confidential", "internal": "Internal", "public": "Public", "model_card_available": "Model Card available", "deprecated": "Deprecated", "shutdown_date": "Shutdown: {{date}}", "litellm_available": "LiteLLM available", "model_card_only": "Model Card only", "chat": "Cha<PERSON>", "embedding": "Embedding", "image": "Image", "vision_processing": "Vision/Image processing", "pdf_input": "PDF Input", "audio_input": "Audio Input", "audio_output": "Audio Output", "embedding_image_input": "Embedding Image Input", "function_calling": "Function Calling", "prompt_caching": "Prompt Caching", "reasoning": "Reasoning", "system_messages": "System Messages", "capability_vision": "Vision/Image processing", "capability_pdf_input": "PDF Input", "capability_audio_input": "Audio Input", "capability_audio_output": "Audio Output", "capability_embedding_image_input": "Embedding Image Input", "capability_function_calling": "Function Calling", "capability_prompt_caching": "Prompt Caching", "capability_reasoning": "Reasoning", "capability_system_messages": "System Messages"}, "empty_state": "No models found", "select_model": "Select model {{name}}", "details_button": "Details"}, "detail_dialog": {"tabs": {"overview": "Overview", "technical": "Technical", "capabilities": "Capabilities", "performance": "Performance", "pricing": "Pricing", "benchmarks": "Benchmarks", "availability": "Availability", "recommendations": "Recommendations"}, "basic_info": {"title": "Basic Information", "provider": "Provider", "model_group": "Model Group", "availability": "Availability", "available": "Available", "unavailable": "Unavailable", "status": "Status", "release_date": "Release Date", "description": "Description", "technical_id": "Technical Identification", "model_id": "Model ID", "model_card_id": "Model Card ID", "internal_key": "Internal Key"}, "capabilities": {"core_title": "Core Capabilities", "modality_title": "Modalities", "technical_title": "Technical Capabilities", "additional_title": "Additional Features", "openai_params_title": "Supported OpenAI Parameters", "vision": "Vision", "pdf_input": "PDF Input", "audio_input": "Audio Input", "audio_output": "Audio Output", "function_calling": "Function Calling", "prompt_caching": "Prompt Caching", "reasoning": "Reasoning", "system_messages": "System Messages", "parallel_function_calling": "Parallel Function Calling", "response_schema": "Response Schema", "tool_choice": "Tool Choice", "native_streaming": "Native Streaming", "assistant_prefill": "Assistant Prefill", "embedding_image_input": "Embedding Image Input", "multilingual_support": "Multilingual Support", "image_generation": "Image Generation", "litellm_provisioning": "LiteLLM Provisioning", "recommended": "Recommended"}, "technical_specs": {"title": "Technical Specifications", "token_limits": "Token Limits", "context_window": "Context Window", "max_output_tokens": "<PERSON> Output Tokens", "max_input_tokens": "Max Input Tokens", "max_reasoning_tokens": "<PERSON>s", "architecture_params": "Architecture & Parameters", "architecture": "Architecture", "parameter_count": "Parameter Count", "supported_modalities": "Supported Modalities", "input_types": "Input Types", "output_types": "Output Types", "input_limitations": "Input Limitations", "max_images": "Max Images", "max_image_size": "Max Image Size", "max_audio_length": "Max Audio Length", "max_video_length": "Max Video Length", "supported_mime_types": "Supported MIME Types", "legacy_api_data": "Legacy API Data", "modalities": "Modalities", "output_modalities": "Output Modalities", "vector_size": "Vector Size"}, "pricing": {"title": "Pricing", "standard_pricing": "Standard Pricing", "input_cost_per_1m": "Input Cost (per 1M tokens)", "output_cost_per_1m": "Output Cost (per 1M tokens)", "currency": "<PERSON><PERSON><PERSON><PERSON>", "caching_costs": "Caching Costs", "cache_hits": "Cache Hits (per 1M tokens)", "cache_writes": "Cache Writes (per 1M tokens)", "cache_hits_description": "Significantly cheaper for repeated content", "cache_writes_description": "One-time cost for cache creation", "reasoning_costs": "Reasoning Costs", "reasoning_tokens": "Reasoning Tokens (per 1M)", "completion_tokens": "Completion Tokens (per 1M)", "reasoning_description": "Internal thinking processes", "completion_description": "Final response tokens", "reasoning_note": "For reasoning models, both reasoning and completion tokens are charged. Actual costs may vary depending on request complexity.", "batch_processing": "Batch Processing (50% discount)", "batch_input": "Input (per 1M tokens)", "batch_output": "Output (per 1M tokens)", "standard_label": "Standard", "legacy_pricing": "Legacy API Pricing (per 1K tokens)", "alternative_pricing": "Alternative Pricing Models", "per_character_input": "Per Character (Input)", "per_character_output": "Per Character (Output)", "per_query": "Per Query", "per_second_input": "Per Second (Input)", "per_second_output": "Per Second (Output)", "per_image": "Per Image", "audio_pricing": "Audio Pricing", "audio_input_token": "Audio Input (per token)", "audio_output_token": "Audio Output (per token)", "cache_pricing_api": "<PERSON><PERSON> (API)", "cache_creation": "Cache Creation (per token)", "cache_read": "<PERSON><PERSON> (per token)", "no_pricing_info": "No pricing information available. Please check the provider documentation for current prices."}, "benchmarks": {"title": "Benchmarks", "aider_polyglot": "Aider-<PERSON>y<PERSON><PERSON>", "polyglot_score": "Polyglot Score", "tests_passed": "tests passed", "benchmark_details": "Benchmark Details", "test_cases": "Test Cases", "pass_rate_2": "Pass Rate 2", "pass_rate_1": "Pass Rate 1", "well_formed": "Well-formed", "total_cost": "Total Cost", "edit_format": "Edit Format", "command_used": "Command Used", "detailed_stats": "Detailed Statistics", "model_card_benchmarks": "Model Card Benchmarks", "other_benchmarks": "Other Benchmarks", "no_benchmark_data": "No benchmark data available", "no_benchmark_description": "No benchmark results are currently available for this model.", "benchmark_info": "Benchmark Information", "aider_description": "Tests code editing capabilities across various programming languages. The score indicates the percentage of successfully edited tasks.", "model_card_description": "Comprehensive evaluations across various categories like reasoning, mathematics, code generation, and more. Scores are scaled differently depending on the benchmark type.", "benchmark_note": "Benchmark results may vary depending on test conditions, version, and configuration. They provide guidance, but actual performance may differ depending on the use case."}, "availability": {"title": "Availability", "no_availability_data": "No Availability Data", "no_availability_description": "Availability information is only available for models with complete Model Cards.", "supported_platforms": "Supported Platforms", "platform_specific_ids": "Platform-specific IDs", "regional_availability": "Regional Availability", "global_available": "Available globally", "data_processing_regions": "Data Processing Regions", "data_processing_description": "These regions are used for ML processing and data handling.", "security_features": "Security Features", "data_residency": "Data Residency", "cmek_support": "CMEK Support", "vpc_support": "VPC Support", "access_transparency": "Access Transparency", "available": "Available", "not_available": "Not available", "compliance_standards": "Compliance Standards", "usage_types": "Usage Types", "dynamic_shared_quota": "Dynamic Shared Quota", "provisioned_throughput": "Provisioned Throughput", "fixed_quota": "Fixed Quota", "additional_info": "Additional Information", "availability_note": "Availability may change depending on region, platform, and time. Please check current availability with your chosen provider.", "data_source": "Data Source", "last_updated": "Last Updated"}}}, "blog": {"title": "LLM Blog", "description": "Latest insights on AI models, release notes, and benchmark analyses. Stay up to date with the latest developments in the LLM landscape.", "sectionModelAnalysis": "Model Analyses", "sectionModelAnalysisDesc": "Detailed reviews of the latest AI models", "sectionReleaseNotes": "Release Notes", "sectionReleaseNotesDesc": "Latest updates and changes to models", "sectionBenchmarkAnalysis": "Benchmark Analyses", "sectionBenchmarkAnalysisDesc": "In-depth evaluations of performance tests", "sectionIndustryNews": "Industry News", "sectionIndustryNewsDesc": "Important developments in the AI market", "stats": {"articles": "Articles", "categories": "Categories", "featured": "Featured Posts", "tags": "Tags"}, "featured_articles": "Featured Articles", "loading_articles": "Loading articles...", "articles": "articles", "found": "found", "clear_all_filters": "Clear all filters", "no_articles_found": "No articles found", "try_different_search": "Try different search terms or filters.", "no_articles_available": "No blog articles available yet.", "reset_filters": "Reset filters", "previous": "Previous", "next": "Next", "back_to_blog": "Back to Blog", "available_in": "Available in:", "available_languages": "Available languages:", "reading_time": "min read", "related_information": "Related Information", "related_models": "Related Models", "related_benchmarks": "Related Benchmarks", "similar_articles": "Similar Articles"}, "recommendations": {"title": "Recommendations for enterprises", "description": "Intelligent recommendation system for {{totalModels}} LLM models from {{totalProviders}} providers. Based on standard use cases in enterprises, the optimal models for various scenarios are recommended. Benchmark performance, capabilities, costs, and other factors are considered for informed decisions.", "availableModels": "Available Models", "providers": "Providers", "gaStatus": "GA Status", "avgInputCost": "Avg. Input Cost", "avgOutputCost": "Avg. Output Cost", "perMillion": "per 1M tokens", "pageTitle": "Model Recommendations for Enterprises", "pageDescription": "Intelligent recommendations for selecting the optimal LLM model based on standard enterprise use cases. Considers performance, costs, capabilities, and other factors.", "topUseCases": "Top Use Cases:", "benchmarksUsed": "Benchmarks Used:", "requiredCapabilities": "Required Capabilities:", "category": "Category:", "priority": {"high": "High", "medium": "Medium", "low": "Low", "label": "Priority"}, "topRecommendations": "Top Recommendations:", "costEffectiveness": {"high": "Cost-effective", "medium": "Standard", "low": "Expensive"}, "qualityCheck": "Quality Check", "stats": {"useCases": "Use Cases", "models": "Models", "recommendations": "Recommendations", "excellent": "Excellent", "avgPerUseCase": "Avg per Use Case"}, "tabs": {"overview": "Overview", "topModels": "Top Models", "highPriority": "Critical Use Cases", "allUseCases": "All Use Cases"}, "sections": {"bestOverallModels": "Best Overall Models", "bestOverallDescription": "Models with the best average performance across all use cases.", "criticalUseCases": "Critical Use Cases", "criticalDescription": "Recommendations for business-critical use cases with high priority.", "allUseCasesTitle": "All Use Cases", "allUseCasesDescription": "Detailed recommendations for every standard use case."}}, "benchmark": {"title": "Benchmark Results", "description": "Detailed analysis of Polyglot benchmark results for {{totalBenchmarks}} benchmark tests.", "testedModels": "Tested Models", "averageScore": "Average Score", "highestScore": "Highest Score", "testCases": "Test Cases", "about": "About the Polyglot Benchmark:", "aboutText1": "This benchmark is based on Exercism coding exercises and tests the ability of language models to solve complex programming problems in 6 different languages:", "languages": "C++, Go, Java, JavaScript, Python, and Rust", "aboutText2": "The benchmark includes the {{hardest}} most difficult exercises out of a total of {{total}} available Exercism problems and was designed to be much more challenging than previous benchmarks. The scores are based on the number of successfully solved coding problems and provide a precise assessment of the code-editing capabilities of modern LLMs."}, "qc": {"title": "Benchmark Comparison for LLM Models", "header": "Benchmark Comparison", "description": "Detailed benchmark analysis for {{modelCount}} LLM models. Compare the performance of different models across {{benchmarkCount}} different benchmarks. This overview enables a direct cross-comparison of actual benchmark values from the model cards.", "availableBenchmarks": "Available Benchmarks", "avgBenchmarksPerModel": "Avg. Benchmarks/Model", "mostCommonBenchmark": "Most Common Benchmark", "modelsWithBenchmarks": "Models with Benchmarks", "topBenchmarks": "Top 5 Benchmarks (by availability)", "models": "Models"}, "components": {"collapsible_header": {"show_info": "Show info", "hide_info": "Hide info"}}, "calculation_methodology": {"title": "Calculation Methodology", "dialog_title": "Recommendation Calculation Methodology", "dialog_description": "Detailed explanation of the intelligent recommendation system and algorithms used", "scoring_overview": "Scoring Algorithm Overview", "scoring_description": "Our intelligent recommendation system evaluates each model for each use case using a 100-point system. The total score consists of five weighted factors:", "benchmark_performance": "Benchmark Performance", "required_capabilities": "Required Capabilities", "cost_efficiency": "Cost Efficiency", "latency_speed": "Latency/Speed", "availability": "Availability", "benchmark_calculation": "Benchmark Performance (45% Weight)", "benchmark_description": "Benchmark scores are intelligently normalized and weighted use-case specifically:", "critical_coding_benchmarks": "Critical Coding Benchmarks (1.5x Weight):", "score_normalization": "Score Normalization:", "arena_scores": "Arena Scores: 1300=60pts, 1350=70pts, 1400=85pts, 1450+=95pts", "elo_ratings": "ELO Ratings: 1200=30pts, 1800=70pts, 2400+=90pts", "standard_benchmarks": "Standard Benchmarks: Direct 0-100% adoption", "other_factors": "Other Evaluation Factors", "capabilities_score": "Capabilities (25%)", "capabilities_description": "Proportional: (fulfilled capabilities / required capabilities) × 100", "cost_score": "Cost Score (10%)", "budget_range": "Budget (0-1$): 85-95 points", "standard_range": "Standard (1-5$): 70-85 points", "premium_range": "Premium (5$+): 50-70 points", "latency_score": "Latency Score (15%)", "fastest": "Fastest: 100pts", "fast": "Fast: 85pts", "moderately_fast": "Moderately Fast: 70pts", "slow_slowest": "Slow/Slowest: 50/30pts", "context_window_score": "Context Window Score", "availability_score": "Availability Score (5%)", "ga_score": "GA: 100pts", "preview_score": "Preview: 80pts", "other_score": "Other: 60pts", "quality_factors": "Quality Factors & Coding Optimizations (2025)", "benchmark_penalties": "Benchmark Penalties", "penalty_description": "-5 points per benchmark with <40% in Factuality/Knowledge categories", "multimodal_bonus": "Multimodal Bonus", "multimodal_description": "+5 points for Vision capabilities in Data Analysis/Documentation use cases", "coding_adjustments": "Special Coding Adjustments", "coding_use_cases": "Coding Use Cases: +10% additional benchmark weighting", "context_bonus": "Context Bonus: +3 points for >500k/200k, +2 points for >128k Context Window", "affected_use_cases": "Affected Use Cases: code-generation, code-review, debugging, refactoring, testing, api-integration, devops-automation", "final_calculation": "Final Score Calculation", "total_score_formula": "Total Score = (Benchmark × 45%) + (Capabilities × 25%) + (Cost × 10%) + (Latency × 15%) + (Availability × 5%) + Multimodal Bonus + Coding Adjustments - Benchmark Penalties", "use_case_mappings": "Use Case Benchmark Mappings", "mappings_description": "Each use case uses specific benchmarks for evaluation. The most important code benchmarks are prioritized:", "code_generation": "Code generation/review/debugging/refactoring/testing:", "api_integration": "API Integration:", "devops_automation": "DevOps Automation:", "data_analysis": "Data Analysis:", "learning_documentation": "Learning/Documentation:", "rating_scale": "Rating Scale & Categorization", "suitability_categories": "Suitability Categories:", "excellent": "Excellent", "good": "Good", "acceptable": "Acceptable", "limited": "Limited", "recommendation_categories": "Recommendation Categories:", "recommended": "Recommended", "alternative": "Alternative", "not_recommended": "Not Recommended", "cost_effectiveness": "Cost Effectiveness:", "high_cost_eff": "High: Budget + Score >65", "medium_cost_eff": "Medium: Standard + Score >70", "low_cost_eff": "Low: Premium models", "disclaimer_title": "Important Notice", "disclaimer_text": "These recommendations are based on algorithmic calculations and serve as guidance. For production applications, always conduct your own tests and evaluations. Prices and availability may change. Data as of:"}, "use_case_dashboard": {"title": "Use Case Recommendations Dashboard", "description": "Overview of the best model recommendations for standard enterprise use cases. Based on benchmark performance, capabilities, costs, and other factors.", "use_cases": "Use Cases", "available_models": "Available Models", "total_recommendations": "Total Recommendations", "avg_recommendations": "Avg Recommendations/Use Case", "search_placeholder": "Search use cases...", "select_category": "Select category", "all_categories": "All Categories", "sort_by_name": "By Name", "sort_by_recommended": "By Recommendations", "recommended_count": "recommended", "cost_effective": "Cost-effective", "standard_cost": "Standard", "expensive": "Expensive", "no_suitable_models": "No suitable models found", "additional_models": "additional models available", "no_use_cases_found": "No use cases found", "try_different_search": "Try different search terms or filters."}}