export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  category: 'model-analysis' | 'release-notes' | 'benchmark-analysis' | 'industry-news';
  tags: string[];
  publishDate: string;
  lastUpdated: string;
  author: {
    name: string;
    role: string;
  };
  readingTime: number; // in minutes
  featured: boolean;
  
  // Model-specific fields
  relatedModelIds?: string[];
  relatedBenchmarks?: string[];
  
  // Release notes specific
  releaseVersion?: string;
  changelog?: ChangelogEntry[];
  
  // SEO
  metaDescription?: string;
  metaKeywords?: string[];
  
  // Content structure
  sections?: BlogSection[];
  
  // Media
  featuredImage?: string;
  gallery?: string[];
}

export interface ChangelogEntry {
  type: 'added' | 'changed' | 'deprecated' | 'removed' | 'fixed' | 'security';
  description: string;
  impact: 'major' | 'minor' | 'patch';
  technicalDetails?: string;
}

export interface BlogSection {
  id: string;
  title: string;
  content: string;
  type: 'text' | 'benchmark-comparison' | 'model-comparison' | 'code-example' | 'performance-chart';
  data?: Record<string, unknown>; // Flexible data for different section types
}

export interface BlogMetadata {
  totalPosts: number;
  categories: { [key: string]: number };
  tags: { [key: string]: number };
  latestPosts: BlogPost[];
  featuredPosts: BlogPost[];
}

export interface ModelBackgroundInfo {
  modelId: string;
  developmentHistory: {
    conceptDate?: string;
    announcementDate?: string;
    releaseDate: string;
    keyMilestones: Milestone[];
  };
  technicalInsights: {
    architecture: string;
    trainingDetails?: TrainingDetails;
    innovations: string[];
    limitations: string[];
  };
  competitiveAnalysis: {
    directCompetitors: string[];
    differentiators: string[];
    marketPosition: string;
  };
  useCaseSpotlight: {
    primaryUseCases: string[];
    successStories?: SuccessStory[];
    bestPractices: string[];
  };
  futureRoadmap?: {
    plannedUpdates: string[];
    expectedImprovements: string[];
    deprecationTimeline?: string;
  };
}

export interface Milestone {
  date: string;
  title: string;
  description: string;
  impact: 'major' | 'minor';
}

export interface TrainingDetails {
  datasetSize?: string;
  trainingDuration?: string;
  computeResources?: string;
  keyTechniques: string[];
}

export interface SuccessStory {
  title: string;
  industry: string;
  summary: string;
  results: string[];
}

export interface BlogFilters {
  category?: string;
  tags?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  relatedModel?: string;
  featured?: boolean;
}

export interface BlogSearchResult {
  posts: BlogPost[];
  totalCount: number;
  facets: {
    categories: { [key: string]: number };
    tags: { [key: string]: number };
    models: { [key: string]: number };
  };
}