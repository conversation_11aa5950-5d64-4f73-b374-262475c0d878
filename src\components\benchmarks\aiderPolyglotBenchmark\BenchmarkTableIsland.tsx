import * as React from "react";
import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "../../ui/card";
import type { ModelData, BenchmarkData } from "../../../types/api";
import { BenchmarkTable } from "./BenchmarkTable";
import { BenchmarkDetailDialog } from "./BenchmarkDetailDialog";

type SortField = "model" | "pass_rate_2" | "percent_cases_well_formed" | "total_cost" | "edit_format";
type SortDirection = "asc" | "desc";

interface BenchmarkTableIslandProps {
  benchmarks: BenchmarkData[];
  models: ModelData[];
}

export default function BenchmarkTableIsland({ benchmarks, models: _models }: BenchmarkTableIslandProps) {
  const [sortField, setSortField] = useState<SortField>("pass_rate_2");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedBenchmark, setSelectedBenchmark] = useState<BenchmarkData | null>(null);
  const [searchFilter, setSearchFilter] = useState("");
  const [isFullscreen, setIsFullscreen] = useState(false);
  const itemsPerPage = 15;

  // Filter benchmarks based on search
  const filteredBenchmarks = useMemo(() => {
    if (!searchFilter) return benchmarks;
    
    const filter = searchFilter.toLowerCase();
    return benchmarks.filter(benchmark => 
      benchmark.model?.toLowerCase().includes(filter) ||
      benchmark.command?.toLowerCase().includes(filter)
    );
  }, [benchmarks, searchFilter]);

  // Sort benchmarks
  const sortedBenchmarks = useMemo(() => {
    return [...filteredBenchmarks].sort((a, b) => {
      let aValue: unknown = a[sortField as keyof BenchmarkData];
      let bValue: unknown = b[sortField as keyof BenchmarkData];
      
      // Handle undefined values
      if (aValue === undefined && bValue === undefined) return 0;
      if (aValue === undefined) return 1;
      if (bValue === undefined) return -1;
      
      // Compare values
      if (typeof aValue === "string" && typeof bValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      // Type-safe comparison for numbers and strings
      if (typeof aValue === "number" && typeof bValue === "number") {
        if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
        if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      } else if (typeof aValue === "string" && typeof bValue === "string") {
        if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
        if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      } else {
        // Fallback for mixed types - convert to string
        const aStr = String(aValue);
        const bStr = String(bValue);
        if (aStr < bStr) return sortDirection === "asc" ? -1 : 1;
        if (aStr > bStr) return sortDirection === "asc" ? 1 : -1;
      }
      return 0;
    });
  }, [filteredBenchmarks, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(sortedBenchmarks.length / itemsPerPage);
  const _paginatedBenchmarks = sortedBenchmarks.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Handle sort
  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection(field === "pass_rate_2" ? "desc" : "asc");
    }
  };

  // Reset page when search changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchFilter]);

  return (
    <div className="space-y-6">
      {/* Search and Filter Controls - Hidden in fullscreen */}
      {!isFullscreen && (
        <Card>
          <CardHeader>
            <CardTitle>Filter & Suche</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 items-center">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Suche nach Modellname oder Befehl..."
                  value={searchFilter}
                  onChange={(e) => setSearchFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring placeholder:text-muted-foreground"
                />
              </div>
              <div className="text-sm text-muted-foreground">
                {filteredBenchmarks.length} von {benchmarks.length} Benchmarks
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Benchmark Table Component */}
      <BenchmarkTable
        benchmarks={sortedBenchmarks}
        sortField={sortField}
        sortDirection={sortDirection}
        currentPage={currentPage}
        totalPages={totalPages}
        itemsPerPage={itemsPerPage}
        onSort={handleSort}
        onBenchmarkSelect={setSelectedBenchmark}
        onPageChange={setCurrentPage}
        isFullscreen={isFullscreen}
        onToggleFullscreen={() => setIsFullscreen(!isFullscreen)}
      />

      {/* Benchmark Details Dialog Component */}
      <BenchmarkDetailDialog
        benchmark={selectedBenchmark}
        isOpen={!!selectedBenchmark}
        onClose={() => setSelectedBenchmark(null)}
      />
    </div>
  );
}