# Deployment-Anweisungen für GitLab Pages

## Überblick

Diese Anleitung beschreibt die Schritte zur Bereitstellung des LLM Browser auf GitLab Pages.

## Voraussetzungen

- GitLab Account
- Lokale Git-Installation
- Node.js 18 oder höher
- API-Schlüssel für den /info Endpoint

## Schritt-für-Schritt Anleitung

### 1. GitLab Repository Setup

#### Repository erstellen
1. <PERSON>ehe zu GitLab.com
2. <PERSON><PERSON><PERSON> auf "New Project"
3. W<PERSON>hle "Create blank project"
4. Projektname: `iteratec-llm-browser`
5. Visibility: Private oder Internal (je nach Anforderung)
6. <PERSON><PERSON><PERSON> das Repository

#### Lokales Repository mit GitLab verbinden
```bash
# GitLab Remote hinzufügen
git remote add gitlab https://gitlab.com/[username]/iteratec-llm-browser.git

# Code zu GitLab pushen
git push gitlab main
```

### 2. Environment Variables konfigurieren

#### In GitLab Project Settings
1. Gehe zu **Settings** → **CI/CD**
2. Erweitere **Variables**
3. Füge folgende Variablen hinzu:

| Key | Value | Protected | Masked |
|-----|-------|-----------|--------|
| `NEXT_PUBLIC_API_KEY` | [Dein API-Schlüssel] | ✅ | ✅ |
| `NODE_ENV` | `production` | ❌ | ❌ |

### 3. GitLab Pages aktivieren

#### Pages Settings
1. Gehe zu **Settings** → **Pages**
2. GitLab Pages wird automatisch aktiviert nach dem ersten erfolgreichen Pipeline-Lauf
3. Die URL wird sein: `https://[username].gitlab.io/iteratec-llm-browser`

### 4. Ersten Deployment durchführen

#### Pipeline triggern
```bash
# Änderung committen und pushen
git add .
git commit -m "feat: setup GitLab Pages deployment"
git push gitlab main
```

#### Pipeline überwachen
1. Gehe zu **CI/CD** → **Pipelines**
2. Überwache den Build-Prozess
3. Bei Erfolg: Grüner Haken ✅
4. Bei Fehlern: Logs in der Pipeline einsehen

### 5. Deployment verifizieren

#### Site testen
1. Öffne `https://[username].gitlab.io/iteratec-llm-browser`
2. Teste alle Hauptfunktionen:
   - Model-Tabelle lädt
   - Filter funktionieren
   - Detail-Dialoge öffnen sich
   - Benchmark-Daten werden angezeigt

#### Performance prüfen
```bash
# Lokaler Build-Test
npm run build
npm run preview

# Lighthouse-Test (optional)
npx lighthouse https://[username].gitlab.io/iteratec-llm-browser --view
```

## Wartung und Updates

### Regelmäßige Daten-Updates
```bash
# Neue Daten generieren
npm run generate:data

# Änderungen committen
git add src/data/
git commit -m "data: update model and benchmark data $(date +%Y-%m-%d)"
git push gitlab main
```

### Dependency Updates
```bash
# Monatlich ausführen
npm update
npm audit fix

# Testen
npm run build

# Bei Erfolg committen
git add package*.json
git commit -m "chore: update dependencies"
git push gitlab main
```

## Troubleshooting

### Häufige Probleme

#### Build-Fehler: "API Key not found"
**Lösung**: Environment Variable `NEXT_PUBLIC_API_KEY` in GitLab CI/CD Settings prüfen

#### Build-Fehler: "generate:data failed"
**Lösung**: 
1. API-Endpoint-Verfügbarkeit prüfen
2. Fallback auf cached Daten aktivieren
3. Retry-Mechanismus in Pipeline

#### Pages nicht erreichbar
**Lösung**:
1. Pipeline erfolgreich? → CI/CD → Pipelines prüfen
2. Pages aktiviert? → Settings → Pages prüfen
3. DNS-Propagation abwarten (bis zu 24h)

#### Performance-Probleme
**Lösung**:
1. Bundle-Größe analysieren: `npm run build -- --analyze`
2. Lighthouse-Report erstellen
3. Astro Islands Optimierung prüfen

### Debug-Commands

```bash
# Lokaler Build mit Debug-Output
npm run build -- --verbose

# Preview mit Network-Logs
npm run preview -- --host 0.0.0.0

# Dependency-Analyse
npm ls --depth=0

# Cache löschen
rm -rf node_modules/.cache
rm -rf dist/
npm ci
```

## Custom Domain (Optional)

### Domain konfigurieren
1. **Settings** → **Pages**
2. **New Domain** klicken
3. Domain eingeben: `llm-browser.iteratec.de`
4. **Create New Domain**

### DNS Records setzen
```
# Bei Domain-Provider
CNAME llm-browser.iteratec.de → [username].gitlab.io
```

### SSL-Zertifikat
- Let's Encrypt wird automatisch konfiguriert
- Kann 24-48h dauern bis aktiv

## Monitoring

### GitLab Analytics
- **Analytics** → **Repository Analytics**
- **CI/CD Analytics** für Pipeline-Performance
- **Pages Analytics** für Traffic-Statistiken

### Performance Monitoring
```javascript
// Optional in src/components/Analytics.astro
<script>
  // Web Vitals Tracking
  import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';
  
  getCLS(console.log);
  getFID(console.log);
  getFCP(console.log);
  getLCP(console.log);
  getTTFB(console.log);
</script>
```

## Backup und Rollback

### Backup-Strategie
- **Git History**: Automatisches Backup durch Versionskontrolle
- **Data Snapshots**: Regelmäßige Commits der generierten Daten
- **Environment Backup**: CI/CD Variablen dokumentiert

### Rollback durchführen
```bash
# Zu vorheriger Version zurück
git log --oneline  # Commit-Hash finden
git revert [commit-hash]
git push gitlab main

# Oder kompletter Reset (Vorsicht!)
git reset --hard [commit-hash]
git push gitlab main --force
```

## Support

### Interne Ressourcen
- [`README.md`](../README.md) - Projektübersicht
- [`docs/gitlab-pages-deployment-plan.md`](gitlab-pages-deployment-plan.md) - Detaillierter Plan
- [`memory-bank/`](../memory-bank/) - Projekt-Kontext

### Externe Ressourcen
- [GitLab Pages Dokumentation](https://docs.gitlab.com/ee/user/project/pages/)
- [Astro Deployment Guide](https://docs.astro.build/en/guides/deploy/gitlab/)
- [GitLab CI/CD Dokumentation](https://docs.gitlab.com/ee/ci/)

---

**Letzte Aktualisierung**: 2025-06-06
**Version**: 1.0
**Status**: Produktionsbereit