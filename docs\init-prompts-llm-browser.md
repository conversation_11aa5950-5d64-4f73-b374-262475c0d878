# Projekt-Initialisierung
## Init-Project

```
<PERSON><PERSON><PERSON> ein Basisgerüst für ShadCN und NextJS mit einer Projekt-Initialisierung über NPM! Nutze als Zielverzeichnis das Rootverzeichnis bzw. "."

Als Komponenten aus shadcn sollten vorbereitet sein: button, input, label, select,  card,  dialog, form, sonner, tooltip, table, tabs

```


**Für Windows 11-Nutzer ergänzend**

Das Operating-System ist ein Windows 11 Rechner. Nutze Powershell als Command-Shell, wenn notwendig.

## CONTROL

Wenn nicht automatisch durch die KI gestartet, hier den aktuellen Stand starten und auch laufen lassen. Mit dem Stack kann jede Entwicklung umgehend geprüft werden im Browser.

```
yarn serve, yarn dev bzw. npm run dev je nach Setup durch die KI
```

## README 
Einfache Zwischen-Dokumentation oder bei Bedarf auch eine technische Begleitdokumentation. Für diese Komplexität ist es aber häufig ausreichend zunächst mit einer Begleit-README zu arbeiten.

```
E<PERSON>elle eine zugehörige README.md
```

# Context-Infos zu Schnittstellen aufbereiten

## LiteLLM - Auth

```
Speichere unter \docs\API\API-authentication.md die Beschreibung die Authentifizierung der API 
@https://api.iteragpt.iteratec.de/#/model%20management/model_group_info_model_group_info_get
```


## LiteLLM - Model - Infos

```
Speichere unter \docs\API\API-model_group_info.md die Beschreibung für den API-Abruf von "/model_group/info"
@https://api.iteragpt.iteratec.de/#/model%20management/model_group_info_model_group_info_get
```

## LiteLLM - Model - Garten 
```
Speichere unter  \docs\API\model_prices_and_context_window.md die Model-Beschreibungen als Beispiel von 
@https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json

Speichere dabei nur Beispiele und Hinweise zur Struktur, ohne die komplette Struktur wiederzugeben.
```

# Datenaufbereitungen

## Datenharmonisierung und Vorverarbeitungen

```
Extrahiere alle Modelle aus @https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json  und erstelle unter \docs\modeldata\model_prices_and_context_window_models.md eine Liste aller enthaltenen Models.

Es darf dabei nur der spezifische Model-Name sichtbar sein!

z.B. aus "model": "claude-3-7-sonnet-20250219 (32k thinking tokens)" sollte "modelid" : "claude-3-7-sonnet-20250219" entstehen!
```


# Init - Prompt zur Erstellung Plan

Hinweis: Hier kann gerne eine neue Session gestartet werden, um den bisherigen Chatverlauf aus den KI-Speicher und die Token-Nutzung zu entlasten!

```
Entwickle eine Anwendung "iteratec-KI-Model-Browser", die eine dynamische und umfassende Übersicht aller relevanten KI-Modelle insb. LLMs bereitstellt. Die Anwendung soll es Benutzern ermöglichen, Modelle effizient zu finden, zu vergleichen und deren Details einzusehen.

Berücksichtige das aktuelle Setup @/README.md 

**Datenquellen und -integration:**

1.  **Modell-Basisinformationen:** 

@/docs/API/API-authentication.md 
@/docs/API/API-model_group_info.md 

2.  **Modell-Kosten und Kontextfenster-Daten**

Beziehe die Kosten (Umrechnung auf 1M/Token wenn notwendig) und Kontextfenstergrößen (In/Out) aus  @/docs/API/model_prices_and_context_window.md

3.  **Datenkorrelation:** 

Finde selbst eine Variante um eine Verbindung zwischen den Informatationen herzustellen:

Model-Prices-Context-Windows enthält dabei deutlich mehr Modelle, als bei iteratec genutzt werden:

@/docs/modeldata/model_prices_and_context_window_models.md 


**Kernfunktionalitäten der Anwendung:**

1.  **Modellübersicht:**
    *   Zeige eine filterbare und sortierbare Liste/Tabelle aller Modelle an.
    *   Wichtige Sofortinformationen: Modellname, Anbieter (falls verfügbar), primärer Anwendungsfall (falls verfügbar).
    *   Visuelle Kennzeichnung, ob Kosten/Kontext-Daten live oder aus dem Cache stammen.
2.  **Detailansicht:**
    *   Biete eine detaillierte Ansicht pro Modell mit allen Informationen aus `/model/info` sowie den spezifischen Kosten- und Kontextdaten.
    *   Zeige den Zeitstempel des letzten erfolgreichen Live-Datenabrufs an.
3.  **Suche und Filterung:** Ermögliche die Suche nach Modellen (z.B. nach Name) und erweiterte Filteroptionen (z.B. nach Anbieter, Fähigkeiten, Kostenkategorie).
4.  **Fehlerbehandlung:** Implementiere eine benutzerfreundliche Fehlerbehandlung für API-Ausfälle, Netzwerkprobleme und Daten-Parsing-Fehler.

**Erstes Arbeitsergebnis: Projektplan**

Erstelle einen detaillierten Projektplan als Markdown-Datei (`app-plan.md`) im Verzeichnis `\docs\feature-init`. Dieser Plan muss mindestens folgende Abschnitte mit Checklisten enthalten:

*   **Projektziele und Umfang:** Klare Definition der App-Ziele.
*   **Systemarchitektur:** Grober Entwurf (z.B. Frontend, Backend-Services/API-Gateway, Cache-Speicher).
*   **Datenflussdiagramm:** Visualisierung von Datenbeschaffung, -verarbeitung, -zusammenführung, Caching und Anzeige.
*   **API-Integrationsstrategie:** Details zu Aufrufen, Authentifizierung, Datenmapping und Fehlerbehandlung für beide Datenquellen.
*   **Fallback-Cache-Logik:** Triggerbedingungen, Aktualisierungsstrategie, Initialisierung.
*   **UI/UX-Konzept:** Grundlegende Beschreibung oder Skizzen der Hauptansichten (Übersicht, Detailansicht, Filtermechanismen).
*   **Technologie-Stack:** Empfehlungen für Frontend, Backend (falls nötig), Caching-Lösung.
*   **Aufgabenplanung mit Checklisten:** Detaillierte Aufschlüsselung der Entwicklungsphasen und -aufgaben (z.B. Setup, API-Anbindung `/model/info`, `litellm`-Integration, Caching-Implementierung, UI-Entwicklung Übersicht, UI-Entwicklung Detailansicht, Testen, Dokumentation).
*   **Annahmen und Abhängigkeiten.**
*   **Definition of Done** für das Projekt und wesentliche Meilensteine.

```

# Run

```
Setze den Plan @/docs/feature-set-init schrittweise um! Aktualisiere die Checkliste nach relevanten Zwischenschritten!
```

# Weitere Iterationen

Ja nach Fantansie weitere Prompt-Iterationen ...

```
Erweitere die Lösung. Erweitere hierfür den Plan unter @/docs/feature-init/app-plan.md!
...
- Berücksichtige den "confidentiality" Status und bilde daraus Tabs für die Models!
- Token-Kosten sollten immer in Millionen Token dargestellt werden
- Nutze die Polyglot-Benchmark-Werte von @/static/static/polyglot-leaderboard-2025-04.json. Der Match soll über die "modelID" erfolgen!
```

## Dokumentation

```
Aktualisiere auf Basis der Entwicklung und getroffenen Entscheidungen die README.md
```

## Update Benchmark
Quelle: https://github.com/Aider-AI/aider/tree/main/aider/website/_data

```
Erstelle unter \static\polyglot-leaderboard-2025-05.json eine neue polyglot-leaderboard-benchmark-Datei als JSON-Datei auf Basis von @https://github.com/Aider-AI/aider/blob/main/aider/website/_data/polyglot_leaderboard.yml 

<JSON-Struktur-Example>
[
  {
    "model": "DeepSeek R1",
    "modelid": "deepseek-r1",
    "pass_rate_2": 56.9,
    "percent_cases_well_formed": 96.9,
    "total_cost": 5.4193,
    "command": "aider --model deepseek/deepseek-reasoner",
    "edit_format": "diff",
    "details": {
      "dirname": "2025-01-20-19-11-38--ds-turns-upd-cur-msgs-fix-with-summarizer",
      "test_cases": 225,
      "commit_hash": "5650697-dirty",
      "pass_rate_1": 26.7,
      "pass_num_1": 60,
      "pass_num_2": 128,
      "error_outputs": 8,
      "num_malformed_responses": 7,
      "num_with_malformed_responses": 7,
      "user_asks": 15,
      "lazy_comments": 0,
      "syntax_errors": 0,
      "indentation_errors": 0,
      "exhausted_context_windows": 1,
      "test_timeouts": 5,
      "total_tests": 225,
      "date": "2025-01-20",
      "versions": "0.71.2.dev",
      "seconds_per_case": 113.7
    }
  },
</JSON-Struktur-Example>
```