{"basicInfo": {"modelId": "o3-2025-04-16", "displayName": "o3", "provider": "OpenAI", "modelFamily": "o3", "version": "2025-04-16", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> von o1 - das leistungsstärkste Reasoning-<PERSON><PERSON> von OpenAI, das neue Standards für Mathematik, Wissenschaft, Coding und visuelles Reasoning setzt", "releaseDate": "2025-04-16", "status": "GA", "knowledgeCutoff": "Juni 2024"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 100000, "maxReasoningTokens": 2000000, "maxCompletionTokens": 100000, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": true, "codeExecution": true, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": false, "codeInterpreter": true, "dalleIntegration": true, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Slowest", "rateLimits": {"queriesPerMinute": 500, "tokensPerMinute": 30000, "batchQueueLimit": 90000}, "reasoningPerformance": {"averageReasoningTime": "30-120 Sekunden", "maxReasoningTime": "300 Sekunden", "reasoningEfficiency": "High"}, "temperature": {"min": 0, "max": 2, "default": 1.0}}, "pricing": {"inputCostPer1MTokens": 2.0, "outputCostPer1MTokens": 8.0, "cachingCosts": {"cacheWrites": 2.5, "cacheHits": 0.5}, "batchProcessingCosts": {"inputCostPer1MTokens": 1.0, "outputCostPer1MTokens": 4.0}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "Azure OpenAI", "OpenAI Batch API"]}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 69.1, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Ersetzt o1 ab 12.06.2025. Basierend auf Anthropic Claude 4 Vergleichsdaten"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 30.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-15", "notes": "Terminus agent framework, ±0.9% confidence interval"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 83.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Basierend auf Anthropic Claude 4 Vergleichsdaten"}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic coding", "score": 70.4, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Basierend auf Anthropic Claude 4 Vergleichsdaten"}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic coding", "score": 52.0, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Basierend auf Anthropic Claude 4 Vergleichsdaten"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 88.8, "metric": "Accuracy", "attemptType": "average", "date": "2025-05-22", "notes": "Basierend auf Anthropic Claude 4 Vergleichsdaten"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 80.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "MMMU benchmark from vals.ai"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 88.9, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Übertroffen von o4-mini (92.7%)"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 91.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Übertroffen von o4-mini (93.4%). Ersetzt o1 ab 12.06.2025."}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 85.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "AIME 2024 and 2025 combined benchmark from vals.ai"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 86.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Übertrifft o4-mini (84.3%) und o1 (71.8%)"}, {"benchmarkName": "CharXiv", "category": "Visual reasoning", "score": 78.6, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Übertrifft o4-mini (72.0%) und o1 (55.1%)"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 20.32, "alternativeScores": {"high": 20.32, "medium": 19.2}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "o3 (high): 20.32±1.58, o3 (medium): 19.20±1.54"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 79.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Aider polyglot benchmark with diff edit format (high reasoning effort)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 95.1, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1300.0, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #6"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 75.9, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}], "metadata": {"lastUpdated": "2025-06-11T08:33:00Z", "dataSource": "DataCamp o4-mini Artikel, OpenAI Documentation, Benchmark-Vergleiche, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard, OpenAI Pricing Update June 2025", "version": "1.5"}}