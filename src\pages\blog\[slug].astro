---
import { getCollection } from 'astro:content';

export async function getStaticPaths() {
  // Sammle alle deutschen Blog-Artikel für Fallback-URLs
  const blogPosts = await getCollection('blog', ({ data }) => {
    return data.lang === 'de';
  });

  return blogPosts.map((post) => ({
    params: { slug: post.slug }
  }));
}

// Fallback für bestehende Blog-URLs - leitet zur sprachspezifischen URL weiter
const { slug } = Astro.params;

// Versuche die bevorzugte Sprache aus Cookies zu ermitteln
const userLang = Astro.cookies.get('preferredLanguage')?.value || 'de';

// Weiterleitung zur sprachspezifischen URL
return Astro.redirect(`/blog/${userLang}/${slug}`);
---
