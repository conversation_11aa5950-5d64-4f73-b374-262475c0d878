{"eslint.enable": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "astro"], "eslint.format.enable": true, "eslint.codeAction.showDocumentation": {"enable": true}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "files.associations": {"*.astro": "astro"}, "[astro]": {"editor.defaultFormatter": "astro-build.astro-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}}