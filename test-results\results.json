{"numTotalTestSuites": 9, "numPassedTestSuites": 7, "numFailedTestSuites": 2, "numPendingTestSuites": 0, "numTotalTests": 30, "numPassedTests": 25, "numFailedTests": 5, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1749880491162, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["transformations utilities", "transformModelInfo"], "fullName": "transformations utilities transformModelInfo transforms basic API response correctly", "status": "passed", "title": "transforms basic API response correctly", "duration": 3.3397999999999683, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["transformations utilities", "transformModelInfo"], "fullName": "transformations utilities transformModelInfo handles missing model_info gracefully", "status": "passed", "title": "handles missing model_info gracefully", "duration": 0.5215999999998076, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["transformations utilities", "transformModelInfo"], "fullName": "transformations utilities transformModelInfo handles empty array", "status": "passed", "title": "handles empty array", "duration": 0.29399999999986903, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["transformations utilities", "transformModelInfo"], "fullName": "transformations utilities transformModelInfo generates fallback ID when missing", "status": "passed", "title": "generates fallback ID when missing", "duration": 0.2696000000000822, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["transformations utilities", "transformModelInfo"], "fullName": "transformations utilities transformModelInfo calculates cost per 1k tokens correctly", "status": "passed", "title": "calculates cost per 1k tokens correctly", "duration": 0.19289999999978136, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["transformations utilities", "transformModelInfo"], "fullName": "transformations utilities transformModelInfo handles boolean capabilities correctly", "status": "passed", "title": "handles boolean capabilities correctly", "duration": 0.19709999999986394, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["transformations utilities", "transformModelInfo"], "fullName": "transformations utilities transformModelInfo sets context window from multiple sources", "status": "passed", "title": "sets context window from multiple sources", "duration": 0.19650000000001455, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["transformations utilities", "extractModelId"], "fullName": "transformations utilities extractModelId extracts clean model ID from name", "status": "passed", "title": "extracts clean model ID from name", "duration": 0.28559999999993124, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["transformations utilities", "extractModelId"], "fullName": "transformations utilities extractModelId handles special characters", "status": "passed", "title": "handles special characters", "duration": 0.147499999999809, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["transformations utilities", "extractModelId"], "fullName": "transformations utilities extractModelId handles empty and whitespace", "status": "passed", "title": "handles empty and whitespace", "duration": 0.1618999999998323, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["transformations utilities", "processBenchmarkData"], "fullName": "transformations utilities processBenchmarkData processes benchmark data correctly", "status": "passed", "title": "processes benchmark data correctly", "duration": 1.1109999999998763, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["transformations utilities", "processBenchmarkData"], "fullName": "transformations utilities processBenchmarkData preserves existing modelid", "status": "passed", "title": "preserves existing modelid", "duration": 0.10410000000001673, "failureMessages": [], "meta": {}}], "startTime": 1749880492993, "endTime": 1749880493000.111, "status": "passed", "message": "", "name": "D:/ExG-Vise-Coding/iteratec-llm-browser/src/utils/transformations.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Button Component"], "fullName": "Button Component renders with default props", "status": "passed", "title": "renders with default props", "duration": 65.40239999999994, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Button Component"], "fullName": "Button Component renders with different variants", "status": "passed", "title": "renders with different variants", "duration": 14.967699999999923, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Button Component"], "fullName": "Button Component renders with different sizes", "status": "passed", "title": "renders with different sizes", "duration": 7.8986999999999625, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Button Component"], "fullName": "Button Component handles click events", "status": "passed", "title": "handles click events", "duration": 26.120499999999993, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Button Component"], "fullName": "Button Component can be disabled", "status": "passed", "title": "can be disabled", "duration": 4.386899999999969, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Button Component"], "fullName": "Button Component renders as different HTML elements", "status": "passed", "title": "renders as different HTML elements", "duration": 4.345399999999927, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Button Component"], "fullName": "Button Component applies custom className", "status": "passed", "title": "applies custom className", "duration": 2.594100000000026, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Button Component"], "fullName": "Button Component forwards ref correctly", "status": "passed", "title": "forwards ref correctly", "duration": 1.4039000000000215, "failureMessages": [], "meta": {}}], "startTime": 1749880493113, "endTime": 1749880493240.4038, "status": "passed", "message": "", "name": "D:/ExG-Vise-Coding/iteratec-llm-browser/src/components/ui/button.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["ModelTable Component"], "fullName": "ModelTable Component renders model data correctly", "status": "passed", "title": "renders model data correctly", "duration": 77.51869999999985, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["ModelTable Component"], "fullName": "ModelTable Component displays model capabilities correctly", "status": "failed", "title": "displays model capabilities correctly", "duration": 35.89730000000009, "failureMessages": ["TestingLibraryElementError: Unable to find an element by: [data-testid=\"/function-calling/i\"]\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"rounded-xl border bg-card text-card-foreground shadow w-full max-w-[1400px] mx-auto\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-0\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"overflow-x-auto\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"relative w-full overflow-auto\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<table\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"w-full caption-bottom text-sm min-w-full\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<caption\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"mt-4 text-sm text-muted-foreground\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<div\u001b[39m\n                  \u001b[33mclass\u001b[39m=\u001b[32m\"flex justify-between items-center p-5\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[36m<div>\u001b[39m\n                    \u001b[0mZeige \u001b[0m\n                    \u001b[0m1\u001b[0m\n                    \u001b[0m bis\u001b[0m\n                    \u001b[0m \u001b[0m\n                    \u001b[0m2\u001b[0m\n                    \u001b[0m von\u001b[0m\n                    \u001b[0m \u001b[0m\n                    \u001b[0m2\u001b[0m\n                    \u001b[0m Modellen\u001b[0m\n                  \u001b[36m</div>\u001b[39m\n                  \u001b[36m<div\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"flex gap-2\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<button\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs\"\u001b[39m\n                      \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mZurück\u001b[0m\n                    \u001b[36m</button>\u001b[39m\n                    \u001b[36m<button\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs\"\u001b[39m\n                      \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mWeiter\u001b[0m\n                    \u001b[36m</button>\u001b[39m\n                  \u001b[36m</div>\u001b[39m\n                \u001b[36m</div>\u001b[39m\n              \u001b[36m</caption>\u001b[39m\n              \u001b[36m<thead\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"[&_tr]:border-b\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<tr\u001b[39m\n                  \u001b[33mclass\u001b[39m=\u001b[32m\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-12\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mAuswählen\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mSicherheit\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mModel Card\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mLiteLLM/Status\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[200px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mName \u001b[0m\n                    \u001b[0m↑\u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[120px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mAnbieter \u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mModus\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[100px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mKontext \u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[120px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mMax Output \u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-...\n    at Object.getElementError (D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:109:15\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\src\\components\\models\\ModelTable.test.tsx:98:41\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)"], "meta": {}}, {"ancestorTitles": ["ModelTable Component"], "fullName": "ModelTable Component handles model selection", "status": "failed", "title": "handles model selection", "duration": 67.06680000000006, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ { id: 'test-model-1', …(24) } ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/expect/dist/index.js:1346:10)\n    at Proxy.<anonymous> (file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/expect/dist/index.js:1022:14)\n    at Proxy.methodWrapper (file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/chai/chai.js:1618:25)\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\src\\components\\models\\ModelTable.test.tsx:117:29\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "meta": {}}, {"ancestorTitles": ["ModelTable Component"], "fullName": "ModelTable Component handles sorting", "status": "passed", "title": "handles sorting", "duration": 45.75139999999965, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["ModelTable Component"], "fullName": "ModelTable Component displays pricing information", "status": "failed", "title": "displays pricing information", "duration": 21.478000000000065, "failureMessages": ["TestingLibraryElementError: Found multiple elements with the text: $2000.00\n\nHere are the matching elements:\n\nIgnored nodes: comments, script, style\n\u001b[36m<td\u001b[39m\n  \u001b[33mclass\u001b[39m=\u001b[32m\"p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\"\u001b[39m\n\u001b[36m>\u001b[39m\n  \u001b[0m$2000.00\u001b[0m\n\u001b[36m</td>\u001b[39m\n\nIgnored nodes: comments, script, style\n\u001b[36m<td\u001b[39m\n  \u001b[33mclass\u001b[39m=\u001b[32m\"p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\"\u001b[39m\n\u001b[36m>\u001b[39m\n  \u001b[0m$2000.00\u001b[0m\n\u001b[36m</td>\u001b[39m\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"rounded-xl border bg-card text-card-foreground shadow w-full max-w-[1400px] mx-auto\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-0\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"overflow-x-auto\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"relative w-full overflow-auto\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<table\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"w-full caption-bottom text-sm min-w-full\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<caption\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"mt-4 text-sm text-muted-foreground\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<div\u001b[39m\n                  \u001b[33mclass\u001b[39m=\u001b[32m\"flex justify-between items-center p-5\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[36m<div>\u001b[39m\n                    \u001b[0mZeige \u001b[0m\n                    \u001b[0m1\u001b[0m\n                    \u001b[0m bis\u001b[0m\n                    \u001b[0m \u001b[0m\n                    \u001b[0m2\u001b[0m\n                    \u001b[0m von\u001b[0m\n                    \u001b[0m \u001b[0m\n                    \u001b[0m2\u001b[0m\n                    \u001b[0m Modellen\u001b[0m\n                  \u001b[36m</div>\u001b[39m\n                  \u001b[36m<div\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"flex gap-2\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<button\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs\"\u001b[39m\n                      \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mZurück\u001b[0m\n                    \u001b[36m</button>\u001b[39m\n                    \u001b[36m<button\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs\"\u001b[39m\n                      \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mWeiter\u001b[0m\n                    \u001b[36m</button>\u001b[39m\n                  \u001b[36m</div>\u001b[39m\n                \u001b[36m</div>\u001b[39m\n              \u001b[36m</caption>\u001b[39m\n              \u001b[36m<thead\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"[&_tr]:border-b\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<tr\u001b[39m\n                  \u001b[33mclass\u001b[39m=\u001b[32m\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-12\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mAuswählen\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mSicherheit\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mModel Card\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mLiteLLM/Status\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[200px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mName \u001b[0m\n                    \u001b[0m↑\u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[120px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mAnbieter \u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mModus\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[100px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mKontext \u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[120px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mMax Output \u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-...\n    at Object.getElementError (D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at getElementError (D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:20:35)\n    at getMultipleElementsFoundError (D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:23:10)\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:55:13\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:95:19\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\src\\components\\models\\ModelTable.test.tsx:142:19\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)"], "meta": {}}, {"ancestorTitles": ["ModelTable Component"], "fullName": "ModelTable Component shows availability status", "status": "failed", "title": "shows availability status", "duration": 17.894700000000284, "failureMessages": ["TestingLibraryElementError: Unable to find an element by: [data-testid=\"/availability/i\"]\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"rounded-xl border bg-card text-card-foreground shadow w-full max-w-[1400px] mx-auto\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-0\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"overflow-x-auto\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"relative w-full overflow-auto\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<table\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"w-full caption-bottom text-sm min-w-full\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<caption\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"mt-4 text-sm text-muted-foreground\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<div\u001b[39m\n                  \u001b[33mclass\u001b[39m=\u001b[32m\"flex justify-between items-center p-5\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[36m<div>\u001b[39m\n                    \u001b[0mZeige \u001b[0m\n                    \u001b[0m1\u001b[0m\n                    \u001b[0m bis\u001b[0m\n                    \u001b[0m \u001b[0m\n                    \u001b[0m2\u001b[0m\n                    \u001b[0m von\u001b[0m\n                    \u001b[0m \u001b[0m\n                    \u001b[0m2\u001b[0m\n                    \u001b[0m Modellen\u001b[0m\n                  \u001b[36m</div>\u001b[39m\n                  \u001b[36m<div\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"flex gap-2\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<button\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs\"\u001b[39m\n                      \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mZurück\u001b[0m\n                    \u001b[36m</button>\u001b[39m\n                    \u001b[36m<button\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs\"\u001b[39m\n                      \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mWeiter\u001b[0m\n                    \u001b[36m</button>\u001b[39m\n                  \u001b[36m</div>\u001b[39m\n                \u001b[36m</div>\u001b[39m\n              \u001b[36m</caption>\u001b[39m\n              \u001b[36m<thead\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"[&_tr]:border-b\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<tr\u001b[39m\n                  \u001b[33mclass\u001b[39m=\u001b[32m\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-12\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mAuswählen\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mSicherheit\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mModel Card\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mLiteLLM/Status\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[200px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mName \u001b[0m\n                    \u001b[0m↑\u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[120px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mAnbieter \u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mModus\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[100px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mKontext \u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[120px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mMax Output \u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-...\n    at Object.getElementError (D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:109:15\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\src\\components\\models\\ModelTable.test.tsx:149:41\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)"], "meta": {}}, {"ancestorTitles": ["ModelTable Component"], "fullName": "ModelTable Component handles model selection toggle", "status": "passed", "title": "handles model selection toggle", "duration": 64.55279999999993, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["ModelTable Component"], "fullName": "ModelTable Component displays benchmark scores", "status": "failed", "title": "displays benchmark scores", "duration": 19.084499999999935, "failureMessages": ["TestingLibraryElementError: Unable to find an element with the text: 85.5. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"rounded-xl border bg-card text-card-foreground shadow w-full max-w-[1400px] mx-auto\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"p-0\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"overflow-x-auto\"\u001b[39m\n        \u001b[36m>\u001b[39m\n          \u001b[36m<div\u001b[39m\n            \u001b[33mclass\u001b[39m=\u001b[32m\"relative w-full overflow-auto\"\u001b[39m\n          \u001b[36m>\u001b[39m\n            \u001b[36m<table\u001b[39m\n              \u001b[33mclass\u001b[39m=\u001b[32m\"w-full caption-bottom text-sm min-w-full\"\u001b[39m\n            \u001b[36m>\u001b[39m\n              \u001b[36m<caption\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"mt-4 text-sm text-muted-foreground\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<div\u001b[39m\n                  \u001b[33mclass\u001b[39m=\u001b[32m\"flex justify-between items-center p-5\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[36m<div>\u001b[39m\n                    \u001b[0mZeige \u001b[0m\n                    \u001b[0m1\u001b[0m\n                    \u001b[0m bis\u001b[0m\n                    \u001b[0m \u001b[0m\n                    \u001b[0m2\u001b[0m\n                    \u001b[0m von\u001b[0m\n                    \u001b[0m \u001b[0m\n                    \u001b[0m2\u001b[0m\n                    \u001b[0m Modellen\u001b[0m\n                  \u001b[36m</div>\u001b[39m\n                  \u001b[36m<div\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"flex gap-2\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<button\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs\"\u001b[39m\n                      \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mZurück\u001b[0m\n                    \u001b[36m</button>\u001b[39m\n                    \u001b[36m<button\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs\"\u001b[39m\n                      \u001b[33mdisabled\u001b[39m=\u001b[32m\"\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mWeiter\u001b[0m\n                    \u001b[36m</button>\u001b[39m\n                  \u001b[36m</div>\u001b[39m\n                \u001b[36m</div>\u001b[39m\n              \u001b[36m</caption>\u001b[39m\n              \u001b[36m<thead\u001b[39m\n                \u001b[33mclass\u001b[39m=\u001b[32m\"[&_tr]:border-b\"\u001b[39m\n              \u001b[36m>\u001b[39m\n                \u001b[36m<tr\u001b[39m\n                  \u001b[33mclass\u001b[39m=\u001b[32m\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\"\u001b[39m\n                \u001b[36m>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-12\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mAuswählen\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mSicherheit\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mModel Card\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mLiteLLM/Status\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[200px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mName \u001b[0m\n                    \u001b[0m↑\u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[120px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mAnbieter \u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-10\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[36m<span\u001b[39m\n                      \u001b[33mclass\u001b[39m=\u001b[32m\"sr-only\"\u001b[39m\n                    \u001b[36m>\u001b[39m\n                      \u001b[0mModus\u001b[0m\n                    \u001b[36m</span>\u001b[39m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[100px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mKontext \u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] cursor-pointer min-w-[120px]\"\u001b[39m\n                  \u001b[36m>\u001b[39m\n                    \u001b[0mMax Output \u001b[0m\n                  \u001b[36m</th>\u001b[39m\n                  \u001b[36m<th\u001b[39m\n                    \u001b[33mclass\u001b[39m=\u001b[32m\"h-10 px-...\n    at Object.getElementError (D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:52:17\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:95:19\n    at D:\\ExG-Vise-Coding\\iteratec-llm-browser\\src\\components\\models\\ModelTable.test.tsx:171:19\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///D:/ExG-Vise-Coding/iteratec-llm-browser/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)"], "meta": {}}, {"ancestorTitles": ["ModelTable Component"], "fullName": "ModelTable Component handles empty model list", "status": "passed", "title": "handles empty model list", "duration": 9.095799999999599, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["ModelTable Component"], "fullName": "ModelTable Component applies correct CSS classes for fullscreen mode", "status": "passed", "title": "applies correct CSS classes for fullscreen mode", "duration": 15.162699999999859, "failureMessages": [], "meta": {}}], "startTime": 1749880493259, "endTime": 1749880493632.1626, "status": "failed", "message": "", "name": "D:/ExG-Vise-Coding/iteratec-llm-browser/src/components/models/ModelTable.test.tsx"}]}