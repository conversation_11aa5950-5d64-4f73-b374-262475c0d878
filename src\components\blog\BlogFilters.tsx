import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import type { BlogFilters as BlogFiltersType, BlogSearchResult, BlogMetadata } from '@/types/blog';
import { Search, Filter, X } from 'lucide-react';

interface BlogFiltersProps {
  filters: BlogFiltersType;
  searchQuery: string;
  searchResult?: BlogSearchResult;
  initialMetadata?: BlogMetadata;
  onFiltersChange: (filters: BlogFiltersType) => void;
  onSearchChange: (query: string) => void;
  onClearFilters: () => void;
}

export function BlogFilters({
  filters,
  searchQuery,
  searchResult,
  initialMetadata,
  onFiltersChange,
  onSearchChange,
  onClearFilters
}: BlogFiltersProps) {
  const categoryLabels = {
    'model-analysis': 'Modell-Analyse',
    'release-notes': 'Release Notes',
    'benchmark-analysis': 'Benchmark-Analyse',
    'industry-news': 'Branchen-News'
  };

  const handleCategoryClick = (category: string) => {
    const newCategory = filters.category === category ? undefined : category;
    onFiltersChange({ ...filters, category: newCategory });
  };

  const handleTagClick = (tag: string) => {
    const currentTags = filters.tags || [];
    const newTags = currentTags.includes(tag)
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag];
    onFiltersChange({ ...filters, tags: newTags.length > 0 ? newTags : undefined });
  };

  const handleFeaturedToggle = () => {
    const newFeatured = filters.featured === true ? undefined : true;
    onFiltersChange({ ...filters, featured: newFeatured });
  };

  const hasActiveFilters = filters.category || (filters.tags && filters.tags.length > 0) || filters.featured;

  return (
    <div className="space-y-4">
      {/* Search */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Search className="w-4 h-4" />
            Suche
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Blog-Artikel durchsuchen..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-9"
            />
          </div>
        </CardContent>
      </Card>

      {/* Active Filters */}
      {hasActiveFilters && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Filter className="w-4 h-4" />
                Aktive Filter
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearFilters}
                className="h-auto p-1"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex flex-wrap gap-2">
              {filters.category && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                  {categoryLabels[filters.category as keyof typeof categoryLabels]}
                  <button
                    onClick={() => filters.category && handleCategoryClick(filters.category)}
                    className="hover:bg-blue-200 rounded-full p-0.5"
                    title="Filter entfernen"
                    aria-label="Kategorie-Filter entfernen"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              )}
              {filters.tags?.map(tag => (
                <span key={tag} className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">
                  {tag}
                  <button
                    onClick={() => handleTagClick(tag)}
                    className="hover:bg-gray-200 rounded-full p-0.5"
                    title={`Tag "${tag}" entfernen`}
                    aria-label={`Tag "${tag}" entfernen`}
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              ))}
              {filters.featured && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                  Featured
                  <button
                    onClick={handleFeaturedToggle}
                    className="hover:bg-yellow-200 rounded-full p-0.5"
                    title="Featured-Filter entfernen"
                    aria-label="Featured-Filter entfernen"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Categories */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Kategorien</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            {Object.entries(categoryLabels).map(([category, label]) => {
              // Use searchResult facets if available, otherwise fall back to initial metadata
              const count = searchResult?.facets.categories[category] || initialMetadata?.categories[category] || 0;
              const isActive = filters.category === category;
              
              return (
                <button
                  key={category}
                  onClick={() => handleCategoryClick(category)}
                  disabled={count === 0}
                  className={`w-full flex items-center justify-between p-2 rounded text-sm transition-colors ${
                    isActive 
                      ? 'bg-blue-100 text-blue-900 font-medium' 
                      : count > 0 
                        ? 'hover:bg-gray-100 text-gray-700' 
                        : 'text-gray-400 cursor-not-allowed'
                  }`}
                >
                  <span>{label}</span>
                  <span className={`text-xs ${isActive ? 'text-blue-600' : 'text-gray-500'}`}>
                    {count}
                  </span>
                </button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Popular Tags */}
      {((searchResult && Object.keys(searchResult.facets.tags).length > 0) ||
        (initialMetadata && Object.keys(initialMetadata.tags).length > 0)) && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Tags</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex flex-wrap gap-2">
              {Object.entries(searchResult?.facets.tags || initialMetadata?.tags || {})
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10)
                .map(([tag, count]) => {
                  const isActive = filters.tags?.includes(tag);
                  return (
                    <button
                      key={tag}
                      onClick={() => handleTagClick(tag)}
                      className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs transition-colors ${
                        isActive
                          ? 'bg-blue-100 text-blue-800 font-medium'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      {tag}
                      <span className="text-xs opacity-75">({count})</span>
                    </button>
                  );
                })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Featured Toggle */}
      <Card>
        <CardContent className="py-3">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={filters.featured === true}
              onChange={handleFeaturedToggle}
              className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
            />
            <span className="text-sm text-gray-700">Nur Featured Artikel</span>
          </label>
        </CardContent>
      </Card>
    </div>
  );
}