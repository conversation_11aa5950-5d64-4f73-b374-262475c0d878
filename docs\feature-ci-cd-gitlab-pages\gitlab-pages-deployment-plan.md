# GitLab Pages Deployment Plan für LLM Browser

## Überblick

Dieser Plan beschreibt die konkreten Schritte zur Veröffentlichung des LLM Browser Projekts auf GitLab Pages, basierend auf der bereits erfolgreich durchgeführten Astro-Migration.

## Aktueller Status

✅ **Astro Migration abgeschlossen** (Phasen 1-4)
- Astro-Framework mit React Islands implementiert
- Statische Datengenerierung zur Build-Zeit
- GitLab Pages-kompatible Architektur
- Entwicklungsserver läuft erfolgreich auf http://localhost:4321

## Deployment-Strategie

### 1. Repository-Vorbereitung

#### 1.1 GitLab Repository Setup
```bash
# Repository auf GitLab erstellen oder migrieren
git remote add gitlab https://gitlab.com/[username]/iteratec-llm-browser.git
git push gitlab main
```

#### 1.2 Branch-Struktur
- **main**: Produktions-Branch für GitLab Pages
- **develop**: Entwicklungs-Branch
- **feature/***: Feature-Branches

### 2. GitLab CI/CD Pipeline Konfiguration

#### 2.1 `.gitlab-ci.yml` erstellen
```yaml
# GitLab CI/CD Pipeline für Astro Static Site
image: node:18

stages:
  - build
  - deploy

variables:
  NODE_ENV: production

cache:
  paths:
    - node_modules/

before_script:
  - npm ci

# Build Stage
build:
  stage: build
  script:
    - npm run generate:data  # Statische Daten generieren
    - npm run build         # Astro Build
  artifacts:
    paths:
      - dist/
    expire_in: 1 hour
  only:
    - main

# Deploy to GitLab Pages
pages:
  stage: deploy
  script:
    - mkdir public
    - cp -r dist/* public/
  artifacts:
    paths:
      - public
  dependencies:
    - build
  only:
    - main
```

#### 2.2 Environment Variables
In GitLab Project Settings → CI/CD → Variables:
```
NEXT_PUBLIC_API_KEY: [API-Schlüssel für /info Endpoint]
NODE_ENV: production
```

### 3. Build-Optimierung für GitLab Pages

#### 3.1 Astro Konfiguration anpassen
```javascript
// astro.config.mjs
export default defineConfig({
  site: 'https://[username].gitlab.io',
  base: '/iteratec-llm-browser',
  output: 'static',
  integrations: [
    react(),
    tailwind()
  ],
  build: {
    assets: '_assets'
  }
});
```

#### 3.2 Package.json Scripts erweitern
```json
{
  "scripts": {
    "generate:data": "node scripts/generate-static-data.ts",
    "prebuild": "npm run generate:data",
    "build": "astro build",
    "preview": "astro preview",
    "deploy:gitlab": "npm run build && echo 'Ready for GitLab Pages'"
  }
}
```

### 4. Statische Daten-Pipeline

#### 4.1 Build-Time Data Generation
```typescript
// scripts/generate-static-data.ts
// Bereits implementiert - generiert:
// - src/data/models.json (27 Modelle)
// - src/data/benchmarks.json (55 Benchmarks)
// - src/data/enriched-models.json (Kombinierte Daten)
// - src/data/statistics.json (Statistiken)
```

#### 4.2 API-Daten Caching
- **Build-Zeit**: API-Daten werden zur Build-Zeit abgerufen
- **Statische Dateien**: Alle Daten in JSON-Dateien gespeichert
- **Keine Runtime-API-Calls**: Vollständig statische Site

### 5. Performance-Optimierung

#### 5.1 Astro Islands Optimierung
```typescript
// Nur interaktive Komponenten als Islands
<ModelTableIsland client:load />
<BenchmarkTableIsland client:visible />
```

#### 5.2 Bundle-Optimierung
- **Tree Shaking**: Automatisch durch Astro
- **Code Splitting**: Islands werden separat geladen
- **CSS Purging**: Tailwind CSS automatisch optimiert

### 6. Domain und SSL

#### 6.1 Custom Domain (Optional)
```
# In GitLab Pages Settings
Custom Domain: llm-browser.iteratec.de
SSL Certificate: Let's Encrypt (automatisch)
```

#### 6.2 DNS Konfiguration
```
# DNS Records für Custom Domain
CNAME: llm-browser.iteratec.de → [username].gitlab.io
```

### 7. Monitoring und Analytics

#### 7.1 GitLab Pages Analytics
- **Built-in Analytics**: GitLab bietet grundlegende Statistiken
- **Custom Analytics**: Google Analytics oder Plausible integrierbar

#### 7.2 Performance Monitoring
```javascript
// Optional: Web Vitals Tracking
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);
```

### 8. Deployment-Workflow

#### 8.1 Automatisches Deployment
1. **Code Push** → main Branch
2. **CI Pipeline** → Build & Test
3. **Data Generation** → Statische Daten erstellen
4. **Astro Build** → Optimierte statische Site
5. **GitLab Pages** → Automatisches Deployment

#### 8.2 Manuelles Deployment
```bash
# Lokaler Build-Test
npm run build
npm run preview

# Deployment
git add .
git commit -m "feat: update model data"
git push gitlab main
```

### 9. Wartung und Updates

#### 9.1 Daten-Updates
```bash
# Regelmäßige Daten-Updates
npm run generate:data  # Neue API-Daten abrufen
git add src/data/
git commit -m "data: update model and benchmark data"
git push gitlab main
```

#### 9.2 Dependency Updates
```bash
# Monatliche Updates
npm update
npm audit fix
npm run build  # Test nach Updates
```

### 10. Backup und Rollback

#### 10.1 Backup-Strategie
- **Git History**: Vollständige Versionskontrolle
- **Data Snapshots**: Regelmäßige Commits der generierten Daten
- **Environment Backup**: CI/CD Variablen dokumentiert

#### 10.2 Rollback-Prozess
```bash
# Rollback zu vorheriger Version
git revert [commit-hash]
git push gitlab main
```

## Zeitplan

### Sofortige Schritte (1-2 Tage)
1. ✅ GitLab Repository erstellen/migrieren
2. ✅ `.gitlab-ci.yml` konfigurieren
3. ✅ Environment Variables setzen
4. ✅ Ersten Deployment-Test durchführen

### Kurzfristig (1 Woche)
1. ✅ Custom Domain konfigurieren (falls gewünscht)
2. ✅ SSL-Zertifikat einrichten
3. ✅ Performance-Tests durchführen
4. ✅ Monitoring einrichten

### Mittelfristig (2-4 Wochen)
1. 📋 Automatisierte Daten-Updates implementieren
2. 📋 Erweiterte Analytics integrieren
3. 📋 Backup-Strategien etablieren
4. 📋 Dokumentation für Team erstellen

## Erfolgskriterien

### Technische Kriterien
- ✅ **Build-Zeit**: < 2 Minuten
- ✅ **Page Load**: < 3 Sekunden (First Contentful Paint)
- ✅ **Lighthouse Score**: > 90 (Performance, Accessibility, SEO)
- ✅ **Uptime**: > 99.9%

### Funktionale Kriterien
- ✅ **Alle Features funktional**: Model-Tabelle, Benchmark-Ansicht, Filter
- ✅ **Responsive Design**: Mobile und Desktop optimiert
- ✅ **Daten-Aktualität**: Automatische Updates möglich
- ✅ **SEO-Optimierung**: Meta-Tags und strukturierte Daten

## Risiken und Mitigation

### Risiko 1: API-Verfügbarkeit zur Build-Zeit
**Mitigation**: Fallback auf cached Daten, Retry-Mechanismus

### Risiko 2: Build-Pipeline Fehler
**Mitigation**: Umfassende Tests, Staging-Environment

### Risiko 3: Performance-Probleme
**Mitigation**: Bundle-Analyse, Progressive Loading

### Risiko 4: Daten-Inkonsistenzen
**Mitigation**: Validierung in Data-Generation-Script

## Nächste Schritte

1. **Repository Setup**: GitLab Repository erstellen und Code migrieren
2. **CI/CD Pipeline**: `.gitlab-ci.yml` implementieren und testen
3. **Environment Setup**: API-Keys und Variablen konfigurieren
4. **Deployment Test**: Ersten Build und Deployment durchführen
5. **Domain Setup**: Custom Domain konfigurieren (optional)
6. **Go-Live**: Produktive Freischaltung

## Support und Dokumentation

### Interne Dokumentation
- **README.md**: Aktualisiert mit GitLab Pages Informationen
- **DEPLOYMENT.md**: Detaillierte Deployment-Anweisungen
- **TROUBLESHOOTING.md**: Häufige Probleme und Lösungen

### Externe Ressourcen
- [GitLab Pages Dokumentation](https://docs.gitlab.com/ee/user/project/pages/)
- [Astro Deployment Guide](https://docs.astro.build/en/guides/deploy/gitlab/)
- [Astro Static Site Generation](https://docs.astro.build/en/guides/static-site-generation/)

---

**Status**: ✅ Bereit für Implementierung
**Letzte Aktualisierung**: 2025-06-06
**Verantwortlich**: Flow-Code Mode