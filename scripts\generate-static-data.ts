import { promises as fs } from 'fs';
import { join, dirname } from 'path';
import { config } from 'dotenv';
import { fetchCombinedModelData, fetchModelInfo, type ApiConfig } from '../src/services/api.js';

// Load environment variables from .env.local
config({ path: '.env.local' });
import { deduplicateBenchmarkData, deduplicateModelData } from '../src/utils/deduplication.js';
import { enrichModelDataWithBenchmark, getBenchmarkStatistics } from '../src/utils/matching.js';
import { 
  processBenchmarkData, 
  createStaticModelData, 
  createStaticBenchmarkData,
  generateModelStatistics 
} from '../src/utils/transformations.js';
import type { BenchmarkData, ModelData, StaticModelData, StaticBenchmarkData } from '../src/types/api.js';

// Model Card interface based on the JSON schema
export interface ModelCard {
  basicInfo: {
    modelId: string;
    displayName: string;
    provider: string;
    modelFamily?: string;
    version?: string;
    description?: string;
    releaseDate?: string;
    status?: string;
    knowledgeCutoff?: string;
    deprecationDate?: string;
    shutdownDate?: string;
  };
  technicalSpecs: {
    contextWindow: number;
    maxOutputTokens: number;
    maxReasoningTokens?: number;
    maxCompletionTokens?: number;
    architecture?: string;
    parameterCount?: string;
    supportedInputTypes?: string[];
    supportedOutputTypes?: string[];
    inputLimitations?: any;
  };
  capabilities: {
    functionCalling?: boolean;
    vision?: boolean;
    pdfSupport?: boolean;
    audioInput?: boolean;
    audioOutput?: boolean;
    imageGeneration?: boolean;
    codeExecution?: boolean;
    systemInstructions?: boolean;
    promptCaching?: boolean;
    batchProcessing?: boolean;
    reasoning?: boolean;
    thinking?: boolean;
    grounding?: boolean;
    multilingualSupport?: boolean;
    embeddingImageInput?: boolean;
    structuredOutputs?: boolean;
    webBrowsing?: boolean;
    codeInterpreter?: boolean;
    dalleIntegration?: boolean;
    realTimeAPI?: boolean;
    "liteLLM-provisioning"?: boolean;
  };
  performance?: {
    latency?: string;
    rateLimits?: any;
    reasoningPerformance?: any;
    temperature?: any;
    topP?: number;
    topK?: number;
  };
  pricing?: {
    inputCostPer1MTokens?: number;
    outputCostPer1MTokens?: number;
    cachingCosts?: any;
    reasoningCosts?: any;
    batchProcessingCosts?: any;
    currency?: string;
  };
  availability?: {
    regions?: any[];
    dataProcessingRegions?: string[];
    supportedPlatforms?: string[];
    platformSpecificIds?: any;
  };
  security?: any;
  usageTypes?: any;
  benchmarks?: any[];
  metadata?: {
    lastUpdated?: string;
    dataSource?: string;
    version?: string;
    variants?: any[];
  };
}

// Configuration
const API_CONFIG: ApiConfig = {
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || process.env.API_BASE_URL || "https://api.iteragpt.iteratec.de",
  apiKey: process.env.NEXT_PUBLIC_API_KEY || process.env.API_KEY || "",
  useMockData: process.env.USE_MOCK_DATA === "true" || false // Use real API since we have the key
};

const OUTPUT_DIR = join(process.cwd(), 'src', 'data');
const BENCHMARK_DIR = join(process.cwd(), 'src', 'data', 'benchmarks');
const MODELS_DIR = join(process.cwd(), 'src', 'data', 'models');
const MODEL_MAPPINGS_PATH = join(process.cwd(), 'src', 'data', 'models', 'model-mappings.json');

// Ensure output directories exist
async function ensureDirectories() {
  try {
    await fs.mkdir(OUTPUT_DIR, { recursive: true });
    await fs.mkdir(BENCHMARK_DIR, { recursive: true });
    console.log('[BUILD] Output directories ensured');
  } catch (error) {
    console.error('[BUILD] Error creating directories:', error);
    throw error;
  }
}

// Load model mappings from JSON file
async function loadModelMappings(): Promise<any> {
  console.log('[BUILD] Loading model mappings...');
  
  try {
    const fileContent = await fs.readFile(MODEL_MAPPINGS_PATH, 'utf-8');
    const mappings = JSON.parse(fileContent);
    console.log(`[BUILD] Loaded model mappings with ${Object.keys(mappings.mappings || {}).length} entries`);
    return mappings;
  } catch (error) {
    console.warn('[BUILD] Could not load model mappings:', error);
    return { mappings: {} };
  }
}

// Load model cards from JSON files using mappings
async function loadModelCards(): Promise<ModelCard[]> {
  console.log('[BUILD] Loading model cards from local files...');
  
  const allModelCards: ModelCard[] = [];
  
  try {
    // Load model mappings to determine which files to load
    const modelMappings = await loadModelMappings();
    const mappings = modelMappings.mappings || {};
    
    // Get unique model card files from mappings
    const uniqueModelCardFiles = new Set<string>();
    Object.values(mappings).forEach((mapping: any) => {
      if (mapping.modelCardFile) {
        uniqueModelCardFiles.add(mapping.modelCardFile);
      }
    });
    
    console.log(`[BUILD] Found ${uniqueModelCardFiles.size} unique model card files in mappings`);
    
    // Load model card files from mappings
    for (const filename of Array.from(uniqueModelCardFiles)) {
      try {
        const filePath = join(MODELS_DIR, filename);
        const fileContent = await fs.readFile(filePath, 'utf-8');
        const modelCard = JSON.parse(fileContent) as ModelCard;
        
        console.log(`[BUILD] Loaded model card: ${modelCard.basicInfo.displayName} from ${filename}`);
        allModelCards.push(modelCard);
      } catch (error) {
        console.warn(`[BUILD] Could not load model card file ${filename}:`, error);
      }
    }
    
    // Also check for any additional JSON files not in mappings (for backward compatibility)
    try {
      const files = await fs.readdir(MODELS_DIR);
      const jsonFiles = files.filter(file =>
        file.endsWith('.json') &&
        !file.includes('mappings') &&
        !file.includes('schema') &&
        !uniqueModelCardFiles.has(file)
      );
      
      if (jsonFiles.length > 0) {
        console.log(`[BUILD] Found ${jsonFiles.length} additional model card files not in mappings`);
        
        for (const filename of jsonFiles) {
          try {
            const filePath = join(MODELS_DIR, filename);
            const fileContent = await fs.readFile(filePath, 'utf-8');
            const modelCard = JSON.parse(fileContent) as ModelCard;
            
            console.log(`[BUILD] Loaded additional model card: ${modelCard.basicInfo.displayName} from ${filename}`);
            allModelCards.push(modelCard);
          } catch (error) {
            console.warn(`[BUILD] Could not load additional model card file ${filename}:`, error);
          }
        }
      }
    } catch (error) {
      console.warn('[BUILD] Could not read models directory for additional files:', error);
    }
    
    console.log(`[BUILD] Total model cards loaded: ${allModelCards.length}`);
  } catch (error) {
    console.warn('[BUILD] Error loading model cards:', error);
  }
  
  return allModelCards;
}

// Convert model cards to ModelData format using mappings to create multiple model entries
async function convertModelCardsToModelData(modelCards: ModelCard[]): Promise<ModelData[]> {
  console.log('[BUILD] Converting model cards to ModelData format with full information preservation...');
  
  // Load model mappings
  const modelMappings = await loadModelMappings();
  const mappings = modelMappings.mappings || {};
  
  const allModelData: ModelData[] = [];
  
  // Create a map from modelCardFile to ModelCard for easy lookup
  const cardsByFile = new Map<string, ModelCard>();
  modelCards.forEach(card => {
    // Find which file this card came from by matching modelId
    for (const [mappingKey, mapping] of Object.entries(mappings)) {
      if ((mapping as any).modelCardId === card.basicInfo.modelId) {
        cardsByFile.set((mapping as any).modelCardFile, card);
        break;
      }
    }
    // Also add with a fallback key based on card ID
    cardsByFile.set(`${card.basicInfo.modelId}.json`, card);
  });
  
  // Process each mapping to create ModelData entries
  for (const [mappingKey, mapping] of Object.entries(mappings)) {
    const typedMapping = mapping as any;
    
    if (!typedMapping.modelCardFile || !typedMapping.modelCardId) {
      console.warn(`[BUILD] Invalid mapping for ${mappingKey}: missing modelCardFile or modelCardId`);
      continue;
    }
    
    // Find the corresponding model card
    const card = cardsByFile.get(typedMapping.modelCardFile);
    if (!card) {
      console.warn(`[BUILD] No model card found for mapping ${mappingKey} -> ${typedMapping.modelCardFile}`);
      continue;
    }
    
    // Create ModelData with ALL information from the model card
    const modelData: ModelData = {
      // Use the mapping key as the ID for this specific model variant
      id: mappingKey,
      name: card.basicInfo.displayName,
      provider: card.basicInfo.provider,
      description: card.basicInfo.description || '',
      
      // Technical specs mapping
      contextWindow: card.technicalSpecs.contextWindow,
      maxOutputTokens: card.technicalSpecs.maxOutputTokens,
      maxTokens: card.technicalSpecs.maxOutputTokens,
      maxInputTokens: card.technicalSpecs.contextWindow,
      maxReasoningTokens: card.technicalSpecs.maxReasoningTokens,
      maxCompletionTokens: card.technicalSpecs.maxCompletionTokens,
      
      // Capabilities mapping with complete coverage
      supportsFunctionCalling: card.capabilities.functionCalling ?? null,
      supportsVision: card.capabilities.vision ?? null,
      supportsPdfInput: card.capabilities.pdfSupport ?? null,
      supportsAudioInput: card.capabilities.audioInput ?? null,
      supportsAudioOutput: card.capabilities.audioOutput ?? null,
      supportsSystemMessages: card.capabilities.systemInstructions ?? null,
      supportsPromptCaching: card.capabilities.promptCaching ?? null,
      supportsReasoning: card.capabilities.reasoning ?? null,
      supportsWebSearch: card.capabilities.webBrowsing ?? null,
      supportsNativeStreaming: card.capabilities.codeInterpreter ?? null,
      supportsImageGeneration: card.capabilities.imageGeneration ?? null,
      supportsCodeExecution: card.capabilities.codeExecution ?? null,
      supportsBatchProcessing: card.capabilities.batchProcessing ?? null,
      supportsThinking: card.capabilities.thinking ?? null,
      supportsGrounding: card.capabilities.grounding ?? null,
      supportsMultilingual: card.capabilities.multilingualSupport ?? null,
      supportsStructuredOutputs: card.capabilities.structuredOutputs ?? null,
      supportsRealTimeAPI: card.capabilities.realTimeAPI ?? null,
      'liteLLM-provisioning': card.capabilities['liteLLM-provisioning'] ?? true,
      
      // Cost mapping
      inputCostPerToken: card.pricing?.inputCostPer1MTokens ? card.pricing.inputCostPer1MTokens / 1000000 : null,
      outputCostPerToken: card.pricing?.outputCostPer1MTokens ? card.pricing.outputCostPer1MTokens / 1000000 : null,
      inputCostPer1kTokens: card.pricing?.inputCostPer1MTokens ? card.pricing.inputCostPer1MTokens / 1000 : undefined,
      outputCostPer1kTokens: card.pricing?.outputCostPer1MTokens ? card.pricing.outputCostPer1MTokens / 1000 : undefined,
      
      // Status and lifecycle mapping
      status: card.basicInfo.status,
      deprecationDate: card.basicInfo.deprecationDate,
      shutdownDate: card.basicInfo.shutdownDate,
      
      // Additional standard properties
      key: mappingKey,
      isAvailable: card.basicInfo.status === 'GA' || card.basicInfo.status === 'Preview',
      modelGroup: card.basicInfo.modelFamily || '',
      mode: 'chat', // Default mode for model cards
      confidentiality: 'external', // Default for model cards
      
      // Enhanced display name that includes provider context
      displayName: card.basicInfo.displayName,
      
      // Mapping information for traceability
      mappingInfo: {
        mappingKey: mappingKey,
        modelCardFile: typedMapping.modelCardFile,
        modelCardId: typedMapping.modelCardId
      },
      
      // Add metadata to distinguish model cards
      _isModelCard: true,
      _modelCardSource: card.metadata?.dataSource || 'model-card',
      _modelCardVersion: card.metadata?.version || '1.0',
      _enrichedWithMapping: true
    } as ModelData & {
      _isModelCard: boolean;
      _modelCardSource: string;
      _modelCardVersion: string;
      _enrichedWithMapping: boolean;
      mappingInfo: {
        mappingKey: string;
        modelCardFile: string;
        modelCardId: string;
      };
    };
    
    console.log(`[BUILD] Created model entry: ${mappingKey} -> ${card.basicInfo.displayName}`);
    allModelData.push(modelData);
  }
  
  // Also process any model cards that don't have mappings (for backward compatibility)
  for (const card of modelCards) {
    const hasMapping = Object.values(mappings).some((mapping: any) =>
      mapping.modelCardId === card.basicInfo.modelId
    );
    
    if (!hasMapping) {
      console.log(`[BUILD] Creating unmapped model entry for: ${card.basicInfo.displayName}`);
      
      const modelData: ModelData = {
        // Use the card's model ID directly
        id: card.basicInfo.modelId,
        name: card.basicInfo.displayName,
        provider: card.basicInfo.provider,
        description: card.basicInfo.description || '',
        
        // Technical specs mapping
        contextWindow: card.technicalSpecs.contextWindow,
        maxOutputTokens: card.technicalSpecs.maxOutputTokens,
        maxTokens: card.technicalSpecs.maxOutputTokens,
        maxInputTokens: card.technicalSpecs.contextWindow,
        maxReasoningTokens: card.technicalSpecs.maxReasoningTokens,
        maxCompletionTokens: card.technicalSpecs.maxCompletionTokens,
        
        // Full capabilities mapping
        supportsFunctionCalling: card.capabilities.functionCalling ?? null,
        supportsVision: card.capabilities.vision ?? null,
        supportsPdfInput: card.capabilities.pdfSupport ?? null,
        supportsAudioInput: card.capabilities.audioInput ?? null,
        supportsAudioOutput: card.capabilities.audioOutput ?? null,
        supportsSystemMessages: card.capabilities.systemInstructions ?? null,
        supportsPromptCaching: card.capabilities.promptCaching ?? null,
        supportsReasoning: card.capabilities.reasoning ?? null,
        supportsWebSearch: card.capabilities.webBrowsing ?? null,
        supportsNativeStreaming: card.capabilities.codeInterpreter ?? null,
        supportsImageGeneration: card.capabilities.imageGeneration ?? null,
        supportsCodeExecution: card.capabilities.codeExecution ?? null,
        supportsBatchProcessing: card.capabilities.batchProcessing ?? null,
        supportsThinking: card.capabilities.thinking ?? null,
        supportsGrounding: card.capabilities.grounding ?? null,
        supportsMultilingual: card.capabilities.multilingualSupport ?? null,
        supportsStructuredOutputs: card.capabilities.structuredOutputs ?? null,
        supportsRealTimeAPI: card.capabilities.realTimeAPI ?? null,
        'liteLLM-provisioning': card.capabilities['liteLLM-provisioning'] ?? true,
        
        // Cost mapping
        inputCostPerToken: card.pricing?.inputCostPer1MTokens ? card.pricing.inputCostPer1MTokens / 1000000 : null,
        outputCostPerToken: card.pricing?.outputCostPer1MTokens ? card.pricing.outputCostPer1MTokens / 1000000 : null,
        inputCostPer1kTokens: card.pricing?.inputCostPer1MTokens ? card.pricing.inputCostPer1MTokens / 1000 : undefined,
        outputCostPer1kTokens: card.pricing?.outputCostPer1MTokens ? card.pricing.outputCostPer1MTokens / 1000 : undefined,
        
        // Status and lifecycle mapping
        status: card.basicInfo.status,
        deprecationDate: card.basicInfo.deprecationDate,
        shutdownDate: card.basicInfo.shutdownDate,
        
        // Additional properties
        key: card.basicInfo.displayName,
        isAvailable: card.basicInfo.status === 'GA' || card.basicInfo.status === 'Preview',
        modelGroup: card.basicInfo.modelFamily || '',
        mode: 'chat',
        confidentiality: 'external',
        displayName: card.basicInfo.displayName,
        
        // Add metadata
        _isModelCard: true,
        _modelCardSource: card.metadata?.dataSource || 'model-card',
        _modelCardVersion: card.metadata?.version || '1.0',
        _enrichedWithMapping: false
      } as ModelData & {
        _isModelCard: boolean;
        _modelCardSource: string;
        _modelCardVersion: string;
        _enrichedWithMapping: boolean;
      };
      
      allModelData.push(modelData);
    }
  }
  
  console.log(`[BUILD] Converted ${allModelData.length} model entries from ${modelCards.length} model cards`);
  console.log(`[BUILD] - ${allModelData.filter((m: any) => m._enrichedWithMapping).length} with mappings`);
  console.log(`[BUILD] - ${allModelData.filter((m: any) => !m._enrichedWithMapping).length} without mappings`);
  
  return allModelData;
}

// Load benchmark data from JSON files
async function loadBenchmarkData(): Promise<BenchmarkData[]> {
  console.log('[BUILD] Loading benchmark data from local files...');
  
  // Read all files from benchmark directory
  const files = await fs.readdir(BENCHMARK_DIR);
  const benchmarkFiles = files.filter(file =>
    file.endsWith('.json') &&
    !file.includes('README') &&
    file.startsWith('polyglot-leaderboard-')
  );
  
  console.log(`[BUILD] Found ${benchmarkFiles.length} benchmark files: ${benchmarkFiles.join(', ')}`);
  
  const allBenchmarkData: BenchmarkData[] = [];
  
  for (const filename of benchmarkFiles) {
    try {
      const filePath = join(BENCHMARK_DIR, filename);
      const fileContent = await fs.readFile(filePath, 'utf-8');
      const data = JSON.parse(fileContent) as BenchmarkData[];
      
      console.log(`[BUILD] Loaded ${data.length} benchmark entries from ${filename}`);
      allBenchmarkData.push(...data);
    } catch (error) {
      console.warn(`[BUILD] Could not load benchmark file ${filename}:`, error);
    }
  }
  
  console.log(`[BUILD] Total benchmark entries loaded: ${allBenchmarkData.length}`);
  
  // Process benchmark data to ensure modelid exists
  const processedData = processBenchmarkData(allBenchmarkData);
  
  // Deduplicate benchmark data
  const deduplicatedData = deduplicateBenchmarkData(processedData);
  
  return deduplicatedData;
}

// Generate static model data (EXCLUDES model cards from models.json)
async function generateStaticModelData(): Promise<StaticModelData> {
  console.log('[BUILD] Generating static model data (excluding model cards)...');
  
  let apiModels: ModelData[] = [];
  
  try {
    // Fetch model data from API or mock
    try {
      const response = await fetchCombinedModelData(API_CONFIG);
      
      if (response.status === 200) {
        // Ensure we have an array of models
        apiModels = Array.isArray(response.data) ? response.data : [];
        
        // Transform confidentiality "open" and "external" to "public"
        const modelsWithOpenConfidentiality = apiModels.filter(model => model.confidentiality === 'open').length;
        const modelsWithExternalConfidentiality = apiModels.filter(model => model.confidentiality === 'external').length;
        apiModels = apiModels.map(model => ({
          ...model,
          confidentiality: (model.confidentiality === 'open' || model.confidentiality === 'external') ? 'public' : model.confidentiality
        }));
        
        if (modelsWithOpenConfidentiality > 0) {
          console.log(`[BUILD] Transformed ${modelsWithOpenConfidentiality} models from confidentiality "open" to "public"`);
        }
        if (modelsWithExternalConfidentiality > 0) {
          console.log(`[BUILD] Transformed ${modelsWithExternalConfidentiality} models from confidentiality "external" to "public"`);
        }
        
        console.log(`[BUILD] Successfully fetched ${apiModels.length} models from API`);
      } else {
        console.warn(`[BUILD] API request failed: ${response.message}`);
      }
    } catch (apiError) {
      console.warn('[BUILD] Error fetching from API:', apiError);
    }
    
    // Only use API models for models.json (no model cards)
    if (apiModels.length === 0) {
      console.warn('[BUILD] No API model data available');
      return createStaticModelData([], 'cache');
    }
    
    // Deduplicate API model data
    const deduplicatedApiModels = deduplicateModelData(apiModels);
    
    console.log(`[BUILD] Using ${deduplicatedApiModels.length} API models for models.json (model cards excluded)`);
    
    // Determine source based on API data only
    const source: 'api' | 'mock' | 'cache' = API_CONFIG.useMockData ? 'mock' : 'api';
    
    return createStaticModelData(deduplicatedApiModels, source);
  } catch (error) {
    console.error('[BUILD] Error generating static model data:', error);
    
    // Return empty data structure on error (no fallback to model cards)
    return createStaticModelData([], 'cache');
  }
}

// Generate static benchmark data
async function generateStaticBenchmarkData(): Promise<StaticBenchmarkData> {
  console.log('[BUILD] Generating static benchmark data...');
  
  try {
    const benchmarkData = await loadBenchmarkData();
    return createStaticBenchmarkData(benchmarkData, 'latest');
  } catch (error) {
    console.error('[BUILD] Error generating static benchmark data:', error);
    return createStaticBenchmarkData([], 'error');
  }
}

// Cache raw API data 1:1 from the API
async function cacheRawApiData(): Promise<any> {
  console.log('[BUILD] Caching raw API data 1:1...');
  
  try {
    // Fetch raw model info data from API
    const response = await fetchModelInfo(API_CONFIG);
    
    if (response.status === 200) {
      console.log(`[BUILD] Successfully cached ${Array.isArray(response.data) ? response.data.length : 0} raw API models`);
      return {
        data: response.data,
        status: response.status,
        message: response.message,
        timestamp: new Date().toISOString(),
        source: API_CONFIG.useMockData ? 'mock' : 'api',
        apiConfig: {
          baseUrl: API_CONFIG.baseUrl,
          useMockData: API_CONFIG.useMockData
        }
      };
    } else {
      console.warn(`[BUILD] Failed to cache raw API data: ${response.message}`);
      return {
        data: [],
        status: response.status,
        message: response.message,
        timestamp: new Date().toISOString(),
        source: 'error',
        error: true
      };
    }
  } catch (error) {
    console.error('[BUILD] Error caching raw API data:', error);
    return {
      data: [],
      status: 500,
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      source: 'error',
      error: true
    };
  }
}

// Combine model and benchmark data
async function generateCombinedData(): Promise<{
  models: StaticModelData;
  benchmarks: StaticBenchmarkData;
  enrichedModels: StaticModelData;
  modelCards: ModelCard[];
  modelMappings: any;
  statistics: any;
  rawApiData: any;
}> {
  console.log('[BUILD] Generating combined model and benchmark data...');
  
  const [modelData, benchmarkData, modelMappings, rawApiData] = await Promise.all([
    generateStaticModelData(), // API models only for models.json
    generateStaticBenchmarkData(),
    loadModelMappings(),
    cacheRawApiData() // Cache raw API data 1:1
  ]);
  
  // Load model cards separately for inclusion in output
  const modelCards = await loadModelCards();
  
  // Convert model cards to ModelData format to have the data available
  const modelCardData = await convertModelCardsToModelData(modelCards);
  
  // Create a map of API models by their keys for fast lookup
  const apiModelsByKey = new Map<string, any>();
  modelData.models.forEach(apiModel => {
    // Store by both key and name for flexible matching
    if (apiModel.key) apiModelsByKey.set(apiModel.key, apiModel);
    if (apiModel.name) apiModelsByKey.set(apiModel.name, apiModel);
    if (apiModel.id) apiModelsByKey.set(apiModel.id, apiModel);
  });
  
  console.log(`[BUILD] API models available: ${Array.from(apiModelsByKey.keys()).join(', ')}`);
  
  // Use model-mappings.json as master list
  const mappings = modelMappings.mappings || {};
  const enrichedModelsData: any[] = [];
  const processedModelCardIds = new Set<string>(); // Track processed model cards to avoid duplicates
  
  // Helper function to detect if a mapping key represents an API-available model
  const isApiProviderKey = (key: string): boolean => {
    const apiProviderPrefixes = ['openai/', 'azure/', 'gcp/', 'anthropic/', 'aws/', 'deepseek/', 'iteratec/'];
    return apiProviderPrefixes.some(prefix => key.startsWith(prefix));
  };
  
  // Process each mapping entry to create unified enriched models
  for (const [mappingKey, mapping] of Object.entries(mappings)) {
    const typedMapping = mapping as any;
    
    if (!typedMapping.modelCardFile || !typedMapping.modelCardId) {
      console.warn(`[BUILD] Invalid mapping for ${mappingKey}: missing modelCardFile or modelCardId`);
      continue;
    }
    
    // Find the corresponding model card data
    const modelCardModel = modelCardData.find(card =>
      (card as any).mappingInfo?.mappingKey === mappingKey
    );
    
    // Determine if this is an API-available model or just a Model Card
    const isApiAvailable = isApiProviderKey(mappingKey);
    
    // Get liteLLM-provisioning flag from model card (default: true)
    const liteLLMProvisioning = modelCardModel && (modelCardModel as any)['liteLLM-provisioning'] !== undefined
      ? (modelCardModel as any)['liteLLM-provisioning']
      : true; // Default is true
    
    if (isApiAvailable) {
      // This is an API-available model - check if we have actual API data
      const apiModel = apiModelsByKey.get(mappingKey);
      
      if (apiModel) {
        // Use API model as base with liteLLM-provisioning and status from model card
        console.log(`[BUILD] API model available: ${mappingKey} -> liteLLM: ${liteLLMProvisioning} (from model card) -> preserving API maxOutputTokens: ${apiModel.maxOutputTokens}`);
        enrichedModelsData.push({
          ...apiModel,
          'liteLLM-provisioning': liteLLMProvisioning,
          // Add model card metadata and status fields if available
          ...(modelCardModel && {
            status: (modelCardModel as any).status,
            deprecationDate: (modelCardModel as any).deprecationDate,
            shutdownDate: (modelCardModel as any).shutdownDate,
            _modelCardSource: (modelCardModel as any)._modelCardSource,
            _modelCardVersion: (modelCardModel as any)._modelCardVersion,
            _enrichedWithMapping: true,
            mappingInfo: (modelCardModel as any).mappingInfo
          }),
          // Explicitly preserve API token values to ensure they are never overridden
          maxOutputTokens: apiModel.maxOutputTokens,
          maxTokens: apiModel.maxTokens,
          maxInputTokens: apiModel.maxInputTokens,
          contextWindow: apiModel.contextWindow
        });
      } else if (modelCardModel) {
        // API should be available but no API data found - use model card data with flag from model card
        console.log(`[BUILD] API model missing, using Model Card: ${mappingKey} -> liteLLM: ${liteLLMProvisioning} (from model card) -> using model card maxOutputTokens: ${(modelCardModel as any).maxOutputTokens}`);
        enrichedModelsData.push({
          ...modelCardModel,
          'liteLLM-provisioning': liteLLMProvisioning
        });
      }
      
      // Mark model card as processed if we have one
      if (modelCardModel && typedMapping.modelCardId) {
        processedModelCardIds.add(typedMapping.modelCardId);
      }
    } else {
      // This is a "friendly name" mapping - only Model Card, not API available, use flag from model card
      if (modelCardModel && !processedModelCardIds.has(typedMapping.modelCardId)) {
        console.log(`[BUILD] Model Card only: ${mappingKey} -> liteLLM: ${liteLLMProvisioning} (from model card)`);
        enrichedModelsData.push({
          ...modelCardModel,
          'liteLLM-provisioning': liteLLMProvisioning
        });
        processedModelCardIds.add(typedMapping.modelCardId);
      }
    }
  }
  
  // Add any API models that are NOT in the mappings (legacy support)
  for (const apiModel of modelData.models) {
    const isInMappings = Object.keys(mappings).includes(apiModel.key || apiModel.name || apiModel.id || '');
    
    if (!isInMappings) {
      console.log(`[BUILD] Adding unmapped API model: ${apiModel.name || apiModel.id} -> liteLLM: true (default)`);
      enrichedModelsData.push({
        ...apiModel,
        'liteLLM-provisioning': true // Default für unmapped API models
      });
    }
  }
  
  console.log(`[BUILD] Total enriched models created: ${enrichedModelsData.length}`);
  console.log(`[BUILD] - With LiteLLM: ${enrichedModelsData.filter(m => m['liteLLM-provisioning'] === true).length}`);
  console.log(`[BUILD] - Model Cards only: ${enrichedModelsData.filter(m => m['liteLLM-provisioning'] === false).length}`);
  
  // Transform confidentiality "open" and "external" to "public" for all enriched models
  const enrichedModelsWithOpenConfidentiality = enrichedModelsData.filter(model => model.confidentiality === 'open').length;
  const enrichedModelsWithExternalConfidentiality = enrichedModelsData.filter(model => model.confidentiality === 'external').length;
  const transformedEnrichedModelsData = enrichedModelsData.map(model => ({
    ...model,
    confidentiality: (model.confidentiality === 'open' || model.confidentiality === 'external') ? 'public' : model.confidentiality
  }));
  
  if (enrichedModelsWithOpenConfidentiality > 0) {
    console.log(`[BUILD] Transformed ${enrichedModelsWithOpenConfidentiality} enriched models from confidentiality "open" to "public"`);
  }
  if (enrichedModelsWithExternalConfidentiality > 0) {
    console.log(`[BUILD] Transformed ${enrichedModelsWithExternalConfidentiality} enriched models from confidentiality "external" to "public"`);
  }
  
  // Enrich ALL models with benchmark data
  const enrichedModels = enrichModelDataWithBenchmark(transformedEnrichedModelsData, benchmarkData.benchmarks);
  const enrichedStaticData = createStaticModelData(enrichedModels, modelData.source);
  
  // Generate statistics
  const modelStats = generateModelStatistics(enrichedModels);
  const benchmarkStats = getBenchmarkStatistics(benchmarkData.benchmarks);
  
  // Enhanced statistics
  const apiModelsCount = enrichedModels.filter(m => (m as any)['liteLLM-provisioning'] === true).length;
  const modelCardsCount = enrichedModels.filter(m => (m as any)['liteLLM-provisioning'] === false).length;
  
  const statistics = {
    models: modelStats,
    benchmarks: benchmarkStats,
    sources: {
      apiModels: apiModelsCount,
      modelCards: modelCardsCount,
      totalModelCards: modelCards.length,
      modelsJsonExcludesModelCards: true, // Flag to indicate models.json excludes model cards
      enrichedModelsIncludesModelCards: true, // Flag to indicate enriched-models.json now includes missing model cards
      enrichedModelsExcludesModelCards: false // Updated flag
    },
    enrichment: {
      totalModels: enrichedModels.length,
      modelsWithBenchmarks: enrichedModels.filter(m => m.benchmarkData).length,
      modelsWithoutBenchmarks: enrichedModels.filter(m => !m.benchmarkData).length,
      apiModelsWithLiteLLM: apiModelsCount,
      modelCardsWithoutLiteLLM: modelCardsCount
    },
    mappings: {
      totalMappings: Object.keys(mappings).length,
      mappedModels: Object.keys(mappings).length
    },
    lastGenerated: new Date().toISOString()
  };
  
  return {
    models: modelData, // API models only
    benchmarks: benchmarkData,
    enrichedModels: enrichedStaticData, // Unified models based on mappings
    modelCards,
    modelMappings,
    statistics,
    rawApiData // Raw API data 1:1 from the API
  };
}

// Write data to files
async function writeDataFiles(data: {
  models: StaticModelData;
  benchmarks: StaticBenchmarkData;
  enrichedModels: StaticModelData;
  modelCards: ModelCard[];
  modelMappings: any;
  statistics: any;
  rawApiData: any;
}) {
  console.log('[BUILD] Writing data files...');
  
  const files = [
    { name: 'models.json', content: data.models },
    { name: 'polyglot_benchmarks.json', content: data.benchmarks },
    { name: 'enriched-models.json', content: data.enrichedModels },
    { name: 'model-cards.json', content: {
        modelCards: data.modelCards,
        totalCards: data.modelCards.length,
        lastUpdated: new Date().toISOString(),
        generatedFrom: 'Individual model files in src/data/models/',
        version: '1.0'
      }
    },
    { name: 'model-mappings-used.json', content: { mappings: data.modelMappings, lastUpdated: new Date().toISOString() } },
    { name: 'statistics.json', content: data.statistics },
    { name: 'raw-api-data.json', content: data.rawApiData }
  ];
  
  for (const file of files) {
    try {
      const filePath = join(OUTPUT_DIR, file.name);
      await fs.writeFile(filePath, JSON.stringify(file.content, null, 2), 'utf-8');
      console.log(`[BUILD] Successfully wrote ${file.name}`);
    } catch (error) {
      console.error(`[BUILD] Error writing ${file.name}:`, error);
    }
  }
  
  // Note: model-mappings.json is not overwritten here as it's a manually maintained configuration file
  console.log('[BUILD] Note: model-mappings.json is preserved as a manually maintained configuration file');
}

// Main function
async function main() {
  console.log('[BUILD] Starting static data generation...');
  const startTime = Date.now();
  
  try {
    // Ensure directories exist
    await ensureDirectories();
    
    // Generate all data
    const data = await generateCombinedData();
    
    // Write to files
    await writeDataFiles(data);
    
    const duration = Date.now() - startTime;
    console.log(`[BUILD] ✅ Static data generation completed successfully in ${duration}ms`);
    console.log(`[BUILD] Generated data for ${data.enrichedModels.models.length} models and ${data.benchmarks.benchmarks.length} benchmarks`);
    console.log(`[BUILD] Sources: ${data.statistics.sources.apiModels} API models + ${data.statistics.sources.modelCards} model cards`);
    console.log(`[BUILD] ${data.statistics.enrichment.modelsWithBenchmarks} models enriched with benchmark data`);
    console.log(`[BUILD] ${data.modelCards.length} model cards processed and saved`);
    console.log(`[BUILD] Raw API data (1:1) cached with ${Array.isArray(data.rawApiData.data) ? data.rawApiData.data.length : 0} entries from ${data.rawApiData.source}`);
    
  } catch (error) {
    console.error('[BUILD] ❌ Static data generation failed:', error);
    process.exit(1);
  }
}

// Run the script if this is the main module
const isMainModule = process.argv[1] && process.argv[1].endsWith('generate-static-data.ts');
if (isMainModule) {
  main();
}

export { main as generateStaticData };