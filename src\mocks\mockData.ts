import type { ModelInfo, ModelCost, ModelData } from "@/types/api";

export const mockModelInfoResponse = {
  data: [
    {
      model_name: "azure/gpt-4-turbo",
      litellm_params: {},
      model_info: {
        id: "3c0069b0c8cd325fc9efb62d6ca4a57f53a9109c24e75b3015a22b50e524e266",
        db_model: false,
        base_model: "azure/gpt-4-1106-preview",
        mode: "chat",
        confidentiality: "internal",
        key: "azure/gpt-4-1106-preview",
        max_tokens: 4096,
        max_input_tokens: 128000,
        max_output_tokens: 4096,
        input_cost_per_token: 1e-05,
        output_cost_per_token: 3e-05,
        litellm_provider: "azure",
        supports_function_calling: true,
        supports_tool_choice: true,
        supports_vision: false,
      },
    },
    {
      model_name: "azure/text-embedding-ada-002",
      litellm_params: {},
      model_info: {
        id: "bc323220bd2abd5c265f5a3a7ac229bd82b24a3c8a64c508dfd8e14a1f68d425",
        db_model: false,
        mode: "embedding",
        confidentiality: "internal",
        key: "azure/text-embedding-ada-002",
        max_tokens: 8191,
        max_input_tokens: 8191,
        input_cost_per_token: 1e-07,
        output_cost_per_token: 0.0,
        litellm_provider: "azure",
        supports_function_calling: false,
        supports_tool_choice: false,
        supports_vision: false,
      },
    },
    {
      model_name: "azure/gpt-4o",
      litellm_params: {},
      model_info: {
        id: "a71d34a1a02f35df7fe78cc81817d993bb41333cc3429ebed5ab40fda829baca",
        db_model: false,
        base_model: "azure/gpt-4o",
        mode: "chat",
        confidentiality: "internal",
        recommendation: true,
        key: "azure/gpt-4o",
        max_tokens: 16384,
        max_input_tokens: 128000,
        max_output_tokens: 16384,
        input_cost_per_token: 2.5e-06,
        output_cost_per_token: 1e-05,
        litellm_provider: "azure",
        supports_function_calling: true,
        supports_tool_choice: true,
        supports_vision: true,
      },
    },
  ],
};

export const mockModelInfo: ModelInfo[] = mockModelInfoResponse.data.map(item => {
  const apiModelInfo = item.model_info || {}; // This is the object from the mock API response
  return {
    id: ('id' in apiModelInfo && apiModelInfo.id) ? apiModelInfo.id : item.model_name,
    name: item.model_name,
    provider: ('litellm_provider' in apiModelInfo && apiModelInfo.litellm_provider) ? apiModelInfo.litellm_provider : 'N/A',
    description: ('description' in apiModelInfo && apiModelInfo.description) ? apiModelInfo.description : '',
    capabilities: ('capabilities' in apiModelInfo && apiModelInfo.capabilities) ? apiModelInfo.capabilities : [],
    modelGroup: ('modelGroup' in apiModelInfo && apiModelInfo.modelGroup) ? apiModelInfo.modelGroup : '',
    isAvailable: ('isAvailable' in apiModelInfo && apiModelInfo.isAvailable !== undefined) ? apiModelInfo.isAvailable : true,
    confidentiality: ('confidentiality' in apiModelInfo && apiModelInfo.confidentiality) ? apiModelInfo.confidentiality : 'N/A',
    mode: ('mode' in apiModelInfo && apiModelInfo.mode) ? apiModelInfo.mode : 'N/A',
    maxTokens: ('max_tokens' in apiModelInfo && apiModelInfo.max_tokens !== undefined) ? apiModelInfo.max_tokens : null,
    maxInputTokens: ('max_input_tokens' in apiModelInfo && apiModelInfo.max_input_tokens !== undefined) ? apiModelInfo.max_input_tokens : null,
    maxOutputTokens: ('max_output_tokens' in apiModelInfo && apiModelInfo.max_output_tokens !== undefined) ? apiModelInfo.max_output_tokens : null,
    inputCostPerToken: ('input_cost_per_token' in apiModelInfo && apiModelInfo.input_cost_per_token !== undefined) ? apiModelInfo.input_cost_per_token : null,
    outputCostPerToken: ('output_cost_per_token' in apiModelInfo && apiModelInfo.output_cost_per_token !== undefined) ? apiModelInfo.output_cost_per_token : null,
    supportsFunctionCalling: ('supports_function_calling' in apiModelInfo && apiModelInfo.supports_function_calling !== undefined) ? apiModelInfo.supports_function_calling : null,
    supportsToolChoice: ('supports_tool_choice' in apiModelInfo && apiModelInfo.supports_tool_choice !== undefined) ? apiModelInfo.supports_tool_choice : null,
    supportsVision: ('supports_vision' in apiModelInfo && apiModelInfo.supports_vision !== undefined) ? apiModelInfo.supports_vision : null,
    key: ('key' in apiModelInfo && apiModelInfo.key) ? apiModelInfo.key : '',
    recommendation: ('recommendation' in apiModelInfo && apiModelInfo.recommendation) ? apiModelInfo.recommendation : false,
    db_model: ('db_model' in apiModelInfo && apiModelInfo.db_model) ? apiModelInfo.db_model : false,
    base_model: ('base_model' in apiModelInfo && apiModelInfo.base_model) ? apiModelInfo.base_model : '',
    cache_creation_input_token_cost: ('cache_creation_input_token_cost' in apiModelInfo && apiModelInfo.cache_creation_input_token_cost !== undefined) ? apiModelInfo.cache_creation_input_token_cost : null,
    cache_read_input_token_cost: ('cache_read_input_token_cost' in apiModelInfo && apiModelInfo.cache_read_input_token_cost !== undefined) ? apiModelInfo.cache_read_input_token_cost : null,
    input_cost_per_character: ('input_cost_per_character' in apiModelInfo && apiModelInfo.input_cost_per_character !== undefined) ? apiModelInfo.input_cost_per_character : null,
    input_cost_per_token_above_128k_tokens: ('input_cost_per_token_above_128k_tokens' in apiModelInfo && apiModelInfo.input_cost_per_token_above_128k_tokens !== undefined) ? apiModelInfo.input_cost_per_token_above_128k_tokens : null,
    input_cost_per_query: ('input_cost_per_query' in apiModelInfo && apiModelInfo.input_cost_per_query !== undefined) ? apiModelInfo.input_cost_per_query : null,
    input_cost_per_second: ('input_cost_per_second' in apiModelInfo && apiModelInfo.input_cost_per_second !== undefined) ? apiModelInfo.input_cost_per_second : null,
    input_cost_per_audio_token: ('input_cost_per_audio_token' in apiModelInfo && apiModelInfo.input_cost_per_audio_token !== undefined) ? apiModelInfo.input_cost_per_audio_token : null,
    input_cost_per_token_batches: ('input_cost_per_token_batches' in apiModelInfo && apiModelInfo.input_cost_per_token_batches !== undefined) ? apiModelInfo.input_cost_per_token_batches : null,
    output_cost_per_token_batches: ('output_cost_per_token_batches' in apiModelInfo && apiModelInfo.output_cost_per_token_batches !== undefined) ? apiModelInfo.output_cost_per_token_batches : null,
    output_cost_per_audio_token: ('output_cost_per_audio_token' in apiModelInfo && apiModelInfo.output_cost_per_audio_token !== undefined) ? apiModelInfo.output_cost_per_audio_token : null,
    output_cost_per_character: ('output_cost_per_character' in apiModelInfo && apiModelInfo.output_cost_per_character !== undefined) ? apiModelInfo.output_cost_per_character : null,
    output_cost_per_token_above_128k_tokens: ('output_cost_per_token_above_128k_tokens' in apiModelInfo && apiModelInfo.output_cost_per_token_above_128k_tokens !== undefined) ? apiModelInfo.output_cost_per_token_above_128k_tokens : null,
    output_cost_per_character_above_128k_tokens: ('output_cost_per_character_above_128k_tokens' in apiModelInfo && apiModelInfo.output_cost_per_character_above_128k_tokens !== undefined) ? apiModelInfo.output_cost_per_character_above_128k_tokens : null,
    output_cost_per_second: ('output_cost_per_second' in apiModelInfo && apiModelInfo.output_cost_per_second !== undefined) ? apiModelInfo.output_cost_per_second : null,
    output_cost_per_image: ('output_cost_per_image' in apiModelInfo && apiModelInfo.output_cost_per_image !== undefined) ? apiModelInfo.output_cost_per_image : null,
    output_vector_size: ('output_vector_size' in apiModelInfo && apiModelInfo.output_vector_size !== undefined) ? apiModelInfo.output_vector_size : null,
    supports_system_messages: ('supports_system_messages' in apiModelInfo && apiModelInfo.supports_system_messages !== undefined) ? apiModelInfo.supports_system_messages : null,
    supports_response_schema: ('supports_response_schema' in apiModelInfo && apiModelInfo.supports_response_schema !== undefined) ? apiModelInfo.supports_response_schema : null,
    supports_assistant_prefill: ('supports_assistant_prefill' in apiModelInfo && apiModelInfo.supports_assistant_prefill !== undefined) ? apiModelInfo.supports_assistant_prefill : null,
    supports_prompt_caching: ('supports_prompt_caching' in apiModelInfo && apiModelInfo.supports_prompt_caching !== undefined) ? apiModelInfo.supports_prompt_caching : null,
    supports_audio_input: ('supports_audio_input' in apiModelInfo && apiModelInfo.supports_audio_input !== undefined) ? apiModelInfo.supports_audio_input : null,
    supports_audio_output: ('supports_audio_output' in apiModelInfo && apiModelInfo.supports_audio_output !== undefined) ? apiModelInfo.supports_audio_output : null,
    supports_pdf_input: ('supports_pdf_input' in apiModelInfo && apiModelInfo.supports_pdf_input !== undefined) ? apiModelInfo.supports_pdf_input : null,
    supports_embedding_image_input: ('supports_embedding_image_input' in apiModelInfo && apiModelInfo.supports_embedding_image_input !== undefined) ? apiModelInfo.supports_embedding_image_input : null,
    supports_native_streaming: ('supports_native_streaming' in apiModelInfo && apiModelInfo.supports_native_streaming !== undefined) ? apiModelInfo.supports_native_streaming : null,
    tpm: ('tpm' in apiModelInfo && apiModelInfo.tpm !== undefined) ? apiModelInfo.tpm : null,
    rpm: ('rpm' in apiModelInfo && apiModelInfo.rpm !== undefined) ? apiModelInfo.rpm : null,
    supported_openai_params: ('supported_openai_params' in apiModelInfo && apiModelInfo.supported_openai_params) ? apiModelInfo.supported_openai_params : null,
    contextWindow: (('context_window' in apiModelInfo && apiModelInfo.context_window) ? apiModelInfo.context_window : null) || (('max_input_tokens' in apiModelInfo && apiModelInfo.max_input_tokens) ? apiModelInfo.max_input_tokens : null) || (('max_tokens' in apiModelInfo && apiModelInfo.max_tokens) ? apiModelInfo.max_tokens : null),
  } as ModelInfo; // Explicitly cast to ModelInfo to satisfy TypeScript
});


export const mockLiteLLMResponse = {
  "azure/gpt-4-turbo": {
    max_tokens: 4096,
    max_input_tokens: 128000,
    max_output_tokens: 4096,
    input_cost_per_token: 1e-05,
    output_cost_per_token: 3e-05,
    litellm_provider: "azure",
    mode: "chat",
    supports_function_calling: true,
    supports_vision: false,
    context_window: 128000, // Added from example, assuming it's max_input_tokens
  },
  "azure/text-embedding-ada-002": {
    max_tokens: 8191,
    max_input_tokens: 8191,
    input_cost_per_token: 1e-07,
    output_cost_per_token: 0.0,
    litellm_provider: "azure",
    mode: "embedding",
    context_window: 8191,
  },
  "azure/gpt-4o": {
    max_tokens: 16384,
    max_input_tokens: 128000,
    max_output_tokens: 16384,
    input_cost_per_token: 2.5e-06,
    output_cost_per_token: 1e-05,
    litellm_provider: "azure",
    mode: "chat",
    supports_function_calling: true,
    supports_vision: true,
    context_window: 128000,
  },
  "gpt-4": { // Example from LiteLLM data
    max_tokens: 4096,
    max_input_tokens: 8192,
    max_output_tokens: 4096,
    input_cost_per_token: 0.00003,
    output_cost_per_token: 0.00006,
    litellm_provider: "openai",
    mode: "chat",
    supports_function_calling: true,
    supports_prompt_caching: true,
    supports_system_messages: true,
    supports_tool_choice: true,
    context_window: 8192,
  }
};

export const mockModelCost: ModelCost[] = Object.entries(mockLiteLLMResponse)
  .filter(([key]) => key !== "sample_spec") // Filter out sample_spec
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  .map(([modelName, details]: [string, any]) => ({
    model: modelName, // This 'model' is the key from LiteLLM data, e.g., "azure/gpt-4-turbo"
    inputCostPer1kTokens: details.input_cost_per_token ? details.input_cost_per_token * 1000 : undefined,
    outputCostPer1kTokens: details.output_cost_per_token ? details.output_cost_per_token * 1000 : undefined,
    contextWindow: details.context_window || details.max_input_tokens || details.max_tokens,
    maxTokens: details.max_tokens,
    maxInputTokens: details.max_input_tokens,
    maxOutputTokens: details.max_output_tokens,
    litellmProvider: details.litellm_provider,
    mode: details.mode,
    supportsFunctionCalling: details.supports_function_calling,
    supportsVision: details.supports_vision,
}));

export const mockModelData: ModelData[] = mockModelInfo.map(info => {
  // Find cost data by matching `info.name` (which is `item.model_name` from API)
  // or `info.key` (which is `item.model_info.key` from API)
  // with `cost.model` (which is the key from LiteLLM data)
  const costData = mockModelCost.find(c =>
    c.model === info.name || (info.key && c.model === info.key)
  );
  return {
    ...info, // Spread all properties from ModelInfo
    // `model` property for ModelData should be consistent, let's use info.name
    // ModelCost fields are merged. If a field exists in both, ModelInfo's version is taken by spread,
    // unless explicitly overridden below.
    inputCostPer1kTokens: costData?.inputCostPer1kTokens,
    outputCostPer1kTokens: costData?.outputCostPer1kTokens,
    // Prioritize contextWindow from costData if available, else from info
    contextWindow: costData?.contextWindow ?? info.contextWindow,
    // If LiteLLM has more specific token info, use it
    maxTokens: costData?.maxTokens ?? info.maxTokens,
    maxInputTokens: costData?.maxInputTokens ?? info.maxInputTokens,
    maxOutputTokens: costData?.maxOutputTokens ?? info.maxOutputTokens,
    // Add distinct cost-related fields if necessary, e.g. if provider/mode differs
    litellmProvider_cost: costData?.litellmProvider,
    mode_cost: costData?.mode,
    displayName: info.name,
  };
});