
# AIMD

```
Aktualisiere auf Ba<PERSON> von @https://www.vals.ai/benchmarks/aime-2025-05-30 als AIME Benchmark-Werte für @/src/data/models
```

# MMMU


```
Aktualisiere alle Benchmark-Werte von @https://www.vals.ai/benchmarks/mmmu-05-30-2025  für MMMU-Benchmarks in @/src/data/models
```

# Kritische Benchmark-Werte fürs Coding

## SWE-bench-verified
Quelle: https://www.swebench.com/index.html | Verified

```
Ergänze den "SWE-bench Verified" für folgende Modelle in @/src/data/models 

- gemini-2.5-flash.json mit 44.2
- gemini-2.5-pro mit 70,2
- gpt-4o mit 57,2
- claude-3-5-sonnet-v2 mit 49
```

## Terminal-Bench

```
Aktualisiere die "Terminal-Bench"-Benchmark-Werte in @/src/data/models auf Ba<PERSON> von @https://www.tbench.ai/leaderboard 
```

## Aider-Polyglot

```
Aktualisiere die "Aider-Polyglot"-Benchmark-Werte in @/src/data/models auf Basis von  @/src/data/polyglot_benchmarks.json
```


## Codeforces

Quelle: https://arxiv.org/html/2501.01257v1

```
Aktualisiere die "Codeforces"-Benchmark-Werte in @/src/data/models auf Basis der angehängten Informationen!
```
## Webdev-Arena

Quelle: https://web.lmarena.ai/leaderboard

```
Aktualisiere die "WebDev-Arena"-Benchmark-Werte in @/src/data/models auf Basis von @https://web.lmarena.ai/leaderboard.

Ergänze auch @/src/data/benchmarks/benchmark-descriptions.json und nehme es als wichtigen Benchmark für alle Coding relevanten Use-Cases in @/src/types/model-recommendations.ts
```

```
Prüfe ob auch Daten aus @https://web.lmarena.ai/leaderboard für:

- Claude 3.7 Sonnet
- Gemini 2.5 Flash
- o4-mini
- Mistral Large 
- GPT-4.1-nano
- Qwen2.5-Coder-32B-Instruct

vorhanden sind! Aktualisiere diese in @/src/data/models !

```

# GPQA-Diamond

Quelle: https://www.vellum.ai/llm-leaderboard

```
Aktualisiere die "GPQA-Diamond"-Benchmark-Werte in @/src/data/models auf Basis der angehängten Informationen!
```


# LiveCodeBench v5

```
Aktualisiere die "LiveCodeBench v5"-Benchmark-Werte in @/src/data/models auf Basis von @https://livecodebench.github.io/leaderboard_v5.html
```


# LiveCodeBench v2025

```
Aktualisiere die "LiveCodeBench v2025"-Benchmark-Werte in @/src/data/models auf Basis von @https://livecodebench.github.io/leaderboard.html

Ergänze auch @/src/data/benchmarks/benchmark-descriptions.json und nehme es als wichtigen Benchmark für alle Coding relevanten Use-Cases in @/src/types/model-recommendations.ts
```

# MRCR

Quelle: https://www.reddit.com/r/singularity/comments/1jjoeq6/gemini_25_pro_benchmarks_released/
```
Aktualisiere die "MRCR"-Benchmark-Werte in @/src/data/models auf Basis der angehängten Informationen!
```