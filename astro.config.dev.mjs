import { defineConfig } from 'astro/config';
import react from '@astrojs/react';
import tailwind from '@astrojs/tailwind';
import { analyzer } from 'vite-bundle-analyzer';
import process from 'node:process';

export default defineConfig({
  integrations: [
    react({
      // Aktiviere React Fast Refresh für besseres HMR
      experimentalReactChildren: true,
    }),
    tailwind({
      applyBaseStyles: false,
    }),
  ],
  site: 'https://iteratec-llm-browser-b6a964.pages.iteratec.de',
  base: '/',
  output: 'static',
  
  // GitLab Pages requires exposed files to be located in a folder called "public".
  // So we're instructing <PERSON><PERSON> to put the static build output in a folder of that name.
  outDir: 'public',

  // The folder name <PERSON><PERSON> uses for static files (`public`) is already reserved
  // for the build output. So in deviation from the defaults we're using a folder
  // called `static` instead.
  publicDir: 'static',
  
  build: {
    assets: '_assets',
  },
  // Entwicklungsserver-Konfiguration für besseres HMR
  server: {
    port: 4321,
    host: true,
  },
  // Vite-Konfiguration für optimiertes HMR
  vite: {
    plugins: [
      // Bundle analyzer - nur aktiviert wenn ANALYZE=true gesetzt ist
      process.env.ANALYZE && analyzer({
        analyzerMode: 'server',
        openAnalyzer: true,
      }),
    ].filter(Boolean),
    server: {
      watch: {
        usePolling: true,
        interval: 100,
      },
      hmr: {
        overlay: true,
      },
    },
    optimizeDeps: {
      include: ['react', 'react-dom'],
    },
  },
});