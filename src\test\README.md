# Testing Setup Documentation

## Overview

This project uses **Vitest** as the primary testing framework with **React Testing Library** for component testing. The setup provides a comprehensive testing environment for React components, utility functions, and services.

## 🚀 Quick Start

```bash
# Run all tests once
npm run test:run

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm run test -- ModelTable.test.tsx
```

## 📁 Project Structure

```
src/
├── test/
│   ├── setup.ts           # Global test setup and mocks
│   └── README.md          # This documentation
├── components/
│   ├── ui/
│   │   └── button.test.tsx    # UI component tests
│   └── models/
│       └── ModelTable.test.tsx # Business component tests
└── utils/
    └── transformations.test.ts  # Utility function tests
```

## ⚙️ Configuration

### Vitest Config (`vitest.config.ts`)
- **Environment**: jsdom for DOM testing
- **Setup Files**: Automatic jest-dom matchers
- **Coverage**: v8 provider with 70% thresholds
- **Aliases**: Path mapping (@/ -> src/)
- **Reporters**: Verbose and JSON output

### Test Setup (`src/test/setup.ts`)
- **jest-dom matchers**: Extended expect assertions
- **Global mocks**: ResizeObserver, IntersectionObserver, matchMedia
- **Cleanup**: Automatic cleanup after each test
- **Console filtering**: Suppresses React warnings in tests

## 🧪 Testing Patterns

### Component Testing
```typescript
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from './button';

describe('Button Component', () => {
  it('handles click events', async () => {
    const user = userEvent.setup();
    const handleClick = vi.fn();
    
    render(<Button onClick={handleClick}>Click me</Button>);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Utility Testing
```typescript
import { extractModelId } from './transformations';

describe('extractModelId', () => {
  it('extracts clean model ID from name', () => {
    expect(extractModelId('GPT-4 Turbo')).toBe('gpt-4-turbo');
  });
});
```

### Mock Data Patterns
```typescript
const mockModels: EnrichedModelData[] = [
  {
    id: 'test-model-1',
    name: 'Test Model 1',
    provider: 'TestProvider',
    // ... other required properties
  },
];
```

## 📊 Coverage Reports

Coverage reports are generated in multiple formats:
- **Text**: Console output during test runs
- **JSON**: `./test-results/results.json`
- **HTML**: Available when html reporter is enabled

### Coverage Thresholds
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

## 🔧 Available Matchers

Thanks to `@testing-library/jest-dom`, you have access to:
- `toBeInTheDocument()`
- `toHaveClass()`
- `toBeDisabled()`
- `toHaveAttribute()`
- `toHaveTextContent()`
- And many more...

## 🎯 Best Practices

### 1. Test User Interactions
```typescript
// ✅ Good - Test user behavior
await user.click(screen.getByRole('button', { name: /submit/i }));

// ❌ Avoid - Testing implementation details
fireEvent.click(button);
```

### 2. Use Semantic Queries
```typescript
// ✅ Good - Accessible queries
screen.getByRole('button', { name: /submit/i })
screen.getByLabelText(/email address/i)

// ❌ Avoid - Fragile queries
screen.getByTestId('submit-btn')
```

### 3. Mock External Dependencies
```typescript
// Mock API calls
vi.mock('../services/api', () => ({
  fetchModels: vi.fn().mockResolvedValue(mockData),
}));
```

### 4. Test Error States
```typescript
it('handles loading state', () => {
  render(<ModelTable {...props} models={[]} />);
  expect(screen.getByRole('table')).toBeInTheDocument();
});
```

## 🚨 Common Issues

### 1. Missing Test IDs
If tests fail to find elements, check if the component uses the expected test IDs or accessible roles.

### 2. Async Operations
Always use `await` with user interactions and async operations:
```typescript
await user.click(button);
await waitFor(() => expect(mockFn).toHaveBeenCalled());
```

### 3. Mock Data Mismatch
Ensure mock data matches the expected TypeScript interfaces:
```typescript
// ✅ Correct
modelCard: undefined,

// ❌ Incorrect
modelCard: null,
```

## 📈 Extending Tests

### Adding New Test Files
1. Create `*.test.tsx` or `*.test.ts` files
2. Follow the naming convention: `ComponentName.test.tsx`
3. Import required testing utilities
4. Write descriptive test names

### Adding Custom Matchers
Extend the setup file with custom matchers:
```typescript
// src/test/setup.ts
expect.extend({
  toHaveCustomProperty(received, expected) {
    // Custom matcher implementation
  },
});
```

## 🔗 Resources

- [Vitest Documentation](https://vitest.dev/)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [jest-dom Matchers](https://github.com/testing-library/jest-dom)
- [User Event API](https://testing-library.com/docs/user-event/intro/)