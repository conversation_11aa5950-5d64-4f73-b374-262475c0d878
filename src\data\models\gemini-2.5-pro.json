{"basicInfo": {"modelId": "gemini-2.5-pro-preview-05-06", "displayName": "Gemini 2.5 Pro", "provider": "Google", "modelFamily": "Gemini", "version": "preview-05-06", "description": "Unser fortschrittlichstes Gemini-Modell für logisches Denken, das komplexe Probleme lösen kann", "releaseDate": "2025-05-06", "status": "Preview", "knowledgeCutoff": "Januar 2025"}, "technicalSpecs": {"contextWindow": 1048576, "maxOutputTokens": 65535, "supportedInputTypes": ["text", "image", "audio", "video", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"maxImages": 3000, "maxImageSize": "7 MB", "maxAudioLength": "8.4 Stunden", "maxVideoLength": "45 Minuten", "supportedMimeTypes": {"image": ["image/png", "image/jpeg", "image/webp"], "audio": ["audio/x-aac", "audio/flac", "audio/mp3", "audio/m4a", "audio/mpeg", "audio/mpga", "audio/mp4", "audio/opus", "audio/pcm", "audio/wav", "audio/webm"], "video": ["video/x-flv", "video/quicktime", "video/mpeg", "video/mpegs", "video/mpg", "video/mp4", "video/webm", "video/wmv", "video/3gpp"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": true, "audioOutput": false, "imageGeneration": false, "codeExecution": true, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": true, "multilingualSupport": true, "embeddingImageInput": true, "liteLLM-provisioning": true}, "performance": {"latency": "Moderately Fast", "rateLimits": {"tokensPerMinute": 1000000}, "temperature": {"min": 0, "max": 2, "default": 1.0}, "topP": 0.95, "topK": 64}, "pricing": {"inputCostPer1MTokens": 1.25, "inputCostPer1MTokensLarge": 2.5, "outputCostPer1MTokens": 10.0, "outputCostPer1MTokensLarge": 15.0, "cachingCosts": {"cacheWrites": 0.31, "cacheWritesLarge": 0.625, "cacheStorage": 4.5}, "currency": "USD", "notes": {"inputPricing": "≤200k tokens: $1.25/1M, >200k tokens: $2.50/1M", "outputPricing": "≤200k tokens: $10.00/1M, >200k tokens: $15.00/1M", "cachePricing": "≤200k tokens: $0.31/1M, >200k tokens: $0.625/1M, Storage: $4.50/1M tokens per hour"}}, "availability": {"regions": [{"region": "global", "availability": "Preview"}, {"region": "us-central1", "availability": "Preview"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI"]}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR", "ISO27001"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": true, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 17.8, "metric": "Pass Rate", "attemptType": "single attempt", "toolsUsed": false, "date": "2025-06-09", "notes": "Gemini 2.5 Pro Preview (May 06 2025)"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 86.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-06"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 86.7, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-06"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 92.0, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-06"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 85.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "AIME 2024 and 2025 combined benchmark from vals.ai (Gemini 2.5 Pro Exp)"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 69.2, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 76.9, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-07", "notes": "Aider polyglot benchmark with diff-fenced edit format"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 97.3, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-05-07", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 70.2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Updated SWE-bench Verified benchmark score"}, {"benchmarkName": "SimpleQA", "category": "Factuality", "score": 52.9, "metric": "Accuracy", "date": "2025-05-06"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 81.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "MMMU benchmark from vals.ai (Gemini 2.5 Pro Exp)"}, {"benchmarkName": "MRCR", "category": "Long context", "score": 91.5, "alternativeScores": {"average": 91.5, "pointwise": 83.1}, "metric": "Accuracy", "contextLength": "128k", "attemptType": "average", "date": "2025-06-09", "notes": "128k average: 91.5%, 1M pointwise: 83.1%"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 25.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-15", "notes": "Terminus agent framework, ±2.8% confidence interval"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1443.22, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #1 (tied)"}], "metadata": {"lastUpdated": "2025-06-21T10:24:00Z", "dataSource": "Google Cloud Vertex AI Documentation, Google Gemini API Pricing (2025-06-18), vals.ai AIME Benchmark, vals.ai MMMU Benchmark, SWE-bench Verified Benchmark, Terminal-Bench Leaderboard", "version": "1.5"}}