{"basicInfo": {"modelId": "o3-pro-2025-06-10", "displayName": "o3-pro", "provider": "OpenAI", "modelFamily": "o3", "version": "2025-06-10", "description": "Version of o3 with more compute for better responses. Designed to tackle tough problems with advanced reasoning capabilities.", "releaseDate": "2025-01-31", "status": "GA", "knowledgeCutoff": "Juni 2024"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 100000, "maxReasoningTokens": 100000, "maxCompletionTokens": 100000, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": true, "codeExecution": false, "systemInstructions": true, "promptCaching": false, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": true, "realTimeAPI": false, "liteLLM-provisioning": false}, "performance": {"latency": "Slow", "rateLimits": {"queriesPerMinute": 500, "tokensPerMinute": 30000, "batchQueueLimit": 90000}, "reasoningPerformance": {"averageReasoningTime": "30-120 Sekunden", "maxReasoningTime": "<PERSON><PERSON><PERSON>", "reasoningEfficiency": "High"}, "temperature": {"min": 0, "max": 1, "default": 1.0}}, "pricing": {"inputCostPer1MTokens": 20.0, "outputCostPer1MTokens": 80.0, "reasoningCosts": {"reasoningTokensPerMillion": 20.0, "completionTokensPerMillion": 80.0}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API"], "regions": [{"region": "global", "availability": "GA"}]}, "security": {"dataResidency": false, "cmekSupport": false, "vpcSupport": false, "accessTransparency": false, "complianceStandards": ["SOC2"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 88.0, "metric": "Pass Rate", "attemptType": "single attempt", "notes": "High School Mathematik-Wettbewerb pass@1 evaluation"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 87.7, "metric": "Pass Rate", "attemptType": "single attempt", "notes": "Graduate-level naturwissenschaftliche Fragen mit Extended Thinking"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 71.7, "metric": "Pass@1", "attemptType": "single attempt", "notes": "Live-Code-Generierung ohne Kontamination"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 71.0, "metric": "Success Rate", "attemptType": "single attempt", "notes": "Reale Software-Engineering-Aufgaben mit Tools"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 79.6, "metric": "Pass Rate", "attemptType": "single attempt", "notes": "Mehrsprachige Code-Bearbeitung mit verschiedenen Editing-Modi"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 95.1, "metric": "Well-formed Rate", "attemptType": "single attempt", "notes": "Prozentsatz der gut strukturierten Code-Antworten bei Aider-Polyglot"}], "metadata": {"lastUpdated": "2025-06-11T08:21:00Z", "dataSource": "OpenAI Documentation", "version": "1.1"}}