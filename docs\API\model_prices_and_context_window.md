# Model Price & Context Window Structure (Examples)

This document provides example entries and structural guidance for model descriptions as found in [`model_prices_and_context_window.json`](https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json) from the LiteLLM project.  
**Note:** This is not a full listing, but a concise reference for structure and field meanings.

---

## General Structure

Each model is described as a JSON object with a unique key (the model name).  
The value is an object with fields describing context window, pricing, provider, and capabilities.

### Example: Generic Model Spec

```json
"sample_spec": {
  "max_tokens": "LEGACY parameter. set to max_output_tokens if provider specifies it. IF not set to max_input_tokens, if provider specifies it.",
  "max_input_tokens": "max input tokens, if the provider specifies it. if not default to max_tokens",
  "max_output_tokens": "max output tokens, if the provider specifies it. if not default to max_tokens",
  "input_cost_per_token": 0.0000,
  "output_cost_per_token": 0.000,
  "output_cost_per_reasoning_token": 0.000,
  "litellm_provider": "one of https://docs.litellm.ai/docs/providers",
  "mode": "one of: chat, embedding, completion, image_generation, audio_transcription, audio_speech, moderation, rerank",
  "supports_function_calling": true,
  "supports_parallel_function_calling": true,
  "supports_vision": true,
  "supports_audio_input": true,
  "supports_audio_output": true,
  "supports_prompt_caching": true,
  "supports_response_schema": true,
  "supports_system_messages": true,
  "supports_reasoning": true,
  "supports_web_search": true,
  "search_context_cost_per_query": {
    "search_context_size_low": 0.0000,
    "search_context_size_medium": 0.0000,
    "search_context_size_high": 0.0000
  },
  "deprecation_date": "YYYY-MM-DD"
}
```

---

## Example Model Entries

### 1. Chat Model

```json
"gpt-4": {
  "max_tokens": 4096,
  "max_input_tokens": 8192,
  "max_output_tokens": 4096,
  "input_cost_per_token": 0.00003,
  "output_cost_per_token": 0.00006,
  "litellm_provider": "openai",
  "mode": "chat",
  "supports_function_calling": true,
  "supports_prompt_caching": true,
  "supports_system_messages": true,
  "supports_tool_choice": true
}
```

### 2. Embedding Model

```json
"text-embedding-3-large": {
  "max_tokens": 8191,
  "max_input_tokens": 8191,
  "output_vector_size": 3072,
  "input_cost_per_token": 0.00000013,
  "output_cost_per_token": 0.000000,
  "litellm_provider": "openai",
  "mode": "embedding"
}
```

### 3. Moderation Model

```json
"omni-moderation-latest": {
  "max_tokens": 32768,
  "max_input_tokens": 32768,
  "max_output_tokens": 0,
  "input_cost_per_token": 0.0,
  "output_cost_per_token": 0.0,
  "litellm_provider": "openai",
  "mode": "moderation"
}
```

### 4. Image Generation Model

```json
"256-x-256/dall-e-2": {
  "mode": "image_generation",
  "input_cost_per_pixel": 0.00000024414,
  "output_cost_per_pixel": 0.0,
  "litellm_provider": "openai"
}
```

### 5. Audio Transcription Model

```json
"whisper-1": {
  "mode": "audio_transcription",
  "input_cost_per_second": 0.0001,
  "output_cost_per_second": 0.0001,
  "litellm_provider": "openai"
}
```

---

## Field Explanations & Notes

- **max_tokens / max_input_tokens / max_output_tokens**: Token limits for input, output, or both.
- **input_cost_per_token / output_cost_per_token**: Pricing per token (input/output).
- **output_vector_size**: For embedding models, the size of the output vector.
- **mode**: Model type (e.g., chat, embedding, completion, image_generation, audio_transcription, moderation, rerank).
- **litellm_provider**: Provider name (e.g., openai, azure, anthropic, etc.).
- **supports_function_calling**, **supports_vision**, etc.: Boolean flags for model capabilities.
- **search_context_cost_per_query**: (If present) Cost for search context by size.
- **deprecation_date**: (Optional) Date when the model is deprecated (format: YYYY-MM-DD).

---

## Usage

- Use these examples as a template for understanding or extending model metadata.
- Not all fields are present for every model; only relevant fields are included per model type.
- For the full, up-to-date list, see the [original JSON](https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json).