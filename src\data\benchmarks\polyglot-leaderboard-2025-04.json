[{"model": "DeepSeek R1", "modelid": "deepseek-r1", "pass_rate_2": 56.9, "percent_cases_well_formed": 96.9, "total_cost": 5.4193, "command": "aider --model deepseek/deepseek-reasoner", "edit_format": "diff", "details": {"dirname": "2025-01-20-19-11-38--ds-turns-upd-cur-msgs-fix-with-summarizer", "test_cases": 225, "commit_hash": "5650697-dirty", "pass_rate_1": 26.7, "pass_num_1": 60, "pass_num_2": 128, "error_outputs": 8, "num_malformed_responses": 7, "num_with_malformed_responses": 7, "user_asks": 15, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 5, "total_tests": 225, "date": "2025-01-20", "versions": "0.71.2.dev", "seconds_per_case": 113.7}}, {"model": "o3 (high) + gpt-4.1", "modelid": "o3", "pass_rate_2": 82.7, "percent_cases_well_formed": 100.0, "total_cost": 69.2921, "command": "aider --model o3 --architect", "edit_format": "architect", "details": {"test_cases": 225, "pass_rate_1": 36.0, "pass_num_1": 81, "pass_num_2": 186, "error_outputs": 9, "seconds_per_case": 110.0}}, {"model": "o3 (high)", "modelid": "o3", "pass_rate_2": 79.6, "percent_cases_well_formed": 95.1, "total_cost": 111.0325, "command": "aider --model o3", "edit_format": "diff", "details": {"test_cases": 225, "pass_rate_1": 36.9, "pass_num_1": 83, "pass_num_2": 179, "error_outputs": 11, "seconds_per_case": 113.8}}, {"model": "Gemini 2.5 Pro Preview 05-06", "modelid": "gemini-2.5-pro-preview-05-06", "pass_rate_2": 76.9, "percent_cases_well_formed": 97.3, "total_cost": 37.4104, "command": "aider --model gemini/gemini-2.5-pro-preview-05-06", "edit_format": "diff-fenced", "details": {"test_cases": 225, "pass_rate_1": 36.4, "pass_num_1": 82, "pass_num_2": 173, "error_outputs": 15, "seconds_per_case": 165.3}}, {"model": "Gemini 2.5 Pro Preview 03-25", "modelid": "gemini-2.5-pro-preview-03-25", "pass_rate_2": 72.9, "percent_cases_well_formed": 92.4, "total_cost": 0, "command": "aider --model gemini/gemini-2.5-pro-preview-03-25", "edit_format": "diff-fenced", "details": {"test_cases": 225, "pass_rate_1": 40.9, "pass_num_1": 92, "pass_num_2": 164, "error_outputs": 21, "seconds_per_case": 45.3}}, {"model": "o4-mini (high)", "modelid": "o4-mini", "pass_rate_2": 72.0, "percent_cases_well_formed": 90.7, "total_cost": 19.6399, "command": "aider --model o4-mini", "edit_format": "diff", "details": {"test_cases": 225, "pass_rate_1": 19.6, "pass_num_1": 44, "pass_num_2": 162, "error_outputs": 26, "seconds_per_case": 176.5}}, {"model": "claude-3-7-sonnet-20250219 (32k thinking tokens)", "modelid": "claude-3-7-sonnet-20250219", "pass_rate_2": 64.9, "percent_cases_well_formed": 97.8, "total_cost": 36.8343, "command": "aider --model anthropic/claude-3-7-sonnet-20250219 --thinking-tokens 32k", "edit_format": "diff", "details": {"test_cases": 225, "pass_rate_1": 29.3, "pass_num_1": 66, "pass_num_2": 146, "error_outputs": 66, "seconds_per_case": 105.2}}, {"model": "DeepSeek R1 + claude-3-5-sonnet-20241022", "modelid": "deepseek-r1", "pass_rate_2": 64.0, "percent_cases_well_formed": 100.0, "total_cost": 13.2933, "command": "aider --architect --model r1 --editor-model sonnet", "edit_format": "architect", "details": {"test_cases": 225, "pass_rate_1": 27.1, "pass_num_1": 61, "pass_num_2": 144, "error_outputs": 2, "seconds_per_case": 251.6}}, {"model": "o1-2024-12-17 (high)", "modelid": "o1-2024-12-17", "pass_rate_2": 61.7, "percent_cases_well_formed": 91.5, "total_cost": 186.4958, "command": "aider --model openrouter/openai/o1", "edit_format": "diff", "details": {"test_cases": 224, "pass_rate_1": 23.7, "pass_num_1": 53, "pass_num_2": 139, "error_outputs": 25, "seconds_per_case": 133.2}}, {"model": "claude-3-7-sonnet-20250219 (no thinking)", "modelid": "claude-3-7-sonnet-20250219", "pass_rate_2": 60.4, "percent_cases_well_formed": 93.3, "total_cost": 17.7191, "command": "aider --model sonnet", "edit_format": "diff", "details": {"test_cases": 225, "pass_rate_1": 24.4, "pass_num_1": 55, "pass_num_2": 136, "error_outputs": 16, "seconds_per_case": 28.3}}, {"model": "o3-mini (high)", "modelid": "o3-mini", "pass_rate_2": 60.4, "percent_cases_well_formed": 93.3, "total_cost": 18.1584, "command": "aider --model o3-mini --reasoning-effort high", "edit_format": "diff", "details": {"test_cases": 224, "pass_rate_1": 21.0, "pass_num_1": 47, "pass_num_2": 136, "error_outputs": 26, "seconds_per_case": 124.6}}, {"model": "Qwen3 235B A22B diff, no think, Alibaba API", "modelid": "qwen3-235b-a22b", "pass_rate_2": 59.6, "percent_cases_well_formed": 92.9, "total_cost": 0.0, "command": "aider --model openai/qwen3-235b-a22b", "edit_format": "diff", "details": {"test_cases": 225, "pass_rate_1": 28.9, "pass_num_1": 65, "pass_num_2": 134, "error_outputs": 22, "seconds_per_case": 45.4}}, {"model": "DeepSeek V3 (0324)", "modelid": "deepseek-v3-0324", "pass_rate_2": 55.1, "percent_cases_well_formed": 99.6, "total_cost": 1.1164, "command": "aider --model deepseek/deepseek-chat", "edit_format": "diff", "details": {"test_cases": 225, "pass_rate_1": 28.0, "pass_num_1": 63, "pass_num_2": 124, "error_outputs": 32, "seconds_per_case": 290.0}}, {"model": "Quasar Alpha", "modelid": "quasar-alpha", "pass_rate_2": 54.7, "percent_cases_well_formed": 98.2, "total_cost": 0.0, "command": "aider --model openrouter/openrouter/quasar-alpha", "edit_format": "diff", "details": {"test_cases": 225, "pass_rate_1": 21.8, "pass_num_1": 49, "pass_num_2": 123, "error_outputs": 4, "seconds_per_case": 14.8}}, {"model": "o3-mini (medium)", "modelid": "o3-mini", "pass_rate_2": 53.8, "percent_cases_well_formed": 95.1, "total_cost": 8.8599, "command": "aider --model o3-mini", "edit_format": "diff", "details": {"test_cases": 225, "pass_rate_1": 19.1, "pass_num_1": 43, "pass_num_2": 121, "error_outputs": 28, "seconds_per_case": 47.2}}, {"model": "Grok 3 Beta", "modelid": "grok-3", "pass_rate_2": 53.3, "percent_cases_well_formed": 99.6, "total_cost": 11.0338, "command": "aider --model openrouter/x-ai/grok-3", "edit_format": "diff", "details": {"test_cases": 225, "pass_rate_1": 22.2, "pass_num_1": 50, "pass_num_2": 120, "error_outputs": 1, "seconds_per_case": 15.3}}]