import * as React from "react";
import { Star, TrendingUp, DollarSign, Clock, CheckCircle, AlertCircle, InfoIcon } from "lucide-react";
import type { EnrichedModelData } from "../models/types";
import type { ModelRecommendation } from "@/types/model-recommendations";
import { generateUseCaseRecommendations, STANDARD_USE_CASES } from "@/services/model-recommendations";

interface ModelRecommendationsProps {
  model: EnrichedModelData;
}

interface RecommendationCardProps {
  recommendation: ModelRecommendation;
  useCaseName: string;
  useCaseDescription: string;
}

const SuitabilityBadge: React.FC<{ suitability: string }> = ({ suitability }) => {
  const getConfig = (suitability: string) => {
    switch (suitability) {
      case 'excellent':
        return { color: 'bg-green-100 text-green-800', label: 'Ausgezeichnet' };
      case 'good':
        return { color: 'bg-blue-100 text-blue-800', label: 'Gut' };
      case 'acceptable':
        return { color: 'bg-yellow-100 text-yellow-800', label: 'Ak<PERSON>ptabel' };
      case 'limited':
        return { color: 'bg-red-100 text-red-800', label: 'Begrenzt' };
      default:
        return { color: 'bg-gray-100 text-gray-800', label: 'Unbekannt' };
    }
  };

  const config = getConfig(suitability);
  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
      {config.label}
    </span>
  );
};

const CostEffectivenessBadge: React.FC<{ effectiveness: string }> = ({ effectiveness }) => {
  const getConfig = (effectiveness: string) => {
    switch (effectiveness) {
      case 'high':
        return { color: 'bg-green-100 text-green-700', icon: TrendingUp, label: 'Hoch' };
      case 'medium':
        return { color: 'bg-blue-100 text-blue-700', icon: DollarSign, label: 'Mittel' };
      case 'low':
        return { color: 'bg-orange-100 text-orange-700', icon: AlertCircle, label: 'Niedrig' };
      default:
        return { color: 'bg-gray-100 text-gray-700', icon: InfoIcon, label: 'Unbekannt' };
    }
  };

  const config = getConfig(effectiveness);
  const Icon = config.icon;

  return (
    <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
      <Icon className="w-3 h-3" />
      {config.label}
    </div>
  );
};

const RecommendationCard: React.FC<RecommendationCardProps> = ({ 
  recommendation, 
  useCaseName, 
  useCaseDescription 
}) => {
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 65) return 'text-blue-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <Star className="w-4 h-4 fill-current" />;
    if (score >= 65) return <CheckCircle className="w-4 h-4" />;
    if (score >= 50) return <Clock className="w-4 h-4" />;
    return <AlertCircle className="w-4 h-4" />;
  };

  return (
    <div className="border rounded-lg p-4 bg-white hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-3">
        <div>
          <h4 className="font-semibold text-gray-900 mb-1">{useCaseName}</h4>
          <p className="text-sm text-gray-600">{useCaseDescription}</p>
        </div>
        <div className="flex items-center gap-2 ml-4">
          <div className={`flex items-center gap-1 ${getScoreColor(recommendation.score)}`}>
            {getScoreIcon(recommendation.score)}
            <span className="text-xl font-bold">{recommendation.score}</span>
            <span className="text-sm">/100</span>
          </div>
        </div>
      </div>

      <div className="flex gap-2 mb-3">
        <SuitabilityBadge suitability={recommendation.suitability} />
        <CostEffectivenessBadge effectiveness={recommendation.costEffectiveness} />
      </div>

      <div className="space-y-3">
        <p className="text-sm text-gray-700">{recommendation.reasoning}</p>

        {recommendation.strengths.length > 0 && (
          <div>
            <h5 className="text-xs font-semibold text-green-700 mb-1">STÄRKEN</h5>
            <ul className="text-xs text-gray-600 space-y-1">
              {recommendation.strengths.map((strength, _index) => (
                <li key={`strength-${strength.slice(0, 20)}`} className="flex items-start gap-1">
                  <CheckCircle className="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" />
                  {strength}
                </li>
              ))}
            </ul>
          </div>
        )}

        {recommendation.limitations.length > 0 && (
          <div>
            <h5 className="text-xs font-semibold text-orange-700 mb-1">LIMITIERUNGEN</h5>
            <ul className="text-xs text-gray-600 space-y-1">
              {recommendation.limitations.map((limitation, _index) => (
                <li key={`limitation-${limitation.slice(0, 20)}`} className="flex items-start gap-1">
                  <AlertCircle className="w-3 h-3 text-orange-500 mt-0.5 flex-shrink-0" />
                  {limitation}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export const ModelRecommendations: React.FC<ModelRecommendationsProps> = ({ model }) => {
  // Generate recommendations for all use cases if model card is available
  const useCaseRecommendations = React.useMemo(() => {
    if (!model.modelCard) return [];
    
    return STANDARD_USE_CASES.map(useCase => {
      const recommendations = generateUseCaseRecommendations([model.modelCard!], useCase);
      const modelRec = recommendations.recommendedModels.find(r => r.modelId === model.modelCard?.basicInfo.modelId) ||
                      recommendations.alternativeModels.find(r => r.modelId === model.modelCard?.basicInfo.modelId) ||
                      recommendations.notRecommendedModels?.find(r => r.modelId === model.modelCard?.basicInfo.modelId);
      
      return {
        useCase,
        recommendation: modelRec
      };
    }).filter(item => item.recommendation !== undefined);
  }, [model.modelCard]);

  // Sort by score descending
  const sortedRecommendations = useCaseRecommendations
    .sort((a, b) => (b.recommendation?.score || 0) - (a.recommendation?.score || 0));

  if (!model.modelCard) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <InfoIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Keine Model Card verfügbar</h3>
          <p className="text-gray-600">
            Empfehlungen können nur für Modelle mit vollständigen Model Card Daten generiert werden.
          </p>
        </div>
      </div>
    );
  }

  if (sortedRecommendations.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Keine Empfehlungen verfügbar</h3>
          <p className="text-gray-600">
            Für dieses Modell konnten keine Use Case Empfehlungen generiert werden.
          </p>
        </div>
      </div>
    );
  }

  // Separate into categories
  const excellentRecs = sortedRecommendations.filter(r => (r.recommendation?.score ?? 0) >= 80);
  const goodRecs = sortedRecommendations.filter(r => (r.recommendation?.score ?? 0) >= 65 && (r.recommendation?.score ?? 0) < 80);
  const acceptableRecs = sortedRecommendations.filter(r => (r.recommendation?.score ?? 0) >= 50 && (r.recommendation?.score ?? 0) < 65);
  const limitedRecs = sortedRecommendations.filter(r => (r.recommendation?.score ?? 0) < 50);

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-semibold text-blue-900 mb-2">Use Case Empfehlungen</h3>
        <p className="text-sm text-blue-800">
          Diese Empfehlungen basieren auf Benchmark-Performance, Capabilities, Kosten und Latenz des Modells 
          für verschiedene Standard-Anwendungsfälle in Unternehmen.
        </p>
      </div>

      {excellentRecs.length > 0 && (
        <div>
          <h4 className="font-semibold text-lg text-green-700 mb-3 flex items-center gap-2">
            <Star className="w-5 h-5 fill-current" />
            Ausgezeichnet geeignet ({excellentRecs.length})
          </h4>
          <div className="grid gap-4">
            {excellentRecs.map(({ useCase, recommendation }) => (
              <RecommendationCard
                key={useCase.id}
                recommendation={recommendation!}
                useCaseName={useCase.name}
                useCaseDescription={useCase.description}
              />
            ))}
          </div>
        </div>
      )}

      {goodRecs.length > 0 && (
        <div>
          <h4 className="font-semibold text-lg text-blue-700 mb-3 flex items-center gap-2">
            <CheckCircle className="w-5 h-5" />
            Gut geeignet ({goodRecs.length})
          </h4>
          <div className="grid gap-4">
            {goodRecs.map(({ useCase, recommendation }) => (
              <RecommendationCard
                key={useCase.id}
                recommendation={recommendation!}
                useCaseName={useCase.name}
                useCaseDescription={useCase.description}
              />
            ))}
          </div>
        </div>
      )}

      {acceptableRecs.length > 0 && (
        <div>
          <h4 className="font-semibold text-lg text-yellow-700 mb-3 flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Akzeptabel ({acceptableRecs.length})
          </h4>
          <div className="grid gap-4">
            {acceptableRecs.map(({ useCase, recommendation }) => (
              <RecommendationCard
                key={useCase.id}
                recommendation={recommendation!}
                useCaseName={useCase.name}
                useCaseDescription={useCase.description}
              />
            ))}
          </div>
        </div>
      )}

      {limitedRecs.length > 0 && (
        <div>
          <h4 className="font-semibold text-lg text-red-700 mb-3 flex items-center gap-2">
            <AlertCircle className="w-5 h-5" />
            Begrenzt geeignet ({limitedRecs.length})
          </h4>
          <div className="grid gap-4">
            {limitedRecs.map(({ useCase, recommendation }) => (
              <RecommendationCard
                key={useCase.id}
                recommendation={recommendation!}
                useCaseName={useCase.name}
                useCaseDescription={useCase.description}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};