*User:**

<task>
Extrahiere die "Berechnungsmethodik der Empfehlungen" in eine eigene Komponente und mache diese auf der Empfehlungsseite im Header verlinkt aufrufbar!
'src/pages/empfehlungen/index.astro' (see below for file content) 
'src/components/models/RecommendationsPage.tsx' (see below for file content)
</task>

<file_content path="src/pages/empfehlungen/index.astro">
  1 | ---
  2 | import Layout from '../../layouts/Layout.astro';
  3 | import { RecommendationsPage } from '../../components/models/RecommendationsPage';
  4 | import { CollapsibleHeader } from '../../components/models/CollapsibleHeader';
  5 | import type { ModelCardsData } from '../../types/model-cards';
  6 | import * as fs from 'node:fs/promises';
  7 | import * as path from 'node:path';
  8 | 
  9 | // Statische Daten zur Build-Zeit laden (GitLab Pages kompatibel)
 10 | // Verwende absoluten Pfad basierend auf dem Projekt-Root
 11 | const projectRoot = path.resolve(process.cwd());
 12 | const dataPath = path.join(projectRoot, 'src', 'data');
 13 | 
 14 | const modelCardsData = JSON.parse(await fs.readFile(path.join(dataPath, 'model-cards.json'), 'utf-8')) as ModelCardsData;
 15 | 
 16 | // Extrahiere nur die Model Cards
 17 | const modelCards = modelCardsData.modelCards;
 18 | 
 19 | // Berechne Statistiken
 20 | const totalModels = modelCards.length;
 21 | const providersSet = new Set(modelCards.map(card => card.basicInfo.provider));
 22 | const totalProviders = providersSet.size;
 23 | 
 24 | // Zähle verfügbare Modelle (GA Status)
 25 | const availableModels = modelCards.filter(card => card.basicInfo.status === 'GA').length;
 26 | 
 27 | // Berechne durchschnittliche Kosten
 28 | const avgInputCost = modelCards.reduce((sum, card) => sum + card.pricing.inputCostPer1MTokens, 0) / totalModels;
 29 | const avgOutputCost = modelCards.reduce((sum, card) => sum + card.pricing.outputCostPer1MTokens, 0) / totalModels;
 30 | ---
 31 | 
 32 | <Layout title="Model-Empfehlungen - Intelligente Auswahl für Unternehmen">
 33 |   <main class="container mx-auto px-4 py-8">
 34 |     <!-- Kollabierbare Header-Komponente -->
 35 |     <CollapsibleHeader
 36 |       title="Model-Empfehlungen für Unternehmen"
 37 |       description={`Intelligente Empfehlungssystem für ${totalModels} LLM-Modelle von ${totalProviders} Anbietern. Basierend auf Standard-Anwendungsfällen in Unternehmen werden die optimalen Modelle für verschiedene Szenarien empfohlen. Berücksichtigt werden Benchmark-Performance, Capabilities, Kosten und weitere Faktoren für fundierte Entscheidungen.`}
 38 |       client:only="react"
 39 |     >
 40 |       <!-- Statistik-Übersicht -->
 41 |       <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
 42 |         <div class="bg-white rounded-lg shadow-md p-6">
 43 |           <h3 class="text-sm font-medium text-gray-500">Verfügbare Modelle</h3>
 44 |           <p class="text-3xl font-bold text-blue-600">{totalModels}</p>
 45 |         </div>
 46 |         <div class="bg-white rounded-lg shadow-md p-6">
 47 |           <h3 class="text-sm font-medium text-gray-500">Anbieter</h3>
 48 |           <p class="text-3xl font-bold text-green-600">{totalProviders}</p>
 49 |         </div>
 50 |         <div class="bg-white rounded-lg shadow-md p-6">
 51 |           <h3 class="text-sm font-medium text-gray-500">GA Status</h3>
 52 |           <p class="text-3xl font-bold text-purple-600">{availableModels}</p>
 53 |         </div>
 54 |         <div class="bg-white rounded-lg shadow-md p-6">
 55 |           <h3 class="text-sm font-medium text-gray-500">⌀ Input Kosten</h3>
 56 |           <p class="text-2xl font-bold text-orange-600">${avgInputCost.toFixed(2)}</p>
 57 |           <p class="text-xs text-gray-500">pro 1M Tokens</p>
 58 |         </div>
 59 |         <div class="bg-white rounded-lg shadow-md p-6">
 60 |           <h3 class="text-sm font-medium text-gray-500">⌀ Output Kosten</h3>
 61 |           <p class="text-2xl font-bold text-red-600">${avgOutputCost.toFixed(2)}</p>
 62 |           <p class="text-xs text-gray-500">pro 1M Tokens</p>
 63 |         </div>
 64 |       </div>
 65 |     </CollapsibleHeader>
 66 | 
 67 |     <!-- React Island für die Empfehlungs-Seite -->
 68 |     <RecommendationsPage
 69 |       modelCards={modelCards}
 70 |       client:only="react"
 71 |     />
 72 | 
 73 |     <!-- Ausführliche Erklärung der Berechnungsmethodik -->
 74 |     <section class="bg-gray-50 border-t mt-16 py-12">
 75 |       <div class="w-full px-4">
 76 |         <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">
 77 |           🧮 Berechnungsmethodik der Empfehlungen
 78 |         </h2>
 79 |         
 80 |         <div class="w-full space-y-8">
 81 |           <!-- Übersicht des Scoring-Algorithmus -->
 82 |           <div class="bg-white rounded-lg shadow-md p-6">
 83 |             <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
 84 |               <span class="text-2xl mr-2">🎯</span>
 85 |               Scoring-Algorithmus Übersicht
 86 |             </h3>
 87 |             <p class="text-gray-700 mb-4">
 88 |               Unser intelligentes Empfehlungssystem bewertet jedes Modell für jeden Use Case anhand eines 100-Punkte-Systems.
 89 |               Der Gesamtscore setzt sich aus fünf gewichteten Faktoren zusammen:
 90 |             </p>
 91 |             <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
 92 |               <div class="text-center p-4 bg-blue-50 rounded-lg">
 93 |                 <div class="text-2xl font-bold text-blue-600">45%</div>
 94 |                 <div class="text-sm text-gray-700">Benchmark Performance</div>
 95 |               </div>
 96 |               <div class="text-center p-4 bg-green-50 rounded-lg">
 97 |                 <div class="text-2xl font-bold text-green-600">25%</div>
 98 |                 <div class="text-sm text-gray-700">Erforderliche Capabilities</div>
 99 |               </div>
100 |               <div class="text-center p-4 bg-purple-50 rounded-lg">
101 |                 <div class="text-2xl font-bold text-purple-600">10%</div>
102 |                 <div class="text-sm text-gray-700">Kosten-Effizienz</div>
103 |               </div>
104 |               <div class="text-center p-4 bg-orange-50 rounded-lg">
105 |                 <div class="text-2xl font-bold text-orange-600">15%</div>
106 |                 <div class="text-sm text-gray-700">Latenz/Geschwindigkeit</div>
107 |               </div>
108 |               <div class="text-center p-4 bg-teal-50 rounded-lg">
109 |                 <div class="text-2xl font-bold text-teal-600">5%</div>
110 |                 <div class="text-sm text-gray-700">Verfügbarkeit</div>
111 |               </div>
112 |             </div>
113 |           </div>
114 | 
115 |           <!-- Benchmark-Performance Berechnung -->
116 |           <div class="bg-white rounded-lg shadow-md p-6">
117 |             <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
118 |               <span class="text-2xl mr-2">📊</span>
119 |               Benchmark-Performance (45% Gewichtung)
120 |             </h3>
121 |             <div class="space-y-4">
122 |               <p class="text-gray-700">
123 |                 Für jeden Use Case werden relevante Benchmarks identifiziert und die Modell-Performance entsprechend bewertet:
124 |               </p>
125 |               <div class="bg-gray-50 p-4 rounded-lg">
126 |                 <h4 class="font-medium text-gray-900 mb-2">Use Case Details:</h4>
127 |                 <p class="text-sm text-gray-700">
128 |                   Jeder Use Case zeigt nun direkt die verwendeten Benchmarks und benötigten Capabilities in der Empfehlungsansicht an.
129 |                   Dies ermöglicht eine transparente Nachvollziehbarkeit der Bewertung für jeden spezifischen Anwendungsfall.
130 |                 </p>
131 |               </div>
132 |               <div class="bg-blue-50 p-4 rounded-lg">
133 |                <h4 class="font-medium text-blue-900 mb-2">Wichtige Optimierungen:</h4>
134 |                <p class="text-sm text-blue-800">
135 |                  • Kritische Coding-Benchmarks erhalten 1.5x Gewichtung<br>
136 |                  • Coding-Leader-Bonuses für Top-Performer (Claude Sonnet 4/Opus 4: +5, Gemini 2.5 Pro: +4)<br>
137 |                  • Qualitäts-Penalties für schlechte Performance (&lt;40% = -5 Punkte)
138 |                </p>
139 |              </div>
140 |             </div>
141 |           </div>
142 | 
143 |           <!-- Weitere Bewertungsfaktoren -->
144 |           <div class="bg-white rounded-lg shadow-md p-6">
145 |             <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
146 |               <span class="text-2xl mr-2">⚙️</span>
147 |               Weitere Bewertungsfaktoren
148 |             </h3>
149 |             <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
150 |               <div class="bg-green-50 p-4 rounded-lg">
151 |                 <h4 class="font-medium text-green-900 mb-2">⚙️ Capabilities (25%)</h4>
152 |                 <p class="text-sm text-green-800">
153 |                   Binäre Bewertung: Vollständige Unterstützung = 100 Punkte,
154 |                   teilweise = proportional
155 |                 </p>
156 |               </div>
157 |               <div class="bg-blue-50 p-4 rounded-lg">
158 |                 <h4 class="font-medium text-blue-900 mb-2">💰 Kosten (10%)</h4>
159 |                 <p class="text-sm text-blue-800">
160 |                   Reduzierte Gewichtung verhindert unfaire Bevorzugung billiger Modelle
161 |                 </p>
162 |               </div>
163 |               <div class="bg-purple-50 p-4 rounded-lg">
164 |                 <h4 class="font-medium text-purple-900 mb-2">⚡ Latenz & Verfügbarkeit (20%)</h4>
165 |                 <p class="text-sm text-purple-800">
166 |                   Geschwindigkeit und Produktionsbereitschaft
167 |                 </p>
168 |               </div>
169 |             </div>
170 |           </div>
171 | 
172 |           <!-- Qualitätsfaktoren & Coding-Optimierungen -->
173 |           <div class="bg-white rounded-lg shadow-md p-6">
174 |             <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
175 |               <span class="text-2xl mr-2">🔧</span>
176 |               Qualitätsfaktoren & Coding-Optimierungen (2025)
177 |             </h3>
178 |             <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
179 |               <div class="bg-red-50 p-4 rounded-lg">
180 |                 <h4 class="font-medium text-red-900 mb-2">❌ Qualitäts-Penalties</h4>
181 |                 <p class="text-sm text-red-800">
182 |                   -5 Punkte für schlechte Performance in kritischen Bereichen (z.B. SimpleQA &lt;40%)
183 |                 </p>
184 |               </div>
185 |               <div class="bg-green-50 p-4 rounded-lg">
186 |                 <h4 class="font-medium text-green-900 mb-2">✅ Coding-Leader-Bonuses</h4>
187 |                 <p class="text-sm text-green-800">
188 |                   Claude Sonnet 4/Opus 4: +5 Punkte, Gemini 2.5 Pro: +4 Punkte für Code-Use-Cases
189 |                 </p>
190 |               </div>
191 |               <div class="bg-blue-50 p-4 rounded-lg">
192 |                 <h4 class="font-medium text-blue-900 mb-2">🖥️ Context & Multimodal</h4>
193 |                 <p class="text-sm text-blue-800">
194 |                   Bonuses für große Context Windows und Vision-Capabilities
195 |                 </p>
196 |               </div>
197 |               <div class="bg-purple-50 p-4 rounded-lg">
198 |                 <h4 class="font-medium text-purple-900 mb-2">⚡ Kritische Benchmarks</h4>
199 |                 <p class="text-sm text-purple-800">
200 |                   1.5x Gewichtung für SWE-bench, HumanEval, LiveCodeBench, Aider, Terminal-bench
201 |                 </p>
202 |               </div>
203 |             </div>
204 |           </div>
205 | 
206 |           <!-- Bewertungsskala und Transparenz -->
207 |           <div class="bg-white rounded-lg shadow-md p-6">
208 |             <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
209 |               <span class="text-2xl mr-2">🏆</span>
210 |               Bewertungsskala & Transparenz
211 |             </h3>
212 |             <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
213 |               <div>
214 |                 <h4 class="font-medium text-gray-900 mb-3">Suitability-Kategorien:</h4>
215 |                 <div class="space-y-2 text-sm">
216 |                   <div class="flex justify-between">
217 |                     <span>⭐ Ausgezeichnet</span>
218 |                     <span class="font-bold text-green-600">≥ 80 Punkte</span>
219 |                   </div>
220 |                   <div class="flex justify-between">
221 |                     <span>✅ Gut</span>
222 |                     <span class="font-bold text-blue-600">65-79 Punkte</span>
223 |                   </div>
224 |                   <div class="flex justify-between">
225 |                     <span>⏰ Akzeptabel</span>
226 |                     <span class="font-bold text-yellow-600">50-64 Punkte</span>
227 |                   </div>
228 |                   <div class="flex justify-between">
229 |                     <span>⚠️ Begrenzt</span>
230 |                     <span class="font-bold text-red-600">&lt; 50 Punkte</span>
231 |                   </div>
232 |                 </div>
233 |               </div>
234 |               <div>
235 |                 <h4 class="font-medium text-gray-900 mb-3">Qualitätssicherung:</h4>
236 |                 <div class="space-y-2 text-sm text-gray-700">
237 |                   <div>🎯 Objektive, messbare Metriken</div>
238 |                   <div>📊 Offizielle Benchmark-Daten</div>
239 |                   <div>🔄 Regelmäßige Updates</div>
240 |                   <div>🔍 Transparente Algorithmen</div>
241 |                 </div>
242 |               </div>
243 |             </div>
244 |           </div>
245 | 
246 |           <!-- Disclaimer -->
247 |           <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
248 |             <h3 class="text-lg font-semibold text-yellow-900 mb-3 flex items-center">
249 |               <span class="text-xl mr-2">⚠️</span>
250 |               Wichtiger Hinweis
251 |             </h3>
252 |             <p class="text-sm text-yellow-800">
253 |               Diese Empfehlungen basieren auf algorithmischen Berechnungen und dienen als Orientierungshilfe.
254 |               Für produktive Anwendungen sollten immer eigene Tests und Evaluationen durchgeführt werden.
255 |               Preise und Verfügbarkeiten können sich ändern. Stand der Daten: {new Date().toLocaleDateString('de-DE')}.
256 |             </p>
257 |           </div>
258 |         </div>
259 |       </div>
260 |     </section>
261 |   </main>
262 | </Layout>
263 | 
264 | <style>
265 |   .container {
266 |     max-width: 1400px;
267 |   }
268 | </style>

</file_content>

<file_content path="src/components/models/RecommendationsPage.tsx">
  1 | import * as React from "react";
  2 | import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
  3 | import { UseCaseOverviewDashboard } from "./UseCaseOverviewDashboard";
  4 | import { generateUseCaseRecommendations, findBestModelsForUseCases, STANDARD_USE_CASES } from "@/services/model-recommendations";
  5 | import type { ModelCard } from "@/types/model-cards";
  6 | import type { UseCaseRecommendations } from "@/types/model-recommendations";
  7 | import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
  8 | import { Star, TrendingUp, Target, BarChart3 } from "lucide-react";
  9 | 
 10 | interface RecommendationsPageProps {
 11 |   modelCards: ModelCard[];
 12 |   onModelSelect?: (modelId: string) => void;
 13 | }
 14 | 
 15 | interface TopModelCardProps {
 16 |   modelCard: ModelCard;
 17 |   averageScore: number;
 18 |   useCaseScores: { [useCaseId: string]: number };
 19 |   onModelSelect?: (modelId: string) => void;
 20 | }
 21 | 
 22 | const TopModelCard: React.FC<TopModelCardProps> = ({ modelCard, averageScore, useCaseScores, onModelSelect }) => {
 23 |   const getScoreColor = (score: number) => {
 24 |     if (score >= 80) return 'text-green-600 bg-green-50';
 25 |     if (score >= 65) return 'text-blue-600 bg-blue-50';
 26 |     if (score >= 50) return 'text-yellow-600 bg-yellow-50';
 27 |     return 'text-red-600 bg-red-50';
 28 |   };
 29 | 
 30 |   const topUseCases = Object.entries(useCaseScores)
 31 |     .sort(([,a], [,b]) => b - a)
 32 |     .slice(0, 3)
 33 |     .map(([useCaseId, score]) => ({
 34 |       name: STANDARD_USE_CASES.find(uc => uc.id === useCaseId)?.name || useCaseId,
 35 |       score
 36 |     }));
 37 | 
 38 |   return (
 39 |     <Card 
 40 |       className="cursor-pointer hover:shadow-md transition-shadow"
 41 |       onClick={() => onModelSelect?.(modelCard.basicInfo.modelId)}
 42 |     >
 43 |       <CardHeader>
 44 |         <div className="flex justify-between items-start">
 45 |           <div>
 46 |             <CardTitle className="text-lg">{modelCard.basicInfo.displayName}</CardTitle>
 47 |             <CardDescription>{modelCard.basicInfo.provider}</CardDescription>
 48 |           </div>
 49 |           <div className={`px-3 py-1 rounded-lg font-bold ${getScoreColor(averageScore)}`}>
 50 |             {averageScore.toFixed(1)}
 51 |           </div>
 52 |         </div>
 53 |       </CardHeader>
 54 |       <CardContent>
 55 |         <div className="space-y-2">
 56 |           <h4 className="text-sm font-medium text-gray-700">Top Use Cases:</h4>
 57 |           {topUseCases.map((uc, index) => (
 58 |             <div key={index} className="flex justify-between items-center text-sm">
 59 |               <span className="text-gray-600">{uc.name}</span>
 60 |               <span className={`font-medium ${getScoreColor(uc.score)}`}>
 61 |                 {uc.score.toFixed(1)}
 62 |               </span>
 63 |             </div>
 64 |           ))}
 65 |         </div>
 66 |         <div className="mt-4 pt-3 border-t">
 67 |           <div className="text-xs text-gray-500">
 68 |             ${modelCard.pricing.inputCostPer1MTokens.toFixed(2)}/1M Input • 
 69 |             ${modelCard.pricing.outputCostPer1MTokens.toFixed(2)}/1M Output
 70 |           </div>
 71 |         </div>
 72 |       </CardContent>
 73 |     </Card>
 74 |   );
 75 | };
 76 | 
 77 | interface UseCaseDetailCardProps {
 78 |   useCaseRecommendations: UseCaseRecommendations;
 79 |   onModelSelect?: (modelId: string) => void;
 80 | }
 81 | 
 82 | const UseCaseDetailCard: React.FC<UseCaseDetailCardProps> = ({ useCaseRecommendations, onModelSelect }) => {
 83 |   const { useCase, recommendedModels, alternativeModels } = useCaseRecommendations;
 84 |   
 85 |   const allModels = [...recommendedModels, ...alternativeModels].slice(0, 5);
 86 | 
 87 |   return (
 88 |     <Card>
 89 |       <CardHeader>
 90 |         <CardTitle className="flex items-center gap-2">
 91 |           <Target className="w-5 h-5 text-blue-600" />
 92 |           {useCase.name}
 93 |         </CardTitle>
 94 |         <CardDescription>{useCase.description}</CardDescription>
 95 |         
 96 |         {/* Use Case Details: Benchmarks and Capabilities */}
 97 |         <div className="mt-4 space-y-3">
 98 |           <div className="bg-blue-50 p-3 rounded-lg">
 99 |             <h4 className="text-sm font-medium text-blue-900 mb-2">📊 Verwendete Benchmarks:</h4>
100 |             <div className="flex flex-wrap gap-1">
101 |               {useCase.optimalBenchmarks.map((benchmark, index) => (
102 |                 <span
103 |                   key={index}
104 |                   className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md"
105 |                 >
106 |                   {benchmark}
107 |                 </span>
108 |               ))}
109 |             </div>
110 |           </div>
111 |           
112 |           <div className="bg-green-50 p-3 rounded-lg">
113 |             <h4 className="text-sm font-medium text-green-900 mb-2">⚙️ Benötigte Capabilities:</h4>
114 |             <div className="flex flex-wrap gap-1">
115 |               {useCase.requiredCapabilities.map((capability, index) => (
116 |                 <span
117 |                   key={index}
118 |                   className="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-md"
119 |                 >
120 |                   {capability}
121 |                 </span>
122 |               ))}
123 |             </div>
124 |           </div>
125 |           
126 |           <div className="bg-gray-50 p-2 rounded-lg">
127 |             <div className="flex items-center justify-between text-xs">
128 |               <span className="text-gray-600">Kategorie: <span className="font-medium">{useCase.category}</span></span>
129 |               <span className={`px-2 py-1 rounded-md font-medium ${
130 |                 useCase.priority === 'high' ? 'bg-red-100 text-red-800' :
131 |                 useCase.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
132 |                 'bg-gray-100 text-gray-800'
133 |               }`}>
134 |                 {useCase.priority === 'high' ? 'Hoch' : useCase.priority === 'medium' ? 'Mittel' : 'Niedrig'} Priorität
135 |               </span>
136 |             </div>
137 |           </div>
138 |         </div>
139 |       </CardHeader>
140 |       <CardContent>
141 |         <div className="space-y-3">
142 |           <h4 className="text-sm font-medium text-gray-700 border-b pb-2">Top Empfehlungen:</h4>
143 |           {allModels.map((rec, index) => (
144 |             <div
145 |               key={rec.modelId}
146 |               className="flex justify-between items-center p-2 border rounded cursor-pointer hover:bg-gray-50"
147 |               onClick={() => onModelSelect?.(rec.modelId)}
148 |             >
149 |               <div className="flex items-center gap-2">
150 |                 <span className="text-lg">{index + 1}.</span>
151 |                 <div>
152 |                   <div className="font-medium">{rec.modelId}</div>
153 |                   <div className="text-xs text-gray-600">{rec.suitability}</div>
154 |                 </div>
155 |               </div>
156 |               <div className="text-right">
157 |                 <div className="font-bold text-blue-600">{rec.score}</div>
158 |                 <div className="text-xs text-gray-500">
159 |                   {rec.costEffectiveness === 'high' ? 'Kostengünstig' :
160 |                    rec.costEffectiveness === 'medium' ? 'Standard' : 'Teuer'}
161 |                 </div>
162 |               </div>
163 |             </div>
164 |           ))}
165 |         </div>
166 |       </CardContent>
167 |     </Card>
168 |   );
169 | };
170 | 
171 | export const RecommendationsPage: React.FC<RecommendationsPageProps> = ({ modelCards, onModelSelect }) => {
172 |   // Generate comprehensive recommendations
173 |   const allUseCaseRecommendations = React.useMemo(() => {
174 |     return STANDARD_USE_CASES.map(useCase => 
175 |       generateUseCaseRecommendations(modelCards, useCase)
176 |     );
177 |   }, [modelCards]);
178 | 
179 |   // Find best overall models across multiple use cases
180 |   const bestOverallModels = React.useMemo(() => {
181 |     const allUseCaseIds = STANDARD_USE_CASES.map(uc => uc.id);
182 |     return findBestModelsForUseCases(modelCards, allUseCaseIds).slice(0, 6);
183 |   }, [modelCards]);
184 | 
185 |   // Find best models for high-priority use cases
186 |   const highPriorityUseCases = STANDARD_USE_CASES.filter(uc => uc.priority === 'high').map(uc => uc.id);
187 |   const bestHighPriorityModels = React.useMemo(() => {
188 |     return findBestModelsForUseCases(modelCards, highPriorityUseCases).slice(0, 6);
189 |   }, [modelCards]);
190 | 
191 |   // Get statistics
192 |   const stats = React.useMemo(() => {
193 |     const totalRecommendations = allUseCaseRecommendations.reduce((sum, rec) => 
194 |       sum + rec.recommendedModels.length, 0
195 |     );
196 |     
197 |     const excellentRecommendations = allUseCaseRecommendations.reduce((sum, rec) => 
198 |       sum + rec.recommendedModels.filter(r => r.suitability === 'excellent').length, 0
199 |     );
200 | 
201 |     return {
202 |       totalUseCases: STANDARD_USE_CASES.length,
203 |       totalModels: modelCards.length,
204 |       totalRecommendations,
205 |       excellentRecommendations,
206 |       averageRecommendationsPerUseCase: totalRecommendations / STANDARD_USE_CASES.length
207 |     };
208 |   }, [allUseCaseRecommendations, modelCards]);
209 | 
210 |   return (
211 |     <div className="space-y-6">
212 |       {/* Header */}
213 |       <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
214 |         <h1 className="text-3xl font-bold text-gray-900 mb-4">
215 |           Model-Empfehlungen für Unternehmen
216 |         </h1>
217 |         <p className="text-gray-700 mb-6">
218 |           Intelligente Empfehlungen für die Auswahl des optimalen LLM-Modells basierend auf 
219 |           Standard-Anwendungsfällen in Unternehmen. Berücksichtigt Performance, Kosten, 
220 |           Capabilities und weitere Faktoren.
221 |         </p>
222 |         
223 |         {/* Statistics */}
224 |         <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
225 |           <div className="bg-white rounded-lg p-4 border">
226 |             <div className="text-2xl font-bold text-blue-600">{stats.totalUseCases}</div>
227 |             <div className="text-sm text-gray-600">Use Cases</div>
228 |           </div>
229 |           <div className="bg-white rounded-lg p-4 border">
230 |             <div className="text-2xl font-bold text-green-600">{stats.totalModels}</div>
231 |             <div className="text-sm text-gray-600">Modelle</div>
232 |           </div>
233 |           <div className="bg-white rounded-lg p-4 border">
234 |             <div className="text-2xl font-bold text-purple-600">{stats.totalRecommendations}</div>
235 |             <div className="text-sm text-gray-600">Empfehlungen</div>
236 |           </div>
237 |           <div className="bg-white rounded-lg p-4 border">
238 |             <div className="text-2xl font-bold text-orange-600">{stats.excellentRecommendations}</div>
239 |             <div className="text-sm text-gray-600">Exzellent</div>
240 |           </div>
241 |           <div className="bg-white rounded-lg p-4 border">
242 |             <div className="text-2xl font-bold text-teal-600">{stats.averageRecommendationsPerUseCase.toFixed(1)}</div>
243 |             <div className="text-sm text-gray-600">⌀ pro Use Case</div>
244 |           </div>
245 |         </div>
246 |       </div>
247 | 
248 |       {/* Main Content Tabs */}
249 |       <Tabs defaultValue="overview" className="w-full">
250 |         <TabsList className="grid w-full grid-cols-4">
251 |           <TabsTrigger value="overview">Übersicht</TabsTrigger>
252 |           <TabsTrigger value="top-models">Top Modelle</TabsTrigger>
253 |           <TabsTrigger value="high-priority">Kritische Use Cases</TabsTrigger>
254 |           <TabsTrigger value="use-cases">Alle Use Cases</TabsTrigger>
255 |         </TabsList>
256 |         
257 |         <TabsContent value="overview" className="space-y-6">
258 |           <UseCaseOverviewDashboard 
259 |             modelCards={modelCards} 
260 |             onModelSelect={onModelSelect} 
261 |           />
262 |         </TabsContent>
263 |         
264 |         <TabsContent value="top-models" className="space-y-6">
265 |           <div>
266 |             <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
267 |               <Star className="w-6 h-6 text-yellow-500" />
268 |               Beste Gesamtmodelle
269 |             </h2>
270 |             <p className="text-gray-600 mb-6">
271 |               Modelle mit der besten durchschnittlichen Performance über alle Use Cases hinweg.
272 |             </p>
273 |             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
274 |               {bestOverallModels.map((result) => {
275 |                 const modelCard = modelCards.find(mc => mc.basicInfo.modelId === result.modelId);
276 |                 return modelCard ? (
277 |                   <TopModelCard
278 |                     key={result.modelId}
279 |                     modelCard={modelCard}
280 |                     averageScore={result.averageScore}
281 |                     useCaseScores={result.useCaseScores}
282 |                     onModelSelect={onModelSelect}
283 |                   />
284 |                 ) : null;
285 |               })}
286 |             </div>
287 |           </div>
288 |         </TabsContent>
289 |         
290 |         <TabsContent value="high-priority" className="space-y-6">
291 |           <div>
292 |             <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
293 |               <TrendingUp className="w-6 h-6 text-red-500" />
294 |               Kritische Use Cases
295 |             </h2>
296 |             <p className="text-gray-600 mb-6">
297 |               Empfehlungen für geschäftskritische Anwendungsfälle mit hoher Priorität.
298 |             </p>
299 |             
300 |             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
301 |               {bestHighPriorityModels.map((result) => {
302 |                 const modelCard = modelCards.find(mc => mc.basicInfo.modelId === result.modelId);
303 |                 return modelCard ? (
304 |                   <TopModelCard
305 |                     key={result.modelId}
306 |                     modelCard={modelCard}
307 |                     averageScore={result.averageScore}
308 |                     useCaseScores={result.useCaseScores}
309 |                     onModelSelect={onModelSelect}
310 |                   />
311 |                 ) : null;
312 |               })}
313 |             </div>
314 |             
315 |             <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
316 |               {allUseCaseRecommendations
317 |                 .filter(rec => rec.useCase.priority === 'high')
318 |                 .map((rec) => (
319 |                   <UseCaseDetailCard
320 |                     key={rec.useCase.id}
321 |                     useCaseRecommendations={rec}
322 |                     onModelSelect={onModelSelect}
323 |                   />
324 |                 ))}
325 |             </div>
326 |           </div>
327 |         </TabsContent>
328 |         
329 |         <TabsContent value="use-cases" className="space-y-6">
330 |           <div>
331 |             <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
332 |               <BarChart3 className="w-6 h-6 text-blue-500" />
333 |               Alle Use Cases
334 |             </h2>
335 |             <p className="text-gray-600 mb-6">
336 |               Detaillierte Empfehlungen für jeden Standard-Anwendungsfall.
337 |             </p>
338 |             <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
339 |               {allUseCaseRecommendations.map((rec) => (
340 |                 <UseCaseDetailCard
341 |                   key={rec.useCase.id}
342 |                   useCaseRecommendations={rec}
343 |                   onModelSelect={onModelSelect}
344 |                 />
345 |               ))}
346 |             </div>
347 |           </div>
348 |         </TabsContent>
349 |       </Tabs>
350 |     </div>
351 |   );
352 | };

</file_content>
<environment_details>
# VSCode Visible Files


# VSCode Open Tabs


# Current Time
6/9/2025, 11:52:31 AM (Europe/Berlin, UTC+2:00)

# Current Context Size (Tokens)
(Not available)

# Current Cost
$0.00

# Current Mode
<slug>flow-architect</slug>
<name>Flow-Architect</name>
<model>claude-sonnet-4-********</model>


# Current Workspace Directory (d:/Source/iteratec/iteratec-llm-browser) Files


(File list truncated. Use list_files on specific subdirectories if you need to explore further.)
</environment_details>

---

**Assistant:**

[MEMORY BANK: INACTIVE]

<thinking>
Der Benutzer möchte die "Berechnungsmethodik der Empfehlungen" aus der Empfehlungsseite in eine eigene Komponente extrahieren und diese im Header verlinkt aufrufbar machen. 

Aktuell befindet sich die gesamte Berechnungsmethodik in der Astro-Datei `src/pages/empfehlungen/index.astro` ab Zeile 73-260. Das ist ein großer Abschnitt mit detaillierten Erklärungen zum Scoring-Algorithmus.

Ich sollte:
1. Eine neue React-Komponente für die Berechnungsmethodik erstellen
2. Den entsprechenden Abschnitt aus der Astro-Datei in die neue Komponente verschieben
3. Die Astro-Datei entsprechend anpassen
4. Einen Link/Button im Header der RecommendationsPage hinzufügen, um die Methodik aufzurufen

Zuerst sollte ich die aktuelle Projektstruktur verstehen und dann die neue Komponente erstellen.
</thinking>

Ich werde die Berechnungsmethodik in eine eigene Komponente extrahieren und diese über den Header der Empfehlungsseite zugänglich machen. Lassen Sie mich zunächst die Projektstruktur analysieren.

<list_files>
<path>src/components</path>
<recursive>true</recursive>
</list_files>

Jetzt erstelle ich eine neue Komponente für die Berechnungsmethodik:

<write_to_file>
<path>src/components/models/CalculationMethodology.tsx</path>


[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]

---

**User:**

[list_files for 'src/components'] Result:
benchmark/
benchmark/BenchmarkDetailDialog.tsx
benchmark/BenchmarkTable.tsx
benchmark/BenchmarkTableIsland.tsx
layout/
layout/AppLayout.tsx
models/
models/CollapsibleHeader.tsx
models/ExportOptions.tsx
models/ModelComparison.tsx
models/ModelDetailDialog.tsx
models/ModelRecommendations.tsx
models/ModelTable.tsx
models/ModelTableIsland.tsx
models/RecommendationsPage.tsx
models/types.ts
models/UseCaseOverviewDashboard.tsx
models/utils.ts
ui/
ui/button.tsx
ui/card.tsx
ui/dialog.tsx
ui/form.tsx
ui/input.tsx
ui/label.tsx
ui/select.tsx
ui/sonner.tsx
ui/table.tsx
ui/tabs.tsx
ui/tooltip.tsx
Tool [write_to_file] was not executed because a tool has already been used in this message. Only one tool may be used per message. You must assess the first tool's result before proceeding to use the next tool.
<environment_details>
# VSCode Visible Files


# VSCode Open Tabs


# Current Time
6/9/2025, 11:52:42 AM (Europe/Berlin, UTC+2:00)

# Current Context Size (Tokens)
26,665 (13%)

# Current Cost
$0.10

# Current Mode
<slug>flow-architect</slug>
<name>Flow-Architect</name>
<model>claude-sonnet-4-********</model>
</environment_details>

---

**Assistant:**

Perfekt! Ich sehe die Struktur der Komponenten. Jetzt erstelle ich eine neue Komponente für die Berechnungsmethodik:

<write_to_file>
<path>src/components/models/CalculationMethodology.tsx</path>
<content>
import * as React from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "../ui/dialog";
import { Button } from "../ui/button";
import { Calculator, Info } from "lucide-react";

interface CalculationMethodologyProps {
  trigger?: React.ReactNode;
}

export const CalculationMethodology: React.FC<CalculationMethodologyProps> = ({ 
  trigger 
}) => {
  const defaultTrigger = (
    <Button variant="outline" size="sm" className="gap-2">
      <Calculator className="w-4 h-4" />
      Berechnungsmethodik
    </Button>
  );

  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Calculator className="w-6 h-6 text-blue-600" />
            Berechnungsmethodik der Empfehlungen
          </DialogTitle>
          <DialogDescription>
            Detaillierte Erklärung des intelligenten Empfehlungssystems und der verwendeten Algorithmen
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 mt-6">
          {/* Übersicht des Scoring-Algorithmus */}
          <div className="bg-white rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <span className="text-2xl mr-2">🎯</span>
              Scoring-Algorithmus Übersicht
            </h3>
            <p className="text-gray-700 mb-4">
              Unser intelligentes Empfehlungssystem bewertet jedes Modell für jeden Use Case anhand eines 100-Punkte-Systems.
              Der Gesamtscore setzt sich aus fünf gewichteten Faktoren zusammen:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">45%</div>
                <div className="text-sm text-gray-700">Benchmark Performance</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">25%</div>
                <div className="text-sm text-gray-700">Erforderliche Capabilities</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">10%</div>
                <div className="text-sm text-gray-700">Kosten-Effizienz</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">15%</div>
                <div className="text-sm text-gray-700">Latenz/Geschwindigkeit</div>
              </div>
              <div className="text-center p-4 bg-teal-50 rounded-lg">
                <div className="text-2xl font-bold text-teal-600">5%</div>
                <div className="text-sm text-gray-700">Verfügbarkeit</div>
              </div>
            </div>
          </div>

          {/* Benchmark-Performance Berechnung */}
          <div className="bg-white rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <span className="text-2xl mr-2">📊</span>
              Benchmark-Performance (45% Gewichtung)
            </h3>
            <div className="space-y-4">
              <p className="text-gray-700">
                Für jeden Use Case werden relevante Benchmarks identifiziert und die Modell-Performance entsprechend bewertet:
              </p>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Use Case Details:</h4>
                <p className="text-sm text-gray-700">
                  Jeder Use Case zeigt nun direkt die verwendeten Benchmarks und benötigten Capabilities in der Empfehlungsansicht an.
                  Dies ermöglicht eine transparente Nachvollziehbarkeit der Bewertung für jeden spezifischen Anwendungsfall.
                </p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
               <h4 className="font-medium text-blue-900 mb-2">Wichtige Optimierungen:</h4>
               <p className="text-sm text-blue-800">
                 • Kritische Coding-Benchmarks erhalten 1.5x Gewichtung<br />
                 • Coding-Leader-Bonuses für Top-Performer (Claude Sonnet 4/Opus 4: +5, Gemini 2.5 Pro: +4)<br />
                 • Qualitäts-Penalties für schlechte Performance (&lt;40% = -5 Punkte)
               </p>
             </div>
            </div>
          </div>

          {/* Weitere Bewertungsfaktoren */}
          <div className="bg-white rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <span className="text-2xl mr-2">⚙️</span>
              Weitere Bewertungsfaktoren
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">⚙️ Capabilities (25%)</h4>
                <p className="text-sm text-green-800">
                  Binäre Bewertung: Vollständige Unterstützung = 100 Punkte,
                  teilweise = proportional
                </p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">💰 Kosten (10%)</h4>
                <p className="text-sm text-blue-800">
                  Reduzierte Gewichtung verhindert unfaire Bevorzugung billiger Modelle
                </p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-2">⚡ Latenz & Verfügbarkeit (20%)</h4>
                <p className="text-sm text-purple-800">
                  Geschwindigkeit und Produktionsbereitschaft
                </p>
              </div>
            </div>
          </div>

          {/* Qualitätsfaktoren & Coding-Optimierungen */}
          <div className="bg-white rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <span className="text-2xl mr-2">🔧</span>
              Qualitätsfaktoren & Coding-Optimierungen (2025)
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-red-50 p-4 rounded-lg">
                <h4 className="font-medium text-red-900 mb-2">❌ Qualitäts-Penalties</h4>
                <p className="text-sm text-red-800">
                  -5 Punkte für schlechte Performance in kritischen Bereichen (z.B. SimpleQA &lt;40%)
                </p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">✅ Coding-Leader-Bonuses</h4>
                <p className="text-sm text-green-800">
                  Claude Sonnet 4/Opus 4: +5 Punkte, Gemini 2.5 Pro: +4 Punkte für Code-Use-Cases
                </p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">🖥️ Context & Multimodal</h4>
                <p className="text-sm text-blue-800">
                  Bonuses für große Context Windows und Vision-Capabilities
                </p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-2">⚡ Kritische Benchmarks</h4>
                <p className="text-sm text-purple-800">
                  1.5x Gewichtung für SWE-bench, HumanEval, LiveCodeBench, Aider, Terminal-bench
                </p>
              </div>
            </div>
          </div>

          {/* Bewertungsskala und Transparenz */}
          <div className="bg-white rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <span className="text-2xl mr-2">🏆</span>
              Bewertungsskala & Transparenz
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Suitability-Kategorien:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>⭐ Ausgezeichnet</span>
                    <span className="font-bold text-green-600">≥ 80 Punkte</span>
                  </div>
                  <div className="flex justify-between">
                    <span>✅ Gut</span>
                    <span className="font-bold text-blue-600">65-79 Punkte</span>
                  </div>
                  <div className="flex justify-between">
                    <span>⏰ Akzeptabel</span>
                    <span className="font-bold text-yellow-600">50-64 Punkte</span>
                  </div>
                  <div className="flex justify-between">
                    <span>⚠️ Begrenzt</span>
                    <span className="font-bold text-red-600">&lt; 50 Punkte</span>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Qualitätssicherung:</h4>
                <div className="space-y-2 text-sm text-gray-700">
                  <div>🎯 Objektive, messbare Metriken</div>
                  <div>📊 Offizielle Benchmark-Daten</div>
                  <div>🔄 Regelmäßige Updates</div>
                  <div>🔍 Transparente Algorithmen</div>
                </div>
              </div>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-900 mb-3 flex items-center">
              <span className="text-xl mr-2">⚠️</span>
              Wichtiger Hinweis
            </h3>
            <p className="text-sm text-yellow-800">
              Diese Empfehlungen basieren auf algorithmischen Berechnungen und dienen als Orientierungshilfe.
              Für produktive Anwendungen sollten immer eigene Tests und Evaluationen durchgeführt werden.
              Preise und Verfügbarkeiten können sich ändern. Stand der Daten: {new Date().toLocaleDateString('de-DE')}.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
</content>
<line_count>174</line_count>
</write_to_file>

---

**User:**

[write_to_file for 'src/components/models/CalculationMethodology.tsx'] Result:
<file_write_result>
<path>src/components/models/CalculationMethodology.tsx</path>
<operation>created</operation>
<notice>
<i>You do not need to re-read the file, as you have seen all changes</i>
<i>Proceed with the task using these changes as the new baseline.</i>
</notice>
</file_write_result>

<environment_details>
# VSCode Visible Files


# VSCode Open Tabs


# Current Time
6/9/2025, 11:53:52 AM (Europe/Berlin, UTC+2:00)

# Current Context Size (Tokens)
31,028 (16%)

# Current Cost
$0.16

# Current Mode
<slug>flow-architect</slug>
<name>Flow-Architect</name>
<model>claude-sonnet-4-********</model>
</environment_details>

