import type { ModelData } from "../../types/api";
import type { ModelCard, ModelCardsData } from "../../types/model-cards";
import type { EnrichedModelData } from "./types";

// Import static data
import modelCardsData from "../../data/model-cards.json";
import modelMappingsData from "../../data/model-mappings-used.json";

// Helper function to find model card for a given model
export const findModelCard = (model: ModelData): ModelCard | null => {
  const mappings = modelMappingsData.mappings.mappings;
  const modelCards = (modelCardsData as ModelCardsData).modelCards;
  
  // Try to find mapping by model name first
  let mapping = mappings[model.name || ''];
  
  // If not found, try by model ID
  if (!mapping && model.id) {
    mapping = mappings[model.id];
  }
  
  // If still not found, try by provider/model combination
  if (!mapping && model.provider && model.name) {
    const providerKey = `${model.provider.toLowerCase()}/${model.name.toLowerCase()}`;
    mapping = mappings[providerKey];
  }
  
  if (!mapping) return null;
  
  // Find the model card by ID
  return modelCards.find(card => card.basicInfo.modelId === mapping.modelCardId) || null;
};

// Helper function to enrich model data with model card information
export const enrichModelWithCard = (model: ModelData): EnrichedModelData => {
  const modelCard = findModelCard(model);
  
  if (!modelCard) return model as EnrichedModelData;
  
  // Create enriched model data
  const enrichedModel: EnrichedModelData = {
    ...model,
    // Keep original name and provider (with prefixes like aws/, gcp/ etc.)
    // Only override technical specs and capabilities with model card data where available
    contextWindow: modelCard.technicalSpecs.contextWindow || model.contextWindow,
    maxOutputTokens: modelCard.technicalSpecs.maxOutputTokens || model.maxOutputTokens,
    inputCostPerToken: modelCard.pricing.inputCostPer1MTokens ? modelCard.pricing.inputCostPer1MTokens / 1000000 : model.inputCostPerToken,
    outputCostPerToken: modelCard.pricing.outputCostPer1MTokens ? modelCard.pricing.outputCostPer1MTokens / 1000000 : model.outputCostPerToken,
    
    // Map capabilities from model card
    supportsVision: modelCard.capabilities.vision ?? model.supportsVision,
    supportsPdfInput: modelCard.capabilities.pdfSupport ?? model.supportsPdfInput,
    supportsAudioInput: modelCard.capabilities.audioInput ?? model.supportsAudioInput,
    supportsAudioOutput: modelCard.capabilities.audioOutput ?? model.supportsAudioOutput,
    supportsFunctionCalling: modelCard.capabilities.functionCalling ?? model.supportsFunctionCalling,
    supportsPromptCaching: modelCard.capabilities.promptCaching ?? model.supportsPromptCaching,
    supportsReasoning: modelCard.capabilities.reasoning ?? model.supportsReasoning,
    supportsSystemMessages: modelCard.capabilities.systemInstructions ?? model.supportsSystemMessages,
    supportsEmbeddingImageInput: modelCard.capabilities.embeddingImageInput ?? model.supportsEmbeddingImageInput,
    
    // Add model card reference for detailed view
    modelCard: modelCard
  };
  
  return enrichedModel;
};