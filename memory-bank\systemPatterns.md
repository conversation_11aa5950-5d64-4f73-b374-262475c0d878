# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.

## Coding Patterns

- **Component Structure**: ShadCN UI components in src/components/ui/ with consistent prop interfaces
- **Import Aliases**: @/ prefix for src/ directory imports for cleaner import statements
- **TypeScript Strict Mode**: Full type safety with interfaces for API responses and component props
- **CSS Variables**: Theme customization via CSS custom properties for consistent theming
- **Client Components**: "use client" directive for interactive components requiring browser APIs

## Architectural Patterns

- **Service Layer Pattern**: Separation of API logic in src/services/ modules
- **Context Provider Pattern**: ModelDataContext for global state management
- **Static Generation**: Next.js static optimization for benchmark data pages
- **Component Composition**: Atomic design with ui/ components composed into feature components
- **File-based Routing**: App Router convention with page.tsx files in route directories

## Testing Patterns

- **Component Testing**: Expected patterns for UI component unit tests
- **API Testing**: Service layer testing with mock data
- **Integration Testing**: End-to-end testing for user workflows
- **Accessibility Testing**: ARIA compliance validation for ShadCN components

## Development Patterns

- **Hot Reload**: Next.js development server with Turbopack for fast updates
- **Linting**: ESLint configuration with Next.js specific rules
- **Type Checking**: TypeScript compilation checks in build process
- **Version Control**: .gitignore excluding .next/, node_modules/, and build artifacts

## Data Flow Patterns

- **API Integration**: Service → Context → Component data flow
- **Static Data**: JSON files → Import → Processing → Display pipeline
- **State Management**: Context providers for cross-component data sharing
- **Error Handling**: Graceful fallbacks for API failures and missing data

2025-06-05 11:45:09 - Initial Memory Bank creation during UMB operation with system patterns from project analysis.
[2025-06-05 15:32:00] - Responsive Design Pattern: ModelTable uses max-w-[1400px] container with horizontal scroll wrapper, min-width columns, and responsive dialog sizing for large screen compatibility
[2025-06-05 16:11:10] - Data Display Pattern: ModelTable dialog uses specific boolean support properties (supportsPdfInput, supportsAudioInput, supportsAudioOutput, supportsEmbeddingImageInput) instead of generic arrays for accurate capability visualization
[2025-06-05 16:17:45] - Data Architecture Pattern: Exclusively uses /info endpoint for all model data, removed external LiteLLM GitHub dependency for simplified and authoritative data sourcing
[2025-06-05 16:52:30] - Data Transformation Pattern: Comprehensive snake_case to camelCase mapping in API service layer for all support properties. Ensures UI consistency by transforming API responses (supports_function_calling) to UI expectations (supportsFunctionCalling) in both fetchModelInfo() and fetchCombinedModelData() functions.
[2025-06-05 16:52:30] - CSS Architecture Pattern: Container sizing with CSS custom properties (--container-lg: 1200px) and responsive media queries (@media (width >= 40rem)) for consistent layout constraints across viewport sizes.
[2025-06-05 16:52:30] - Dialog Layout Pattern: Top-aligned modal content using flexbox (flex flex-col items-start justify-start) with full-width tab content (w-full) for optimal space utilization in detail views.
[2025-06-14 09:03:30] - Theme Architecture Pattern: Comprehensive Dark/Light theme system implemented using next-themes with Astro Island architecture
  - **Theme Provider Pattern**: ThemeProvider wrapper component for global theme state management
  - **Island Component Pattern**: ThemeToggleIsland.tsx for client-side theme switching in Astro SSG environment
  - **Theme-Aware CSS Pattern**: All UI elements use Tailwind theme tokens (bg-background, text-foreground, text-muted-foreground) instead of hardcoded colors
  - **FOUC Prevention Pattern**: Inline script in Layout.astro to initialize theme before hydration
  - **Icon Transition Pattern**: Smooth rotate/scale transitions for Sun/Moon icons using CSS transforms
  - **Navigation Integration Pattern**: Theme toggle positioned as dedicated UI element between navigation items and logo