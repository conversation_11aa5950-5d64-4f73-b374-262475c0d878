import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    // Test-Umgebung für DOM-Tests
    environment: 'jsdom',
    
    // Setup-Dateien
    setupFiles: ['./src/test/setup.ts'],
    
    // Globals für bessere DX
    globals: true,
    
    // Coverage-Konfiguration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        'dist/',
        'public/',
        'static/',
        'scripts/',
        'docs/',
        'memory-bank/',
        '**/*.astro',
        'src/pages/',
        'src/layouts/',
        'src/content/',
        'src/data/',
        'src/mocks/',
      ],
      thresholds: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70,
        },
      },
    },
    
    // Include/Exclude Patterns
    include: [
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
    ],
    exclude: [
      'node_modules/',
      'dist/',
      'public/',
      'static/',
      '.astro/',
    ],
    
    // Test-Timeout
    testTimeout: 10000,
    
    // Reporter-Konfiguration
    reporters: ['verbose', 'json'],
    outputFile: {
      json: './test-results/results.json',
    },
  },
  
  // Resolve-Konfiguration für Imports
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/components': resolve(__dirname, './src/components'),
      '@/utils': resolve(__dirname, './src/utils'),
      '@/types': resolve(__dirname, './src/types'),
      '@/services': resolve(__dirname, './src/services'),
      '@/lib': resolve(__dirname, './src/lib'),
    },
  },
  
  // Vite-spezifische Konfiguration für Tests
  define: {
    'import.meta.vitest': undefined,
  },
});