{"basicInfo": {"modelId": "claude-sonnet-4@20250514", "displayName": "<PERSON> 4", "provider": "Anthropic", "modelFamily": "<PERSON>", "version": "20250514", "description": "Das mittlere Modell von Anthropic mit überlegener Intelligenz für hohes Volumen, z. B. für Programmierung, ausführliche Recherchen und Kundenservicemitarbeiter. <PERSON> 4 bietet eine erhebliche Verbesserung gegenüber Sonnet 3.7 mit verbesserter Codierung und Argumentation.", "releaseDate": "2025-05-22", "status": "GA", "knowledgeCutoff": "März 2025"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 64000, "architecture": "Transformer", "supportedInputTypes": ["text", "image", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 35, "inputTokensPerMinute": 280000, "outputTokensPerMinute": 20000}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 3.0, "outputCostPer1MTokens": 15.0, "cachingCosts": {"cacheWrites": 3.75, "cacheHits": 0.3}, "currency": "USD"}, "availability": {"regions": [{"region": "us-east5", "availability": "GA"}, {"region": "europe-west1", "availability": "GA"}, {"region": "global", "availability": "GA"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI", "AWS Bedrock", "Anthropic API"]}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": true, "fixedQuota": true}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 72.7, "alternativeScores": {"multipleAttempts": 80.2}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Mit bash/editor tools. Alternative Score mit parallel test-time compute."}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 35.5, "alternativeScores": {"multipleAttempts": 41.3}, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Agentic terminal coding mit Claude Code als agent framework."}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 75.4, "alternativeScores": {"multipleAttempts": 83.8}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Alternative Score mit extended thinking."}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic coding", "score": 80.5, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Agentic tool use benchmark - Retail domain."}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic coding", "score": 60.0, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Agentic tool use benchmark - Airline domain."}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 86.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Multilingual Q&A - Durchschnitt über 14 nicht-englische Sprachen."}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 75.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "MMMU benchmark from vals.ai (Claude Sonnet 4 Thinking)"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 70.5, "alternativeScores": {"multipleAttempts": 85.0}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "High school math competition. Alternative Score mit extended thinking."}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 76.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "AIME 2024 and 2025 combined benchmark from vals.ai (Claude Sonnet 4 Thinking)"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 61.3, "alternativeScores": {"withThinking": 61.3, "noThinking": 56.4}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-24", "notes": "Aider polyglot benchmark with diff edit format (32k thinking tokens vs no thinking)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 97.3, "alternativeScores": {"withThinking": 97.3, "noThinking": 98.2}, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-05-24", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1389.18, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #3 (tied)"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 70.9, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 5.52, "alternativeScores": {"thinking": 7.76}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "<PERSON> 4: 5.52±0.90, <PERSON> 4 (Thinking): 7.76±1.05"}], "metadata": {"lastUpdated": "2025-06-09T12:47:00Z", "dataSource": "Google Cloud Vertex AI Documentation, Anthropic Blog Post, Anthropic Documentation, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard", "version": "1.3"}}