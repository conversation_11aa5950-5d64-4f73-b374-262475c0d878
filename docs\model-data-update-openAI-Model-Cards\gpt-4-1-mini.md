```
<PERSON><PERSON><PERSON> eine neue Modelkarte für "gpt-4.1.mini" unter src/data/models/gpt-4.1.mini.json mit der Grundstruktur auf Basis des Schemas unter @/src/data/models/model-card-json-schema.md . Achte auf die exakte Schreibweise der Benchmarks aus @/src/data/benchmarks/benchmark-descriptions.json! Andernfalls ist ein Matching nicht möglich! Achte zudem darauf das entsprechend Schema die Kosten korrekt übernommen werden!


**Step 1:**
Model-Card-Basis-Infos:
- @https://platform.openai.com/docs/models/gpt-4.1-mini


Nutze als Referenzbeispiel: 
<example>
@/src/data/models/claude-sonnet-4.json 
</example>

**Step 2:**
Prüfe ob weitere Benchmarkdaten aktualisiert werden können. 

- Benchmark: "AIME": @https://www.vals.ai/benchmarks/aime-2025-05-30 
- Benchmark: "MMMU": @https://www.vals.ai/benchmarks/mmmu-05-30-2025 
- Benchmark: "SWE-bench Verified": @https://www.swebench.com/index.html 
- Benchmark: "Terminal-Bench": @https://www.tbench.ai/leaderboard 
- Benchmark: "Webdev-Arena": @https://web.lmarena.ai/leaderboard 
- Benchmark: "GPQA-Diamond": @https://www.vellum.ai/llm-leaderboard 
- Benchmark: "LiveCodeBench v2025": @https://livecodebench.github.io/leaderboard.html 
- Benchmark: "Humanity's Last Exam": @https://scale.com/leaderboard/humanitys_last_exam_text_only

Achte auf die exakte Schreibweise der Benchmarks aus @/src/data/benchmarks/benchmark-descriptions.json! Andernfalls ist ein Matching nicht möglich!

**Step 3:**
Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md .

**Step 4:**
Aktualisiere auch die Polyglot-Benchmarks entsprechend @/src/data/benchmarks/benchmark-descriptions.json auf Basis von @/src/data/polyglot_benchmarks.json in @/src/data/models/magistral-medium.json  . Achte darauf dass folgende WErte findest und schreibst:

- "Aider-Polyglot-Wellformated"
- "Aider-Polyglot"
```
