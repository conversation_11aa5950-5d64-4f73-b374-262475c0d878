import * as React from "react";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../ui/table";
import { <PERSON><PERSON> } from "../../ui/button";
import { Card, CardContent } from "../../ui/card";
import { Maximize2, Minimize2 } from "lucide-react";
import type { BenchmarkData } from "../../../types/api";

type SortField = "model" | "pass_rate_2" | "percent_cases_well_formed" | "total_cost" | "edit_format";
type SortDirection = "asc" | "desc";

interface BenchmarkTableProps {
  benchmarks: BenchmarkData[];
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  sortField: SortField;
  sortDirection: SortDirection;
  onSort: (field: SortField) => void;
  onPageChange: (page: number) => void;
  onBenchmarkSelect: (benchmark: BenchmarkData) => void;
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
}

export function BenchmarkTable({
  benchmarks,
  currentPage,
  totalPages,
  itemsPerPage,
  sortField,
  sortDirection,
  onSort,
  onPageChange,
  onBenchmarkSelect,
  isFullscreen = false,
  onToggleFullscreen,
}: BenchmarkTableProps) {
  // Pagination
  const paginatedBenchmarks = benchmarks.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Get performance color class
  const getPerformanceColor = (score: number) => {
    if (score >= 80) return "text-green-600 dark:text-green-400 font-bold";
    if (score >= 60) return "text-blue-600 dark:text-blue-400 font-semibold";
    if (score >= 40) return "text-orange-600 dark:text-orange-400";
    return "text-red-600 dark:text-red-400";
  };

  return (
    <Card className={`w-full ${isFullscreen ? 'max-w-none' : 'max-w-[1400px]'} mx-auto`}>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table className="min-w-full">
            <TableCaption>
              <div className={`flex justify-between items-center ${isFullscreen ? 'p-3' : 'p-5'}`}>
                <div>
                  Zeige {(currentPage - 1) * itemsPerPage + 1} bis{" "}
                  {Math.min(currentPage * itemsPerPage, benchmarks.length)} von{" "}
                  {benchmarks.length} Benchmarks
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    Zurück
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(Math.min(currentPage + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  >
                    Weiter
                  </Button>
                </div>
              </div>
            </TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">#</TableHead>
                <TableHead className="cursor-pointer min-w-[200px]" onClick={() => onSort("model")}>
                  Modell {sortField === "model" && (sortDirection === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead className="cursor-pointer min-w-[120px]" onClick={() => onSort("pass_rate_2")}>
                  Score {sortField === "pass_rate_2" && (sortDirection === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead className="min-w-[120px]">
                  Bestanden
                </TableHead>
                <TableHead className="cursor-pointer min-w-[120px]" onClick={() => onSort("percent_cases_well_formed")}>
                  Wohlgeformt {sortField === "percent_cases_well_formed" && (sortDirection === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead className="cursor-pointer min-w-[100px]" onClick={() => onSort("total_cost")}>
                  Kosten {sortField === "total_cost" && (sortDirection === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead className="cursor-pointer min-w-[120px]" onClick={() => onSort("edit_format")}>
                  Format {sortField === "edit_format" && (sortDirection === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead className="text-right min-w-[80px]">
                  <div className="flex items-center justify-end gap-2">
                    <span>Details</span>
                    {onToggleFullscreen && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={onToggleFullscreen}
                        className="h-6 w-6 p-0"
                        title={isFullscreen ? "Filter anzeigen" : "Vollbild"}
                      >
                        {isFullscreen ? (
                          <Minimize2 className="w-4 h-4" />
                        ) : (
                          <Maximize2 className="w-4 h-4" />
                        )}
                      </Button>
                    )}
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedBenchmarks.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    Keine Benchmarks gefunden
                  </TableCell>
                </TableRow>
              ) : (
                paginatedBenchmarks.map((benchmark, index) => (
                  <TableRow key={`${benchmark.model}-${benchmark.pass_rate_2}`}>
                    <TableCell className="text-muted-foreground">
                      {(currentPage - 1) * itemsPerPage + index + 1}
                    </TableCell>
                    <TableCell className="font-medium">{benchmark.model}</TableCell>
                    <TableCell className={getPerformanceColor(benchmark.pass_rate_2)}>
                      {benchmark.pass_rate_2.toFixed(1)}%
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">
                        {benchmark.details.pass_num_2} / {benchmark.details.test_cases || 225}
                      </span>
                    </TableCell>
                    <TableCell>
                      {benchmark.percent_cases_well_formed.toFixed(1)}%
                    </TableCell>
                    <TableCell>
                      ${benchmark.total_cost?.toFixed(2) || "N/A"}
                    </TableCell>
                    <TableCell>
                      {benchmark.edit_format || "N/A"}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onBenchmarkSelect(benchmark)}
                      >
                        Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}