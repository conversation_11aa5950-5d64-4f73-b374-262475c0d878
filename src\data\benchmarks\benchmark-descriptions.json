{"benchmarks": {"MMLU": {"fullName": "Massive Multitask Language Understanding", "category": "Reasoning & Knowledge", "description": "Umfassende Bewertung des Allgemeinwissens und der Reasoning-Fähigkeiten über 57 verschiedene Fachbereiche", "shortDescription": "Allgemeinwissen und Reasoning über 57 Fachbereiche", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt beantworteten Fragen", "scoreRange": "0-100%", "difficulty": "Mittel bis Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["5-shot", "0-shot CoT"], "usedBy": ["Claude 3.5 Sonnet v2", "Mistral Large 2411", "GPT-4.1", "GPT-4.1 nano"], "website": "https://github.com/hendrycks/test", "paperUrl": "https://arxiv.org/abs/2009.03300"}, "GPQA Diamond": {"fullName": "Graduate-Level Google-Proof Q&A Diamond", "category": "Science", "description": "Extrem schwierige naturwissenschaftliche Fragen auf Graduate-Level, die selbst für Experten herausfordernd sind", "shortDescription": "Graduate-level naturwissenschaftliche Fragen", "metric": "Accuracy / Pass Rate", "metricDescription": "Prozentsatz der korrekt beantworteten wissenschaftlichen Fragen", "scoreRange": "0-100%", "difficulty": "Sehr Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["0-shot CoT", "Extended Thinking"], "usedBy": ["Claude 3.5 Sonnet v2", "<PERSON> 4", "Claude 3.7 Sonnet", "<PERSON> 4", "Gemini 2.5 Pro", "GPT-4.1", "GPT-4.1 nano", "o3-mini", "o3"], "website": "https://www.vellum.ai/llm-leaderboard", "paperUrl": "https://arxiv.org/abs/2311.12022"}, "DROP": {"fullName": "Discrete Reasoning Over Paragraphs", "category": "Reasoning & Knowledge", "description": "Reading Comprehension mit numerischem und diskretem Reasoning über Textpassagen", "shortDescription": "Reading Comprehension mit numerischem Reasoning", "metric": "F1 Score", "metricDescription": "Harmonisches Mittel aus Precision und Recall", "scoreRange": "0-100", "difficulty": "Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["3-shot"], "usedBy": ["Claude 3.5 Sonnet v2"], "website": "https://allenai.org/data/drop", "paperUrl": "https://arxiv.org/abs/1903.00161"}, "BIG-Bench-Hard": {"fullName": "BIG-Bench Hard", "category": "Reasoning & Knowledge", "description": "Schwierige Aufgaben aus dem BIG-Bench für komplexes Reasoning, bei denen Chain-of-Thought besonders hilfreich ist", "shortDescription": "Schwierige Reasoning-Aufgaben aus BIG-Bench", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt gelösten Aufgaben", "scoreRange": "0-100%", "difficulty": "Sehr Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["3-shot CoT"], "usedBy": ["Claude 3.5 Sonnet v2"], "website": "https://github.com/suzgunmirac/BIG-Bench-Hard", "paperUrl": "https://arxiv.org/abs/2210.09261"}, "Humanity's Last Exam": {"fullName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "description": "Extrem schwi<PERSON>ger Reasoning-Test mit besonders herausfordernden Fragen", "shortDescription": "<PERSON>tre<PERSON> sch<PERSON><PERSON> Reasoning-Test", "metric": "Pass Rate", "metricDescription": "Prozentsatz der bestandenen Prüfungen", "scoreRange": "0-100%", "difficulty": "Extrem Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["No tools"], "usedBy": ["Gemini 2.5 Pro"], "website": "https://scale.com/leaderboard/humanitys_last_exam", "paperUrl": null}, "MATH": {"fullName": "MATH Dataset", "category": "Mathematics", "description": "Mathematische Problemlösung auf Hochschulniveau mit Aufgaben aus verschiedenen mathematischen Bereichen", "shortDescription": "Hochschul-Mathematikaufgaben", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt gelösten mathematischen Probleme", "scoreRange": "0-100%", "difficulty": "Sehr Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["0-shot CoT"], "usedBy": ["Claude 3.5 Sonnet v2", "DeepSeek Coder V2"], "website": "https://github.com/hendrycks/math", "paperUrl": "https://arxiv.org/abs/2103.03874"}, "MGSM": {"fullName": "Multilingual Grade School Math", "category": "Mathematics", "description": "Mehrsprachige Version von GSM8K mit Grundschul-Mathematikaufgaben in verschiedenen Sprachen", "shortDescription": "Mehrsprachige Grundschul-Mathematik", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt gelösten mehrsprachigen Mathematikaufgaben", "scoreRange": "0-100%", "difficulty": "<PERSON><PERSON><PERSON>", "languages": ["Mehrsprachig"], "variants": ["0-shot CoT"], "usedBy": ["Claude 3.5 Sonnet v2"], "website": "https://github.com/google-research/url-nlp", "paperUrl": "https://arxiv.org/abs/2210.03057"}, "AIME 2024": {"fullName": "American Invitational Mathematics Examination 2024", "category": "Mathematics", "description": "High School Mathematik-Wettbewerb 2024 mit besonders anspruchsvollen Aufgaben", "shortDescription": "High School Mathematik-Wettbewerb 2024", "metric": "Pass Rate", "metricDescription": "Prozentsatz der korrekt gelösten AIME-Aufgaben", "scoreRange": "0-100%", "difficulty": "Sehr Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["Extended Thinking", "nucleus sampling top_p=0.95", "single attempt"], "usedBy": ["Claude 3.7 Sonnet", "Gemini 2.5 Pro", "GPT-4.1", "GPT-4.1 nano"], "website": "https://www.maa.org/math-competitions/aime", "paperUrl": null}, "AIME 2025": {"fullName": "American Invitational Mathematics Examination 2025", "category": "Mathematics", "description": "High School Mathematik-Wettbewerb 2025 mit besonders anspruchsvollen Aufgaben", "shortDescription": "High School Mathematik-Wettbewerb 2025", "metric": "Pass Rate", "metricDescription": "Prozentsatz der korrekt gelösten AIME-Aufgaben", "scoreRange": "0-100%", "difficulty": "Sehr Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["Extended Thinking"], "usedBy": ["<PERSON> 4", "<PERSON> 4", "Gemini 2.5 Pro", "o3-mini", "o3"], "website": "https://www.vals.ai/benchmarks/aime-2025-05-30", "paperUrl": null}, "LiveCodeBench v2025": {"fullName": "LiveCodeBench Version 2025", "category": "Code generation", "description": "Holistische und kontaminationsfreie Bewertung von Large Language Models für Code-Generierung mit kontinuierlich aktualisierten Problemen aus aktuellen Programmierwettbewerben. Die neueste Version mit 454 ausgewählten Problemen im aktuellen Zeitfenster.", "shortDescription": "Aktuelle Live-Code-Generierung ohne Kontamination", "metric": "Pass@1", "metricDescription": "Prozentsatz der erfolgreich gelösten Code-Probleme beim ersten Versuch", "scoreRange": "0-100%", "difficulty": "Sehr Hoch", "languages": ["Python", "Java", "C++", "JavaScript", "Go"], "variants": ["Easy-Pass@1", "Medium-Pass@1", "Hard-Pass@1"], "usedBy": ["<PERSON> 4", "<PERSON> 4", "Claude 3.7 Sonnet", "Claude 3.5 Sonnet v2", "Gemini 2.5 Pro", "Gemini 2.5 Flash", "GPT-4.1", "GPT-4.1 nano", "GPT-4o", "GPT-4o mini", "o3-mini", "o3", "o4-mini", "DeepSeek-R1", "Qwen2.5-Coder-32B-Instruct", "Mistral Large 2411"], "website": "https://livecodebench.github.io/leaderboard.html", "paperUrl": "https://arxiv.org/abs/2403.07974"}, "Aider-Polyglot": {"fullName": "Aider-Polyglot", "category": "Code editing", "description": "Code-Bearbeitung in verschiedenen Programmiersprachen mit verschiedenen Editing-Modi", "shortDescription": "Mehrsprachige Code-Bearbeitung", "metric": "Pass Rate", "metricDescription": "Prozentsatz der erfolgreich bearbeiteten Code-Dateien", "scoreRange": "0-100%", "difficulty": "Hoch", "languages": ["Me<PERSON>ere Programmiersprachen"], "variants": ["Whole file editing", "Diff-based editing"], "usedBy": ["Gemini 2.5 Pro", "GPT-4.1", "GPT-4.1 nano", "DeepSeek Coder V2"], "website": "https://aider.chat/docs/leaderboards/", "paperUrl": "/benchmark"}, "MathVista": {"fullName": "MathVista", "category": "Visual reasoning", "description": "Visuelle mathematische Aufgaben, die sowohl Bildverständnis als auch mathematisches Reasoning erfordern", "shortDescription": "Visuelle mathematische Aufgaben", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt gelösten visuellen Mathematikaufgaben", "scoreRange": "0-100%", "difficulty": "Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["single attempt"], "usedBy": ["GPT-4.1", "GPT-4.1 nano"], "website": "https://mathvista.github.io/", "paperUrl": "https://arxiv.org/abs/2310.02255"}, "CharXiv": {"fullName": "CharXiv", "category": "Visual reasoning", "description": "Fragen zu Diagrammen und Charts aus wissenschaftlichen Papieren, die visuelles Verständnis und Reasoning erfordern", "shortDescription": "Reasoning über wissenschaftliche Charts", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt beantworteten Fragen zu wissenschaftlichen Charts", "scoreRange": "0-100%", "difficulty": "Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["single attempt"], "usedBy": ["GPT-4.1", "GPT-4.1 nano"], "website": null, "paperUrl": null}, "Internal API instruction following (hard)": {"fullName": "Internal API Instruction Following (Hard)", "category": "Instruction following", "description": "Interne Bewertung für das Befolgen komplexer API-Anweisungen mit erhöhter Schwierigkeit", "shortDescription": "Komplexe API-Anweisungen befolgen", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt befolgten komplexen Anweisungen", "scoreRange": "0-100%", "difficulty": "Sehr Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["single attempt"], "usedBy": ["GPT-4.1", "GPT-4.1 nano"], "website": null, "paperUrl": null}, "MultiChallenge": {"fullName": "MultiChallenge", "category": "Instruction following", "description": "Multi-Turn Instruction Following Benchmark mit komplexen, mehrstufigen Anweisungen", "shortDescription": "Multi-Turn Instruction Following", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt befolgten mehrstufigen Anweisungen", "scoreRange": "0-100%", "difficulty": "Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["single attempt"], "usedBy": ["GPT-4.1", "GPT-4.1 nano"], "website": null, "paperUrl": null}, "IFEval": {"fullName": "Instruction Following Evaluation", "category": "Instruction following", "description": "Bewertung des Befolgens von Anweisungen mit verifizierbaren Instruktionen", "shortDescription": "Instruction Following mit verifizierbaren Anweisungen", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt befolgten verifizierbaren Anweisungen", "scoreRange": "0-100%", "difficulty": "Mittel bis Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["single attempt"], "usedBy": ["GPT-4.1", "GPT-4.1 nano"], "website": "https://github.com/google-research/google-research/tree/master/instruction_following_eval", "paperUrl": "https://arxiv.org/abs/2311.07911"}, "Multi-IF": {"fullName": "Multi-Instruction Following", "category": "Instruction following", "description": "Multi-Instruction Following Benchmark für komplexe, verschachtelte Anweisungen", "shortDescription": "Multi-Instruction Following", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt befolgten Multi-Instruktionen", "scoreRange": "0-100%", "difficulty": "Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["single attempt"], "usedBy": ["GPT-4.1", "GPT-4.1 nano"], "website": null, "paperUrl": null}, "Graphwalks BFS <128k accuracy": {"fullName": "OpenAI MRCR (Multi-round co-reference resolution)", "category": "Long context", "description": "OpenAI MRCR (Multi-round co-reference resolution) ist ein Long-Context-Dataset zum Benchmarking der Fähigkeit eines LLM, zwischen mehreren versteckten Nadeln im Kontext zu unterscheiden. Die Aufgabe besteht darin, dass das Modell e<PERSON> lange, mehrst<PERSON><PERSON>, synthetisch generierte Konversation zwischen Benutzer und Modell erhält, in der der Benutzer nach einem Text zu einem Thema fragt, z.B. \"schreibe ein Gedicht über Tapire\" oder \"schreibe einen Blogpost über Steine\". Versteckt in dieser Konversation sind 2, 4 oder 8 identische Anfragen, und das Modell wird schließlich aufgefordert, die i-te Instanz einer dieser Anfragen zurückzugeben, z.B. \"Gib das 2. Gedicht über Tapire zurück\".", "shortDescription": "Multi-round co-reference resolution in langen Kontexten", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt identifizierten und zurückgegebenen spezifischen Instanzen von Anfragen", "scoreRange": "0-100%", "difficulty": "Sehr Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["single attempt", "verschiedene Context-Längen bis 128k"], "usedBy": ["GPT-4.1", "GPT-4.1 nano", "GPT-4.1 mini", "GPT-4o", "GPT-4o mini", "o1", "o3-mini"], "website": "https://huggingface.co/datasets/openai/mrcr", "paperUrl": null}, "ComplexFuncBench": {"fullName": "Complex Function Calling Benchmark", "category": "Function calling", "description": "Benchmark für komplexe Function Calling Aufgaben mit verschachtelten und abhängigen Funktionsaufrufen", "shortDescription": "Komplexe Function Calling Aufgaben", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt ausgeführten komplexen Funktionsaufrufe", "scoreRange": "0-100%", "difficulty": "Sehr Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["single attempt"], "usedBy": ["GPT-4.1", "GPT-4.1 nano"], "website": null, "paperUrl": null}, "SWE-bench Verified": {"fullName": "SWE-bench Verified", "category": "Agentic coding", "description": "Reale Software-Engineering-Aufgaben mit verifizierten GitHub Issues", "shortDescription": "Reale Software-Engineering-Aufgaben", "metric": "Pass Rate / Success Rate", "metricDescription": "Prozentsatz der erfolgreich gelösten Software-Engineering-Probleme", "scoreRange": "0-100%", "difficulty": "Sehr Hoch", "languages": ["Python"], "variants": ["bash/editor tools", "parallel test-time compute"], "usedBy": ["<PERSON> 4", "Claude 3.7 Sonnet", "<PERSON> 4", "Gemini 2.5 Pro", "GPT-4.1", "o3-mini", "o3", "DeepSeek Coder V2"], "website": "https://www.swebench.com/index.html", "paperUrl": "https://arxiv.org/abs/2310.06770"}, "Terminal-bench": {"fullName": "Terminal-bench", "category": "Agentic coding", "description": "Agentic terminal-basierte Coding-Aufgaben mit Command-Line-Interface", "shortDescription": "Terminal-basierte Coding-Aufgaben", "metric": "Pass Rate", "metricDescription": "Prozentsatz der erfolgreich über Terminal gelösten Aufgaben", "scoreRange": "0-100%", "difficulty": "Sehr Hoch", "languages": ["Bash/Shell"], "variants": ["Claude Code als Agent Framework"], "usedBy": ["<PERSON> 4", "Claude 3.7 Sonnet", "<PERSON> 4", "GPT-4.1", "o3"], "website": "https://www.tbench.ai/leaderboard", "paperUrl": null}, "TAU-bench Retail": {"fullName": "Tool-Augmented Understanding Benchmark - Retail", "category": "Agentic coding", "description": "Agentic Tool Use Benchmark im Einzelhandels-Kontext", "shortDescription": "Tool-Nutzung im Einzelhandel", "metric": "Success Rate", "metricDescription": "Prozentsatz der erfolgreich mit Tools gelösten Retail-Aufgaben", "scoreRange": "0-100%", "difficulty": "Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["Extended Thinking", "Tool Use"], "usedBy": ["<PERSON> 4", "Claude 3.7 Sonnet", "<PERSON> 4", "GPT-4.1", "GPT-4.1 nano", "o3"], "website": null, "paperUrl": null}, "TAU-bench Airline": {"fullName": "Tool-Augmented Understanding Benchmark - Airline", "category": "Agentic coding", "description": "Agentic Tool Use Benchmark im Airline-Kontext", "shortDescription": "Tool-Nutzung im Airline-Bereich", "metric": "Success Rate", "metricDescription": "Prozentsatz der erfolgreich mit Tools gelösten Airline-Aufgaben", "scoreRange": "0-100%", "difficulty": "Sehr Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["Extended Thinking", "Tool Use"], "usedBy": ["<PERSON> 4", "Claude 3.7 Sonnet", "<PERSON> 4", "GPT-4.1", "GPT-4.1 nano", "o3"], "website": null, "paperUrl": null}, "SimpleQA": {"fullName": "SimpleQA", "category": "Factuality", "description": "Einfache Fragen zur Bewertung der Faktentreue und Korrektheit von Antworten", "shortDescription": "Faktentreue bei einfachen Fragen", "metric": "Accuracy", "metricDescription": "Prozentsatz der faktisch korrekten Antworten", "scoreRange": "0-100%", "difficulty": "<PERSON><PERSON><PERSON>", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["single attempt"], "usedBy": ["Gemini 2.5 Pro"], "website": null, "paperUrl": null}, "MMMU": {"fullName": "Massive Multi-discipline Multimodal Understanding", "category": "Visual reasoning", "description": "Multimodales Verständnis und Reasoning über verschiedene Disziplinen mit Bildern und Text", "shortDescription": "Multimodales Verständnis und Reasoning", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt beantworteten multimodalen Fragen", "scoreRange": "0-100%", "difficulty": "Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["Validation set", "Extended Thinking"], "usedBy": ["<PERSON> 4", "Claude 3.7 Sonnet", "<PERSON> 4", "Gemini 2.5 Pro", "GPT-4.1", "GPT-4.1 nano", "GPT-4o", "o3-mini", "o3"], "website": "https://www.vals.ai/benchmarks/mmmu-05-30-2025", "paperUrl": "https://arxiv.org/abs/2311.16502"}, "Vibe-Eval (Reka)": {"fullName": "Vibe-Eval by <PERSON><PERSON>", "category": "Image understanding", "description": "Bildverständnis-Benchmark von Reka für grundlegendes visuelles Verständnis", "shortDescription": "Grundlegendes Bildverständnis", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt interpretierten Bilder", "scoreRange": "0-100%", "difficulty": "Mittel bis Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["single attempt"], "usedBy": ["Gemini 2.5 Pro"], "website": null, "paperUrl": null}, "MRCR": {"fullName": "Multi-hop Reading Comprehension with Reasoning", "category": "Long context", "description": "Verarbeitung langer Texte und Kontexte mit mehrstufigem Reasoning", "shortDescription": "Long-Context Reading Comprehension", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt beantworteten Long-Context-Fragen", "scoreRange": "0-100%", "difficulty": "Hoch", "languages": ["<PERSON><PERSON><PERSON>"], "variants": ["128k average", "1M pointwise"], "usedBy": ["Gemini 2.5 Pro"], "website": null, "paperUrl": null}, "Global MMLU (Lite)": {"fullName": "Global MMLU Lite", "category": "Multilingual performance", "description": "Globale mehrsprachige Wissensbewertung mit internationalen Perspektiven", "shortDescription": "Globale mehrsprachige Wissensbewertung", "metric": "Accuracy", "metricDescription": "Prozentsatz der korrekt beantworteten globalen Wissensfragen", "scoreRange": "0-100%", "difficulty": "Mittel bis Hoch", "languages": ["Mehrsprachig"], "variants": ["single attempt"], "usedBy": ["Gemini 2.5 Pro"], "website": null, "paperUrl": null}, "WebDev-Arena": {"fullName": "WebDev-Arena", "category": "Agentic coding", "description": "Real-time AI coding competition platform where models compete in web development challenges. Models are evaluated on their ability to create, modify, and debug web applications in a competitive environment.", "shortDescription": "Real-time web development coding competition", "metric": "Arena Score", "metricDescription": "Elo-style rating system based on head-to-head competition results in web development tasks", "scoreRange": "800-1500+ Arena Score", "difficulty": "Sehr Hoch", "languages": ["HTML", "CSS", "JavaScript", "TypeScript", "React", "<PERSON><PERSON>", "Angular"], "variants": ["Real-time competition", "Web application development"], "usedBy": ["<PERSON> 4", "Gemini 2.5 Pro", "<PERSON> 4", "GPT-4.1", "GPT-4o"], "website": "https://web.lmarena.ai/leaderboard", "paperUrl": null}}, "categories": {"Reasoning & Knowledge": {"description": "Tests für logisches Denken und Wissensbewertung", "color": "#3B82F6", "icon": "🧠"}, "Science": {"description": "Naturwissenschaftliche Fragen und Problemlösung", "color": "#10B981", "icon": "🔬"}, "Mathematics": {"description": "Mathematische Problemlösung und Berechnungen", "color": "#8B5CF6", "icon": "📐"}, "Code generation": {"description": "Generierung von Code basierend auf Beschreibungen", "color": "#F59E0B", "icon": "💻"}, "Code editing": {"description": "Bearbeitung und Verbesserung bestehenden Codes", "color": "#EF4444", "icon": "✏️"}, "Agentic coding": {"description": "Komplexe Software-Engineering-Aufgaben mit Tools", "color": "#6366F1", "icon": "🤖"}, "Factuality": {"description": "Korrektheit und Faktentreue der Antworten", "color": "#14B8A6", "icon": "✅"}, "Visual reasoning": {"description": "Bildverständnis und visuelle Logik", "color": "#F97316", "icon": "👁️"}, "Image understanding": {"description": "Grundlegendes Bildverständnis", "color": "#EC4899", "icon": "🖼️"}, "Long context": {"description": "Verarbeitung langer Texte und Kontexte", "color": "#84CC16", "icon": "📄"}, "Multilingual performance": {"description": "Mehrsprachige Fähigkeiten", "color": "#06B6D4", "icon": "🌍"}, "Instruction following": {"description": "<PERSON><PERSON><PERSON><PERSON> von Anweisungen und Instruktionen", "color": "#8B5CF6", "icon": "📋"}, "Function calling": {"description": "Ausführung von Funktionsaufrufen und Tool-Nutzung", "color": "#F59E0B", "icon": "🔧"}, "Academic knowledge": {"description": "Akademisches Wissen und Verständnis", "color": "#3B82F6", "icon": "🎓"}}, "metrics": {"Accuracy": {"description": "Prozentsatz der korrekt beantworteten Fragen oder gelösten Aufgaben", "unit": "%", "range": "0-100"}, "Pass Rate": {"description": "Prozentsatz der erfolgreich bestandenen Tests oder Aufgaben", "unit": "%", "range": "0-100"}, "Success Rate": {"description": "Prozentsatz der erfolgreich abgeschlossenen komplexen Aufgaben", "unit": "%", "range": "0-100"}, "Arena Score": {"description": "Elo-style rating system based on head-to-head competition results", "unit": "Score", "range": "800-1500+"}, "F1 Score": {"description": "Harmonisches Mittel aus Precision und Recall", "unit": "Score", "range": "0-100"}, "Elo Rating": {"description": "Ranking-basierte Bewertung der Fähigkeiten", "unit": "Elo", "range": "800-3500+"}, "Exact Match Accuracy": {"description": "Prozentsatz der exakt übereinstimmenden Antworten ohne Teilpunkte", "unit": "%", "range": "0-100"}}, "difficultyLevels": {"Mittel": {"description": "Standardschwierigkeit für die meisten Nutzer", "color": "#10B981"}, "Mittel bis Hoch": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Schwierigkeit, er<PERSON><PERSON> gute Kenntnisse", "color": "#F59E0B"}, "Hoch": {"description": "<PERSON><PERSON>wi<PERSON>keit, <PERSON><PERSON><PERSON>", "color": "#EF4444"}, "Sehr Hoch": {"description": "Sehr hohe Schwierigkeit, selbst für Experten herausfordernd", "color": "#DC2626"}, "Extrem Hoch": {"description": "Extrem hohe Schwierigkeit, nur für die besten Modelle lösbar", "color": "#991B1B"}}}