# I18N Implementation Plan für Recommendations Page

## Übersicht
Dieser Plan beschreibt die Implementierung einer vollständigen clientseitigen Internationalisierung für die Recommendations-Seite (`src/pages/recommendations/index.astro`) basierend auf dem Astro Framework mit GitLab Pages Kompatibilität.

## Hintergrund
Die Recommendations-Seite verwendet derzeit eine serverseitige Übersetzungslogik mit hartkodierten deutschen Fallbacks. Für eine statische GitLab Pages Deployment muss dies auf clientseitige Spracherkennung und -wechsel umgestellt werden.

## Technische Grundlagen
- **Astro Static Site Generation**: `output: 'static'` bedeutet keine Cookies/Headers zur Build-Zeit
- **Astro Islands Architecture**: React-Komponenten in Astro-Seiten mit `client:load`/`client:only="react"`
- **Translation Context System**: Custom React Context für Übersetzungen und Sprachwechsel
- **Client-side Language Detection**: Nutzung von localStorage und Cookies für Spracherkennung
- **Dynamic Content Updates**: `data-translate` Attribute + Client-side Scripts für statische Inhalte

## Implementierungsplan

### Phase 1: Vorbereitung der Translation Infrastructure

#### ✅ Bereits implementiert (aus vorheriger Arbeit):
- [x] `src/contexts/TranslationContext.tsx` - Custom Translation Context
- [x] `src/layouts/Layout.astro` - Client-side Spracherkennungs-Script
- [x] `src/components/models/CollapsibleHeader.tsx` - Übersetzungsschlüssel-Support
- [x] Translation files in `public/locales/` und `static/locales/`

#### ⏳ Zu validieren:
- [ ] Prüfung: Sind alle `recommendations.*` Übersetzungsschlüssel in allen Sprachdateien vorhanden?
- [ ] Prüfung: Funktioniert die `CollapsibleHeader` mit dynamischen Übersetzungsschlüsseln?

### Phase 2: Komponenten-Anpassungen

#### 2.1 CollapsibleHeader Integration
- [x] ✅ **Bereits implementiert**: `CollapsibleHeader` unterstützt `titleKey` und `descriptionKey` Props
- [ ] **TODO**: Recommendations-Seite auf Schlüssel-basierte Props umstellen

**Aktuelle Implementierung:**
```astro
<CollapsibleHeader
  title={translations.recommendations?.title || "Fallback"}
  description={translations.recommendations?.description || "Fallback"}
  client:only="react"
>
```

**Ziel-Implementierung:**
```astro
<CollapsibleHeaderIsland
  titleKey="recommendations.title"  
  descriptionKey="recommendations.description"
  totalModels={totalModels}
  totalProviders={totalProviders}
  client:load
>
```

#### 2.2 RecommendationsPage Wrapper Component
- [ ] **TODO**: `RecommendationsPageWrapper.tsx` erstellen
- [ ] **TODO**: `TranslationProvider` Context bereitstellen
- [ ] **TODO**: Bestehende `RecommendationsPage` für Übersetzungen anpassen

**Benötigte Dateien:**
```typescript
// src/components/recommendations/RecommendationsPageWrapper.tsx
import { TranslationProvider } from '@/contexts/TranslationContext';
import { RecommendationsPage } from './RecommendationsPage';

export function RecommendationsPageWrapper({ modelCards, ...props }) {
  return (
    <TranslationProvider>
      <RecommendationsPage modelCards={modelCards} {...props} />
    </TranslationProvider>
  );
}
```

### Phase 3: Statische Inhalte mit data-translate

#### 3.1 Statistik-Karten
Aktuelle hartcodierte Inhalte in statische + dynamische Inhalte aufteilen:

```astro
<!-- VORHER -->
<h3 class="text-sm font-medium text-muted-foreground">
  {translations.recommendations?.availableModels || "Verfügbare Modelle"}
</h3>

<!-- NACHHER -->
<h3 class="text-sm font-medium text-muted-foreground" data-translate="recommendations.availableModels">
  Verfügbare Modelle
</h3>
```

**Zu behandelnde Elemente:**
- [ ] `recommendations.availableModels`
- [ ] `recommendations.providers` 
- [ ] `recommendations.gaStatus`
- [ ] `recommendations.avgInputCost`
- [ ] `recommendations.avgOutputCost`
- [ ] `recommendations.perMillion`

### Phase 4: Translation Keys Vervollständigung

#### 4.1 Benötigte Übersetzungsschlüssel
```json
{
  "recommendations": {
    "title": "...",
    "description": "...",
    "availableModels": "...",
    "providers": "...", 
    "gaStatus": "...",
    "avgInputCost": "...",
    "avgOutputCost": "...",
    "perMillion": "..."
  }
}
```

#### 4.2 Sprachdateien aktualisieren
- [ ] `public/locales/de/i18n.json` - Deutsche Übersetzungen
- [ ] `public/locales/en/i18n.json` - Englische Übersetzungen  
- [ ] `public/locales/pl/i18n.json` - Polnische Übersetzungen
- [ ] `static/locales/de/i18n.json` - Deutsche Übersetzungen (Backup)
- [ ] `static/locales/en/i18n.json` - Englische Übersetzungen (Backup)
- [ ] `static/locales/pl/i18n.json` - Polnische Übersetzungen (Backup)

### Phase 5: Astro-Seite Umstrukturierung

#### 5.1 Server-side Translation Removal
```astro
---
// ENTFERNEN: Hardcodierte Sprach-Logik
const currentLang = "de";
const translationPath = path.join(process.cwd(), "public", "locales", currentLang, "i18n.json");
let translations: any = {};
// ... Translation loading logic
---

// ERSETZEN DURCH: Minimale Server-side Translations für SEO
---
import * as fsSync from "node:fs";
import * as path from "node:path";

// Load German translations for SEO/initial render only
const fallbackTranslations = JSON.parse(
  fsSync.readFileSync(
    path.join(process.cwd(), "public", "locales", "de", "i18n.json"), 
    "utf-8"
  )
);
---
```

#### 5.2 Layout Integration
```astro
<Layout 
  title={fallbackTranslations.recommendations?.title || "Empfehlungen"}
  description={fallbackTranslations.recommendations?.description?.replace("{{totalModels}}", totalModels).replace("{{totalProviders}}", totalProviders)}
>
```

#### 5.3 Template Anpassungen
```astro
<!-- Header mit data-translate -->
<CollapsibleHeaderIsland
  titleKey="recommendations.title"
  descriptionKey="recommendations.description" 
  totalModels={totalModels}
  totalProviders={totalProviders}
  client:load
>

<!-- Statistiken mit data-translate -->
<div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
  <div class="bg-card rounded-lg shadow-md p-6 border">
    <h3 class="text-sm font-medium text-muted-foreground" data-translate="recommendations.availableModels">
      Verfügbare Modelle
    </h3>
    <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">
      {totalModels}
    </p>
  </div>
  <!-- Weitere Statistik-Karten... -->
</div>

<!-- React Component mit Translation Context -->
<RecommendationsPageWrapper modelCards={modelCards} client:load />
```

### Phase 6: Testing & Validation

#### 6.1 Funktionale Tests
- [ ] Sprachschaltfläche DE → EN funktioniert
- [ ] Sprachschaltfläche EN → PL funktioniert  
- [ ] Sprachschaltfläche zurück zu DE funktioniert
- [ ] Browser-Refresh behält gewählte Sprache bei
- [ ] Navigation zwischen Seiten behält Sprache bei

#### 6.2 Content Tests
- [ ] Header-Titel ändert sich korrekt
- [ ] Header-Beschreibung ändert sich korrekt
- [ ] Alle Statistik-Karten-Labels ändern sich
- [ ] React-Komponenten zeigen korrekte Übersetzungen
- [ ] Keine Übersetzungsschlüssel sichtbar (z.B. "recommendations.title")

#### 6.3 Performance Tests  
- [ ] Keine Infinite-Reload-Loops
- [ ] Schnelle Sprachumschaltung ohne Flackern
- [ ] Korrekte Darstellung bei langsamerer Netzwerkverbindung

## Implementierungsreihenfolge

### Schritt 1: Translation Keys hinzufügen
1. Alle benötigten `recommendations.*` Schlüssel in Sprachdateien ergänzen
2. Bestehende Übersetzungen aus dem Code extrahieren

### Schritt 2: Komponenten anpassen  
1. `RecommendationsPageWrapper` erstellen
2. `RecommendationsPage` für Übersetzungskontext anpassen
3. `CollapsibleHeaderIsland` Integration

### Schritt 3: Astro-Seite umbauen
1. Server-side Translation Logic entfernen
2. `data-translate` Attribute hinzufügen
3. Wrapper-Komponenten einsetzen

### Schritt 4: Testing & Debugging
1. Funktionalität testen
2. Console-Debugging aktivieren
3. Edge Cases behandeln

## Mögliche Herausforderungen

### Problem 1: Dynamische Werte in Übersetzungen
**Challenge**: `{{totalModels}}` und `{{totalProviders}}` Platzhalter  
**Lösung**: Custom Props für `CollapsibleHeaderIsland` mit Ersetzungslogik

### Problem 2: React Island Translation Context
**Challenge**: React-Komponenten haben keinen Zugriff auf Astro-Übersetzungen  
**Lösung**: Wrapper-Komponenten mit `TranslationProvider`

### Problem 3: SEO & Initial Render
**Challenge**: Statische Builds benötigen initiale Inhalte für SEO  
**Lösung**: Deutsche Fallback-Übersetzungen für Server-side Rendering beibehalten

## Erfolgskriterien

### ✅ Vollständige Implementierung erreicht wenn:
- [ ] Alle statischen Texte wechseln bei Sprachauswahl
- [ ] Alle React-Komponenten zeigen korrekte Übersetzungen
- [ ] Sprachpräferenz persistiert zwischen Sessions
- [ ] Keine Übersetzungsschlüssel im UI sichtbar
- [ ] Keine Console-Errors oder Infinite-Loops
- [ ] Performance ist vergleichbar mit anderen Seiten

## Referenzen

### Bereits implementierte Lösungen:
- **Models Page**: `src/pages/models/index.astro` - CollapsibleHeader mit titleKey/descriptionKey
- **Blog Page**: `src/pages/blog/index.astro` - Vollständige I18N mit React Islands
- **Translation Context**: `src/contexts/TranslationContext.tsx` - React Context für Übersetzungen
- **Layout Script**: `src/layouts/Layout.astro` - Client-side Language Detection

### Code-Patterns:
```typescript
// Translation Hook Pattern
const { getTranslation } = useTranslation();
const text = getTranslation('recommendations.title');

// Data-Translate Pattern  
<h1 data-translate="recommendations.title">Fallback Text</h1>

// Wrapper Component Pattern
<TranslationProvider>
  <ComponentWithTranslations />
</TranslationProvider>