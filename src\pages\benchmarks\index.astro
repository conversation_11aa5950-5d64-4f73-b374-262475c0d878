---
import Layout from "../../layouts/Layout.astro";
import QualityCheckPage from "../../components/benchmarks/benchmarkTable";
import CollapsibleHeaderIsland from "../../components/models/CollapsibleHeaderIsland";
import type { ModelCardsData } from "../../types/model-cards";
import * as fs from "node:fs/promises";
import * as path from "node:path";

// Statische Daten zur Build-Zeit laden (GitLab Pages kompatibel)
const projectRoot = path.resolve(process.cwd());
const dataPath = path.join(projectRoot, "src", "data");

const modelCardsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "model-cards.json"), "utf-8")
) as ModelCardsData;

// Extrahiere nur die Model Cards
const modelCards = modelCardsData.modelCards;

// Berechne Statistiken für Benchmarks
const allBenchmarks = new Set<string>();
const benchmarkCounts = new Map<string, number>();

modelCards.forEach((card) => {
  if (card.benchmarks) {
    card.benchmarks.forEach((benchmark) => {
      allBenchmarks.add(benchmark.benchmarkName);
      benchmarkCounts.set(
        benchmark.benchmarkName,
        (benchmarkCounts.get(benchmark.benchmarkName) || 0) + 1
      );
    });
  }
});

const totalBenchmarks = allBenchmarks.size;
const avgBenchmarksPerModel =
  modelCards.reduce((sum, card) => sum + (card.benchmarks?.length || 0), 0) /
  modelCards.length;

// Finde die am häufigsten verwendeten Benchmarks
const topBenchmarks = Array.from(benchmarkCounts.entries())
  .sort((a, b) => b[1] - a[1])
  .slice(0, 5);

// Client-side i18n only - no server-side translation loading
---

<Layout title="Benchmark Comparison">
  <main class="container mx-auto px-4 py-8">
    <!-- Kollabierbare Header-Komponente -->
    <CollapsibleHeaderIsland
      titleKey="qc.header"
      descriptionKey="qc.description"
      totalModels={modelCards.length}
      totalBenchmarks={totalBenchmarks}
      client:load
    >
      <!-- Benchmark-Statistiken -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-card rounded-lg border p-6">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="qc.availableBenchmarks">
            Verfügbare Benchmarks
          </h3>
          <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">
            {totalBenchmarks}
          </p>
        </div>
        <div class="bg-card rounded-lg border p-6">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="qc.avgBenchmarksPerModel">
            ⌀ Benchmarks/Modell
          </h3>
          <p class="text-3xl font-bold text-green-600 dark:text-green-400">
            {avgBenchmarksPerModel.toFixed(1)}
          </p>
        </div>
        <div class="bg-card rounded-lg border p-6">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="qc.mostCommonBenchmark">
            Häufigster Benchmark
          </h3>
          <p class="text-lg font-bold text-purple-600 dark:text-purple-400">
            {topBenchmarks[0]?.[0] || "N/A"}
          </p>
          <p class="text-xs text-muted-foreground">
            {topBenchmarks[0]?.[1] || 0}
            <span data-translate="qc.models">Modelle</span>
          </p>
        </div>
        <div class="bg-card rounded-lg border p-6">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="qc.modelsWithBenchmarks">
            Modelle mit Benchmarks
          </h3>
          <p class="text-3xl font-bold text-orange-600 dark:text-orange-400">
            {
              modelCards.filter(
                (card) => card.benchmarks && card.benchmarks.length > 0
              ).length
            }
          </p>
        </div>
      </div>

      <!-- Top Benchmarks Liste -->
      <div class="bg-card rounded-lg border p-6 mb-6">
        <h3 class="text-lg font-semibold text-foreground mb-4" data-translate="qc.topBenchmarks">
          Top 5 Benchmarks (nach Verfügbarkeit)
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          {
            topBenchmarks.map(([name, count]) => (
              <div class="text-center p-3 bg-muted rounded-lg">
                <p class="font-medium text-sm text-foreground">{name}</p>
                <p class="text-xs text-muted-foreground">
                  {count} <span data-translate="qc.models">Modelle</span>
                </p>
              </div>
            ))
          }
        </div>
      </div>
    </CollapsibleHeaderIsland>

    <!-- React Island für die Quality-Check-Seite -->
    <QualityCheckPage modelCards={modelCards} client:only="react" />
  </main>
</Layout>

<style>
  .container {
    max-width: 1400px;
  }
</style>
