"use client";

import type { ReactNode } from "react";
import React, { createContext, useContext, useState, useEffect } from "react";
import { fetchCombinedModelData } from "@/services/api";
import { fetchBenchmarkData, enrichModelDataWithBenchmark } from "@/services/benchmarkService";
import type { ModelData, BenchmarkData } from "@/types/api";
import { toast } from "sonner";

interface ModelDataContextType {
  models: ModelData[];
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
  filteredModels: ModelData[];
  setFilteredModels: React.Dispatch<React.SetStateAction<ModelData[]>>;
  selectedModelIds: string[];
  setSelectedModelIds: React.Dispatch<React.SetStateAction<string[]>>;
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  selectedModelGroup: string;
  setSelectedModelGroup: React.Dispatch<React.SetStateAction<string>>;
  selectedMode: string;
  setSelectedMode: React.Dispatch<React.SetStateAction<string>>;
  modeOptions: string[];
  selectedConfidentiality: string;
  setSelectedConfidentiality: React.Dispatch<React.SetStateAction<string>>;
  confidentialityOptions: string[];
  benchmarkData: BenchmarkData[];
  filteredBenchmarkData: BenchmarkData[];
  setFilteredBenchmarkData: React.Dispatch<React.SetStateAction<BenchmarkData[]>>;
}

const ModelDataContext = createContext<ModelDataContextType | undefined>(undefined);

export function ModelDataProvider({ children }: { children: ReactNode }) {
  const [models, setModels] = useState<ModelData[]>([]);
  const [filteredModels, setFilteredModels] = useState<ModelData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedModelIds, setSelectedModelIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedModelGroup, setSelectedModelGroup] = useState<string>("");
  const [selectedMode, setSelectedMode] = useState<string>("all");
  const [modeOptions, setModeOptions] = useState<string[]>([]);
  const [selectedConfidentiality, setSelectedConfidentiality] = useState<string>("all");
  const [confidentialityOptions, setConfidentialityOptions] = useState<string[]>([]);
  const [benchmarkData, setBenchmarkData] = useState<BenchmarkData[]>([]);
  const [filteredBenchmarkData, setFilteredBenchmarkData] = useState<BenchmarkData[]>([]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // Fetch model data and benchmark data in parallel
      const [modelResponse, benchmarkDataResult] = await Promise.all([
        fetchCombinedModelData(),
        fetchBenchmarkData()
      ]);
      
      // Store benchmark data
      setBenchmarkData(benchmarkDataResult);
      setFilteredBenchmarkData(benchmarkDataResult);
      
      if (modelResponse.status === 200) {
        if (modelResponse.data.length === 0) {
          setError("Keine Modelldaten verfügbar");
          toast.warning("Keine Modelldaten verfügbar", {
            description: "Es konnten keine Modelldaten geladen werden. Bitte versuchen Sie es später erneut."
          });
        } else {
          // Enrich model data with benchmark data
          const enrichedData = enrichModelDataWithBenchmark(modelResponse.data, benchmarkDataResult);
          
          setModels(enrichedData);
          setFilteredModels(enrichedData);
          setError(null);
          
          // Extract unique confidentiality values and mode values
          const confidentialityValues = new Set<string>();
          const modeValues = new Set<string>();
          
          enrichedData.forEach(model => {
            if (model.confidentiality) {
              confidentialityValues.add(model.confidentiality);
            }
            if (model.mode) {
              modeValues.add(model.mode);
            }
          });
          
          setConfidentialityOptions(Array.from(confidentialityValues).sort());
          setModeOptions(Array.from(modeValues).sort());
          
          // Show toast message if using cached data
          if (modelResponse.message.includes("cached")) {
            toast.info("Verwende zwischengespeicherte Daten", {
              description: "Die Live-Daten konnten nicht abgerufen werden. Es werden zwischengespeicherte Daten angezeigt."
            });
          } else if (modelResponse.message.includes("fallback")) {
            toast.warning("Verwende Fallback-Daten", {
              description: "Die Live-Daten konnten nicht abgerufen werden. Es werden Fallback-Daten angezeigt."
            });
          }
        }
      } else {
        setError(modelResponse.message);
        toast.error("Fehler beim Laden der Modelldaten", {
          description: modelResponse.message
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unbekannter Fehler aufgetreten";
      setError(errorMessage);
      toast.error("Fehler beim Laden der Daten", {
        description: errorMessage
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const refreshData = async () => {
    await fetchData();
  };

  const value = {
    models,
    loading,
    error,
    refreshData,
    filteredModels,
    setFilteredModels,
    selectedModelIds,
    setSelectedModelIds,
    searchTerm,
    setSearchTerm,
    selectedModelGroup,
    setSelectedModelGroup,
    selectedMode,
    setSelectedMode,
    modeOptions,
    selectedConfidentiality,
    setSelectedConfidentiality,
    confidentialityOptions,
    benchmarkData,
    filteredBenchmarkData,
    setFilteredBenchmarkData
  };

  return (
    <ModelDataContext.Provider value={value}>
      {children}
    </ModelDataContext.Provider>
  );
}

export function useModelData() {
  const context = useContext(ModelDataContext);
  if (context === undefined) {
    throw new Error("useModelData must be used within a ModelDataProvider");
  }
  return context;
}