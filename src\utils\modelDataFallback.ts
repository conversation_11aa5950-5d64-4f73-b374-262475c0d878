import type { ModelCost } from "@/types/api";

// Local storage key for cached model data
const MODEL_DATA_CACHE_KEY = "iteragpt-model-data-cache";
const CACHE_EXPIRY_TIME = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Save model data to local storage as a fallback
 */
export function saveModelDataToLocalStorage(data: ModelCost[]): void {
  try {
    const cacheData = {
      timestamp: Date.now(),
      data
    };
    localStorage.setItem(MODEL_DATA_CACHE_KEY, JSON.stringify(cacheData));
    console.log("Model data saved to local storage");
  } catch (error) {
    console.error("Failed to save model data to local storage:", error);
  }
}

/**
 * Get model data from local storage if available and not expired
 */
export function getModelDataFromLocalStorage(): ModelCost[] | null {
  try {
    const cachedDataString = localStorage.getItem(MODEL_DATA_CACHE_KEY);
    if (!cachedDataString) return null;

    const cachedData = JSON.parse(cachedDataString);
    const now = Date.now();
    
    // Check if cache is expired
    if (now - cachedData.timestamp > CACHE_EXPIRY_TIME) {
      console.log("Cached model data is expired");
      localStorage.removeItem(MODEL_DATA_CACHE_KEY);
      return null;
    }
    
    console.log("Using cached model data from local storage");
    return cachedData.data;
  } catch (error) {
    console.error("Failed to get model data from local storage:", error);
    return null;
  }
}

/**
 * Check if we're in a browser environment (to avoid localStorage errors in SSR)
 */
export function isBrowser(): boolean {
  return typeof window !== "undefined";
}