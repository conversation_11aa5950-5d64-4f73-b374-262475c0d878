import type { ModelCard, ModelCardsData } from '@/types/model-cards';

interface ModelMappings {
  [key: string]: {
    modelCardId?: string;
    displayName?: string;
    [key: string]: unknown;
  };
}

let modelCardsCache: ModelCardsData | null = null;
let modelMappingsCache: ModelMappings | null = null;

// Reset cache for development/testing
export function resetModelCardsCache() {
  modelCardsCache = null;
  modelMappingsCache = null;
}

// Load model mappings from model-mappings.json
async function getModelMappings(): Promise<ModelMappings> {
  if (modelMappingsCache) {
    return modelMappingsCache;
  }

  try {
    const cacheBuster = Date.now();
    const response = await fetch(`/data/model-mappings.json?v=${cacheBuster}`);
    if (!response.ok) {
      console.warn('Could not load model-mappings.json, using fallback mappings');
      return { mappings: {} };
    }
    
    modelMappingsCache = await response.json();
    return modelMappingsCache || { mappings: {} };
  } catch (error) {
    console.warn('Error loading model mappings:', error);
    return { mappings: {} };
  }
}

// Debug function to help with matching
export async function debugMatchModelIdToCard(modelId: string, modelCards: ModelCard[]): Promise<{
  match: ModelCard | undefined,
  debug: Record<string, unknown>
}> {
  const cleanModelId = modelId.replace(/^(azure|aws|gcp|anthropic|openai|vertex_ai|bedrock)\//, '');
  const normalizedId = cleanModelId.toLowerCase().replace(/[^a-z0-9]/g, '');
  
  const debug = {
    originalId: modelId,
    cleanId: cleanModelId,
    normalizedId: normalizedId,
    availableCards: modelCards.map(card => ({
      id: card.basicInfo.modelId,
      displayName: card.basicInfo.displayName,
      normalizedId: card.basicInfo.modelId.toLowerCase().replace(/[^a-z0-9]/g, '')
    })),
    mappingSteps: [] as string[]
  };
  
  // Load mappings for debugging
  const modelMappings = await getModelMappings();
  
  // Step-by-step matching debug
  debug.mappingSteps.push(`1. Trying exact match for: "${modelId}"`);
  let card = getModelCardById(modelCards, modelId);
  if (card) {
    debug.mappingSteps.push(`✓ Found exact match!`);
    return { match: card, debug };
  }
  
  debug.mappingSteps.push(`2. Trying clean ID match for: "${cleanModelId}"`);
  card = getModelCardById(modelCards, cleanModelId);
  if (card) {
    debug.mappingSteps.push(`✓ Found clean ID match!`);
    return { match: card, debug };
  }
  
  debug.mappingSteps.push(`3. Checking loaded mappings...`);
  const match = matchModelIdToCard(modelId, modelCards, modelMappings);
  
  if (match) {
    debug.mappingSteps.push(`✓ Found pattern match: "${match.basicInfo.modelId}"`);
  } else {
    debug.mappingSteps.push(`✗ No match found`);
  }
  
  return { match, debug };
}

export async function getModelCards(): Promise<ModelCardsData> {
  if (modelCardsCache) {
    return modelCardsCache;
  }

  try {
    const allModelCards: ModelCard[] = [];

    // Cache buster for development
    const cacheBuster = Date.now();

    // Load model mappings
    const modelMappings = await getModelMappings();

    // Load separate model cards from model-cards.json
    try {
      const modelCardsResponse = await fetch(`/data/model-cards.json?v=${cacheBuster}`);
      if (modelCardsResponse.ok) {
        const modelCardsData: ModelCardsData = await modelCardsResponse.json();
        allModelCards.push(...modelCardsData.modelCards);
      }
    } catch (error) {
      console.warn('Could not load model-cards.json:', error);
    }

    // Load individual model card files from mappings
    const uniqueModelCardFiles = new Set<string>();
    Object.values(modelMappings.mappings || {}).forEach((mapping: unknown) => {
      const typedMapping = mapping as { modelCardFile?: string; [key: string]: unknown };
      if (typedMapping.modelCardFile) {
        uniqueModelCardFiles.add(typedMapping.modelCardFile);
      }
    });

    for (const filename of Array.from(uniqueModelCardFiles)) {
      try {
        const response = await fetch(`/data/models/${filename}?v=${cacheBuster}`);
        if (response.ok) {
          const modelCard: ModelCard = await response.json();
          allModelCards.push(modelCard);
        }
      } catch (error) {
        console.warn(`Could not load model card file ${filename}:`, error);
      }
    }

    // Load embedded model cards from models.json
    const modelsResponse = await fetch(`/data/models.json?v=${cacheBuster}`);
    if (!modelsResponse.ok) {
      throw new Error(`Failed to fetch models: ${modelsResponse.status}`);
    }
    const modelsData = await modelsResponse.json();

    // Extract embedded model cards (those with _isModelCard: true)
    const embeddedModelCards: ModelCard[] = modelsData.models
      .filter((model: { _isModelCard?: boolean; [key: string]: unknown }) => model._isModelCard === true)
      .map((model: Record<string, unknown>) => {
        // Convert embedded model card to ModelCard format
        return {
          basicInfo: {
            modelId: model.id || model.key,
            displayName: model.displayName || model.name,
            provider: model.provider,
            modelFamily: model.modelGroup,
            version: model.version || "Unknown",
            description: model.description || "",
            releaseDate: model.releaseDate || "Unknown",
            status: model.status || "Unknown",
            knowledgeCutoff: model.knowledgeCutoff || "Unknown"
          },
          technicalSpecs: {
            contextWindow: model.contextWindow || model.maxInputTokens,
            maxOutputTokens: model.maxOutputTokens || model.maxTokens,
            architecture: model.architecture || "Unknown",
            parameterCount: model.parameterCount || "Unknown",
            supportedInputTypes: model.supportedInputTypes || ["text"],
            supportedOutputTypes: model.supportedOutputTypes || ["text"],
            inputLimitations: model.inputLimitations || {}
          },
          capabilities: {
            functionCalling: model.supportsFunctionCalling || false,
            vision: model.supportsVision || false,
            pdfSupport: model.supportsPdfInput || false,
            audioInput: model.supportsAudioInput || false,
            audioOutput: model.supportsAudioOutput || false,
            imageGeneration: model.supportsImageGeneration || false,
            codeExecution: model.supportsCodeExecution || false,
            systemInstructions: model.supportsSystemMessages || false,
            promptCaching: model.supportsPromptCaching || false,
            batchProcessing: model.supportsBatchProcessing || false,
            reasoning: model.supportsReasoning || false,
            thinking: model.supportsThinking || false,
            grounding: model.supportsGrounding || false,
            multilingualSupport: model.supportsMultilingualSupport || false,
            embeddingImageInput: model.supportsEmbeddingImageInput || false,
            structuredOutputs: model.supportsStructuredOutputs || false,
            webBrowsing: model.supportsWebSearch || false,
            codeInterpreter: model.supportsCodeInterpreter || false,
            dalleIntegration: model.supportsDalleIntegration || false,
            realTimeAPI: model.supportsRealTimeAPI || false
          },
          performance: {
            latency: model.latency || "Unknown",
            rateLimits: model.rateLimits || {},
            temperature: model.temperature || { min: 0, max: 1, default: 1 },
            topP: model.topP,
            topK: model.topK
          },
          pricing: {
            inputCostPer1MTokens: model.inputCostPer1kTokens ? (model.inputCostPer1kTokens as number) * 1000 : (model.inputCostPerToken as number) * 1000000,
            outputCostPer1MTokens: model.outputCostPer1kTokens ? (model.outputCostPer1kTokens as number) * 1000 : (model.outputCostPerToken as number) * 1000000,
            cachingCosts: model.cachingCosts || {},
            currency: model.currency || "USD"
          },
          availability: {
            regions: model.regions || [],
            dataProcessingRegions: model.dataProcessingRegions || [],
            supportedPlatforms: model.supportedPlatforms || [],
            platformSpecificIds: model.platformSpecificIds || {}
          },
          security: model.security || {},
          usageTypes: model.usageTypes || {},
          benchmarks: model.benchmarks || [],
          metadata: {
            lastUpdated: model._modelCardLastUpdated || new Date().toISOString(),
            dataSource: model._modelCardSource || "Embedded in models.json",
            version: model._modelCardVersion || "1.0"
          }
        };
      });

    // Combine all model cards (avoid duplicates by modelId)
    allModelCards.push(...embeddedModelCards);
    
    // Remove duplicates by modelId (prefer individual files over embedded ones)
    const uniqueModelCards = new Map<string, ModelCard>();
    allModelCards.forEach(card => {
      const existingCard = uniqueModelCards.get(card.basicInfo.modelId);
      if (!existingCard || card.metadata.dataSource !== "Embedded in models.json") {
        uniqueModelCards.set(card.basicInfo.modelId, card);
      }
    });
    
    const combinedData: ModelCardsData = {
      modelCards: Array.from(uniqueModelCards.values()),
      lastUpdated: new Date().toISOString(),
      source: "combined"
    };

    modelCardsCache = combinedData;
    return combinedData;
  } catch (error) {
    console.error('Error loading model cards:', error);
    throw error;
  }
}

export function getModelCardById(modelCards: ModelCard[], modelId: string): ModelCard | undefined {
  return modelCards.find(card => card.basicInfo.modelId === modelId);
}

export function matchModelIdToCard(modelId: string, modelCards: ModelCard[], loadedMappings?: ModelMappings): ModelCard | undefined {
  console.log(`[matchModelIdToCard] Starting match for: ${modelId}`);
  console.log(`[matchModelIdToCard] Available model cards:`, modelCards.map(c => c.basicInfo.modelId));
  console.log(`[matchModelIdToCard] Loaded mappings:`, loadedMappings?.mappings);

  // Exact match first
  let card = getModelCardById(modelCards, modelId);
  if (card) {
    console.log(`[matchModelIdToCard] ✓ Exact match found: ${card.basicInfo.modelId}`);
    return card;
  }

  // Remove provider prefix from model ID for better matching
  const cleanModelId = modelId.replace(/^(azure|aws|gcp|anthropic|openai|vertex_ai|bedrock)\//, '');
  console.log(`[matchModelIdToCard] Clean model ID: ${cleanModelId}`);
  
  // Try exact match with clean ID
  card = getModelCardById(modelCards, cleanModelId);
  if (card) {
    console.log(`[matchModelIdToCard] ✓ Clean ID match found: ${card.basicInfo.modelId}`);
    return card;
  }

  // Use loaded mappings if available
  if (loadedMappings?.mappings) {
    const mappings = loadedMappings.mappings;
    console.log(`[matchModelIdToCard] Checking mappings for: ${modelId} and ${cleanModelId}`);
    
    if (mappings[modelId] || mappings[cleanModelId]) {
      const mapping = mappings[modelId] || mappings[cleanModelId];
      const mappedId = (mapping as { modelCardId?: string }).modelCardId;
      console.log(`[matchModelIdToCard] Found mapping: ${modelId} -> ${mappedId}`);
      
      if (mappedId) {
        card = getModelCardById(modelCards, mappedId);
        if (card) {
          console.log(`[matchModelIdToCard] ✓ Mapping match found: ${card.basicInfo.modelId}`);
          console.log(`[matchModelIdToCard] Benchmark count: ${card.benchmarks?.length || 0}`);
          return card;
        } else {
          console.log(`[matchModelIdToCard] ✗ Mapped ID not found in model cards: ${mappedId}`);
        }
      }
    } else {
      console.log(`[matchModelIdToCard] No mapping found for ${modelId} or ${cleanModelId}`);
    }
  } else {
    console.log(`[matchModelIdToCard] No mappings available`);
  }

  // Fallback to manual mappings for backward compatibility
  const manualMappings: { [key: string]: string } = {
    'claude-3.7-sonnet': 'claude-3-7-sonnet@20250219',
    'gpt-4.1': 'gpt-4.1',
    'aws/claude-3.7-sonnet': 'claude-3-7-sonnet@20250219',
    'azure/gpt-4.1': 'gpt-4.1'
  };

  console.log(`[matchModelIdToCard] Checking manual mappings...`);
  // Check manual mappings
  if (manualMappings[modelId] || manualMappings[cleanModelId]) {
    const mappedId = manualMappings[modelId] || manualMappings[cleanModelId];
    console.log(`[matchModelIdToCard] Manual mapping found: ${modelId} -> ${mappedId}`);
    card = getModelCardById(modelCards, mappedId);
    if (card) {
      console.log(`[matchModelIdToCard] ✓ Manual mapping match found: ${card.basicInfo.modelId}`);
      return card;
    }
  }

  console.log(`[matchModelIdToCard] Trying pattern matching...`);

  // Try various pattern matching for common model ID variations
  const normalizedId = cleanModelId.toLowerCase().replace(/[^a-z0-9]/g, '');
  
  for (const modelCard of modelCards) {
    const cardId = modelCard.basicInfo.modelId.toLowerCase().replace(/[^a-z0-9]/g, '');
    
    // Check if IDs match when normalized
    if (normalizedId === cardId) {
      return modelCard;
    }
    
    // Check if model ID is contained in card ID or vice versa
    if (normalizedId.includes(cardId) || cardId.includes(normalizedId)) {
      return modelCard;
    }
    
    // Check display name
    const displayName = modelCard.basicInfo.displayName.toLowerCase().replace(/[^a-z0-9]/g, '');
    if (normalizedId === displayName || normalizedId.includes(displayName) || displayName.includes(normalizedId)) {
      return modelCard;
    }
    
    // Special matching for common patterns
    // GPT models: match "gpt-4.1" with "gpt41"
    if (normalizedId.includes('gpt') && cardId.includes('gpt')) {
      const gptVersionMatch = normalizedId.match(/gpt(\d+\.?\d*)/);
      const cardGptVersionMatch = cardId.match(/gpt(\d+\.?\d*)/);
      if (gptVersionMatch && cardGptVersionMatch) {
        const normalizedVersion1 = gptVersionMatch[1].replace('.', '');
        const normalizedVersion2 = cardGptVersionMatch[1].replace('.', '');
        if (normalizedVersion1 === normalizedVersion2) {
          return modelCard;
        }
      }
    }
    
    // Claude models: special handling for version differences
    if (normalizedId.includes('claude') && cardId.includes('claude')) {
      // Remove all separators and suffixes for comparison
      const cleanModelId = normalizedId.replace(/[-.]/g, '').replace(/sonnet$/, 'sonnet');
      const cleanCardId = cardId.replace(/[-.@]/g, '').replace(/\d{8}$/, '').replace(/sonnet$/, 'sonnet');
      
      // Check if both contain sonnet and extract version numbers
      if (cleanModelId.includes('sonnet') && cleanCardId.includes('sonnet')) {
        const modelVersion = cleanModelId.match(/claude(\d+)/);
        const cardVersion = cleanCardId.match(/claude(\d+)/);
        
        if (modelVersion && cardVersion) {
          // For claude-3.7-sonnet vs claude-3-7-sonnet
          const modelVersionFull = normalizedId.match(/claude[-]?(\d+[.-]?\d*)/);
          const cardVersionFull = cardId.match(/claude[-]?(\d+[.-]?\d*)/);
          
          if (modelVersionFull && cardVersionFull) {
            const normalizedModelVersion = modelVersionFull[1].replace(/[.-]/g, '');
            const normalizedCardVersion = cardVersionFull[1].replace(/[.-]/g, '');
            if (normalizedModelVersion === normalizedCardVersion) {
              return modelCard;
            }
          }
        }
      }
    }
  }
  
  console.log(`[matchModelIdToCard] ✗ No match found for ${modelId}`);
  return undefined;
}

// Helper function to get all benchmark categories
export function getBenchmarkCategories(modelCards: ModelCard[]): string[] {
  const categories = new Set<string>();
  
  modelCards.forEach(card => {
    card.benchmarks.forEach(benchmark => {
      categories.add(benchmark.category);
    });
  });
  
  return Array.from(categories).sort();
}

// Helper function to get all available benchmarks
export function getAllBenchmarkNames(modelCards: ModelCard[]): string[] {
  const benchmarkNames = new Set<string>();
  
  modelCards.forEach(card => {
    card.benchmarks.forEach(benchmark => {
      benchmarkNames.add(benchmark.benchmarkName);
    });
  });
  
  return Array.from(benchmarkNames).sort();
}

// Helper function to format capability name for display
export function formatCapabilityName(key: string): string {
  return key
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
}

// Helper function to get capability categories
export function getCapabilityCategories() {
  return {
    'Input/Output': ['vision', 'audioInput', 'audioOutput', 'pdfSupport', 'imageGeneration'],
    'Code & Development': ['codeExecution', 'codeInterpreter', 'functionCalling'],
    'Advanced Features': ['reasoning', 'thinking', 'grounding', 'webBrowsing', 'dalleIntegration'],
    'Infrastructure': ['systemInstructions', 'promptCaching', 'batchProcessing', 'structuredOutputs', 'realTimeAPI'],
    'Language': ['multilingualSupport', 'embeddingImageInput']
  };
}