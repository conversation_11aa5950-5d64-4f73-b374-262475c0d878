---
import Layout from "../../../layouts/Layout.astro";
import BenchmarkTableIsland from "../../../components/benchmarks/aiderPolyglotBenchmark/BenchmarkTableIsland";
import CollapsibleHeaderIsland from "../../../components/models/CollapsibleHeaderIsland";
import type { ModelData, BenchmarkData } from "../../../types/api";
import * as fs from "node:fs/promises";
import * as path from "node:path";

// Client-side i18n only - no server-side translation loading

// Statische Daten zur Build-Zeit laden (GitLab Pages kompatibel)
// Verwende absoluten Pfad basierend auf dem Projekt-Root
const projectRoot = path.resolve(process.cwd());
const dataPath = path.join(projectRoot, "src", "data");

const modelsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "models.json"), "utf-8")
);
const enrichedModelsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "enriched-models.json"), "utf-8")
);
const benchmarksData = JSON.parse(
  await fs.readFile(path.join(dataPath, "polyglot_benchmarks.json"), "utf-8")
);
const statisticsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "statistics.json"), "utf-8")
);

// Type assertions für die JSON-Daten - extrahiere models/benchmarks Array aus dem JSON
const models = (modelsData.models || modelsData) as ModelData[];
const enrichedModels = (enrichedModelsData.models ||
  enrichedModelsData) as ModelData[];
const benchmarks = (benchmarksData.benchmarks ||
  benchmarksData) as BenchmarkData[];
const statistics = {
  totalModels: statisticsData.models?.totalModels || 0,
  totalBenchmarks: statisticsData.benchmarks?.totalBenchmarks || 0,
  averagePassRate: statisticsData.benchmarks?.averagePassRate || 0,
  topPerformers:
    statisticsData.benchmarks?.topPerformers?.map((p: any) => p.model) || [],
};
---

<Layout title="Aider Polyglot Benchmark - LLM Browser">
  <main class="container mx-auto px-4 py-8">
    <!-- Kollabierbare Header-Komponente -->
    <CollapsibleHeaderIsland
      titleKey="benchmark.title"
      descriptionKey="benchmark.description"
      totalBenchmarks={statistics.totalBenchmarks}
      client:load
    >
      <!-- Benchmark-Statistik-Übersicht -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="benchmark.testedModels">
            Getestete Modelle
          </h3>
          <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">
            {benchmarks.length}
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="benchmark.averageScore">
            Durchschnittsscore
          </h3>
          <p class="text-3xl font-bold text-green-600 dark:text-green-400">
            {(statistics.averagePassRate || 0).toFixed(1)}%
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="benchmark.highestScore">
            Höchster Score
          </h3>
          <p class="text-3xl font-bold text-purple-600 dark:text-purple-400">
            {
              benchmarks.length > 0
                ? Math.max(...benchmarks.map((b) => b.pass_rate_2)).toFixed(1)
                : "0"
            }%
          </p>
        </div>
        <div class="bg-card rounded-lg shadow-md p-6 border">
          <h3 class="text-sm font-medium text-muted-foreground" data-translate="benchmark.testCases">
            Testfälle
          </h3>
          <p class="text-3xl font-bold text-orange-600 dark:text-orange-400">
            {
              benchmarks.length > 0
                ? benchmarks[0].details.test_cases || 225
                : 225
            }
          </p>
        </div>
      </div>

      <!-- Erklärungstext -->
      <div
        class="bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-400 dark:border-blue-600 p-4 mb-6"
      >
        <div class="flex">
          <div class="ml-3">
            <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">
              <strong data-translate="benchmark.about">Über den Polyglot Benchmark:</strong>
              <span data-translate="benchmark.aboutText1">
                Dieser Benchmark basiert auf Exercism-Coding-Übungen und testet die Fähigkeit von Sprachmodellen, komplexe Programmierprobleme in 6 verschiedenen Sprachen zu lösen:
              </span>
              <strong data-translate="benchmark.languages">C++, Go, Java, JavaScript, Python und Rust</strong>.
            </p>
            <p class="text-sm text-blue-700 dark:text-blue-300 mb-3" data-translate="benchmark.aboutText2" data-translate-hardest="225" data-translate-total="697">
              Der Benchmark umfasst die 225 schwierigsten Übungen aus insgesamt 697 verfügbaren Exercism-Problemen und wurde entwickelt, um deutlich herausfordernder zu sein als frühere Benchmarks. Die Scores basieren auf der Anzahl erfolgreich gelöster Coding-Probleme und bieten eine präzise Bewertung der Code-Editing-Fähigkeiten moderner LLMs.
            </p>
            <p class="text-sm text-blue-700 dark:text-blue-300">
              <strong>Datenquelle:</strong> Die Benchmark-Daten stammen von
              <a
                href="https://aider.chat/docs/leaderboards/"
                target="_blank"
                rel="noopener noreferrer"
                class="text-blue-800 dark:text-blue-200 hover:text-blue-900 dark:hover:text-blue-100 underline font-medium"
              >
                aider.chat/docs/leaderboards/
              </a>
              und werden automatisch 1x täglich aktualisiert, um stets aktuelle Ergebnisse
              zu gewährleisten.
            </p>
          </div>
        </div>
      </div>
    </CollapsibleHeaderIsland>

    <!-- React Island für die interaktive Benchmark Table -->
    <BenchmarkTableIsland
      benchmarks={benchmarks}
      models={enrichedModels}
      client:only="react"
    />
  </main>
</Layout>

<style>
  .container {
    max-width: 1400px;
  }
</style>
