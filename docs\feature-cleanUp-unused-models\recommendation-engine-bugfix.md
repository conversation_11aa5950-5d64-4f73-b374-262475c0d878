# Recommendation Engine Bugfix Report

## Problem
Use<PERSON> be<PERSON>, dass <PERSON> 3.7 Son<PERSON> fälschlicher<PERSON><PERSON> vor <PERSON> 4 und Claude Opus 4 gerankt wurde, obwohl die Benchmark-Daten klar zeigen, dass <PERSON> 4 Modelle überlegen sind.

## Benchmark-Vergleich

### Claude 3.7 Sonnet:
- SWE-bench Verified: 62.3%
- LiveCodeBench v2025: 63.8%
- Aider-Polyglot: 64.9%
- WebDev-Arena: 1357

### Claude <PERSON> 4:
- SWE-bench Verified: 72.7% ✅ **+10.4%**
- LiveCodeBench v2025: 70.9% ✅ **+7.1%**
- Aider-Polyglot: 61.3% ❌ **-3.6%**
- WebDev-Arena: 1389 ✅ **+32 Punkte**

### Claude <PERSON> 4:
- SWE-bench Verified: 72.5% ✅ **+10.2%**
- LiveCodeBench v2025: 77.8% ✅ **+14.0%**
- Aider-Polyglot: 72.0% ✅ **+7.1%**
- WebDev-Arena: 1412 ✅ **+55 Punkte**

## Identifizierte Bugs

### 1. CRITICAL_CODING_BENCHMARKS Array Fehler
**Problem**: `'Web'` statt `'WebDev-Arena'`
```typescript
// FEHLERHAFT:
const CRITICAL_CODING_BENCHMARKS = [
  'SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'Terminal-bench','Web'
];

// KORRIGIERT:
const CRITICAL_CODING_BENCHMARKS = [
  'SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'
];
```

**Auswirkung**: WebDev-Arena Benchmarks bekamen keine kritische Gewichtung (1.5x Multiplikator)

### 2. Arena Score Normalisierung fehlte
**Problem**: WebDev-Arena Scores (1300-1400) wurden nicht normalisiert
```typescript
// NEU HINZUGEFÜGT:
if (benchmark.metric === 'Arena Score') {
  // Arena Score Normalisierung: 1300 = 60, 1350 = 70, 1400 = 85, 1450+ = 95
  if (benchmark.score >= 1450) normalizedScore = 95;
  else if (benchmark.score >= 1400) normalizedScore = 85 + ((benchmark.score - 1400) / 50) * 10;
  else if (benchmark.score >= 1350) normalizedScore = 70 + ((benchmark.score - 1350) / 50) * 15;
  else if (benchmark.score >= 1300) normalizedScore = 60 + ((benchmark.score - 1300) / 50) * 10;
  else normalizedScore = Math.max(30, (benchmark.score / 1300) * 60);
}
```

**Auswirkung**: Arena Scores wurden als rohe Zahlen (1300+) behandelt, was extreme Übergewichtung verursachte

### 3. Falsche Bonus-Logik
**Problem**: Kommentare und Bonus-Vergabe basierte auf veralteten Daten
```typescript
// FEHLERHAFT:
// Gemini 2.5 Pro: Führend bei LiveCodeBench (70.4%) und Aider (74.0%)

// KORRIGIERT:
// Claude Opus 4: Zusätzlicher Bonus für LiveCodeBench (77.8%) und Aider-Polyglot (72.0%) Führung
if (modelName.includes('claude opus 4') &&
    ['code-generation', 'debugging', 'refactoring'].includes(useCase.id)) {
  codingLeaderBonus += 2; // Zusätzliche +2 Punkte für die beste Performance
}
```

## Erwartete Korrektur nach Bugfix

Nach diesen Korrekturen sollte die neue Ranking-Reihenfolge sein:
1. **Claude Opus 4** (beste Performance in 3/4 Benchmarks)
2. **Claude Sonnet 4** (gute Performance, bessere Kosten als Opus)
3. **Claude 3.7 Sonnet** (ältere Generation)

## Implementierte Korrekturen

✅ **src/services/model-recommendations.ts**:
- CRITICAL_CODING_BENCHMARKS Array korrigiert
- Arena Score Normalisierung hinzugefügt  
- Bonus-Logik für Claude Opus 4 korrigiert

## Test-Empfehlung
Nach dem Bugfix sollte die Code-Review Use Case Recommendations überprüft werden, um zu bestätigen, dass:
1. Claude Opus 4 als #1 gerankt wird
2. Claude Sonnet 4 vor Claude 3.7 Sonnet steht
3. WebDev-Arena Benchmarks korrekt gewichtet werden

Datum: 06.01.2025 17:35