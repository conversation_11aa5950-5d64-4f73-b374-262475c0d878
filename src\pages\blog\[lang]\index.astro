---
import Layout from "../../../layouts/Layout.astro";
import { getCollection } from "astro:content";
import { BlogOverviewIslandWrapper } from "../../../components/blog/BlogOverviewIslandWrapper";

// Export getStaticPaths for static generation
export async function getStaticPaths() {
  const supportedLanguages = ['de', 'en', 'pl'];
  
  return supportedLanguages.map((lang) => ({
    params: { lang },
    props: { lang }
  }));
}

const { lang } = Astro.params;
const { lang: propLang } = Astro.props;

// Get all blog entries for the specific language
const allBlogEntries = await getCollection("blog");
const blogEntries = allBlogEntries.filter(entry => entry.data.lang === lang);

// Transform entries to the expected format
const posts = blogEntries.map(entry => ({
  id: entry.slug,
  title: entry.data.title,
  slug: entry.slug,
  excerpt: entry.data.excerpt,
  content: entry.body,
  category: entry.data.category,
  tags: entry.data.tags,
  publishDate: entry.data.publishDate.toISOString(),
  lastUpdated: entry.data.lastUpdated?.toISOString() || entry.data.publishDate.toISOString(),
  author: {
    name: entry.data.author.name,
    role: entry.data.author.role,
  },
  readingTime: entry.data.readingTime || 5,
  featured: entry.data.featured,
  lang: entry.data.lang,
  translationKey: entry.data.translationKey,
  availableLanguages: entry.data.availableLanguages || [],
  relatedModelIds: entry.data.relatedModelIds || [],
  relatedBenchmarks: entry.data.relatedBenchmarks || [],
  releaseVersion: entry.data.releaseVersion,
  changelog: entry.data.changelog || [],
  metaDescription: entry.data.metaDescription,
  metaKeywords: entry.data.metaKeywords || [],
  featuredImage: entry.data.featuredImage,
  gallery: entry.data.gallery || [],
}));

// Calculate metadata from posts
const categories: { [key: string]: number } = {};
const tags: { [key: string]: number } = {};

posts.forEach(post => {
  categories[post.category] = (categories[post.category] || 0) + 1;
  post.tags.forEach(tag => {
    tags[tag] = (tags[tag] || 0) + 1;
  });
});

const metadata = {
  totalPosts: posts.length,
  categories,
  tags,
  latestPosts: posts.slice(0, 5),
  featuredPosts: posts.filter(post => post.featured).slice(0, 3)
};

const pageTitle = lang === 'de' ? 'LLM Blog' :
                  lang === 'en' ? 'LLM Blog' :
                  'LLM Blog';

const pageDescription = lang === 'de' ? 
  'Aktuelle Insights zu AI-Modellen, Release Notes und Benchmark-Analysen. Bleiben Sie auf dem Laufenden über die neuesten Entwicklungen in der LLM-Landschaft.' :
  lang === 'en' ? 
  'Latest insights on AI models, release notes, and benchmark analyses. Stay up to date with the latest developments in the LLM landscape.' :
  'Najnowsze informacje o modelach AI, notatki o wydaniach i analizy benchmarków. Bądź na bieżąco z najnowszymi wydarzeniami w świecie LLM.';
---

<Layout title={pageTitle} description={pageDescription}>
  <main class="container mx-auto px-4 py-8">
    <!-- Language Switcher -->
    <div class="flex items-center gap-2 mb-6">
      <span class="text-sm text-gray-600" data-translate="blog.available_languages">Verfügbare Sprachen:</span>
      <a
        href="/blog/de/"
        class={`px-3 py-1 rounded text-sm transition-colors ${
          lang === 'de'
            ? 'bg-blue-100 text-blue-800 font-medium'
            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        }`}
      >
        Deutsch
      </a>
      <a
        href="/blog/en/"
        class={`px-3 py-1 rounded text-sm transition-colors ${
          lang === 'en'
            ? 'bg-blue-100 text-blue-800 font-medium'
            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        }`}
      >
        English
      </a>
      <a
        href="/blog/pl/"
        class={`px-3 py-1 rounded text-sm transition-colors ${
          lang === 'pl'
            ? 'bg-blue-100 text-blue-800 font-medium'
            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        }`}
      >
        Polski
      </a>
    </div>

    <!-- Blog Overview Component -->
    <BlogOverviewIslandWrapper
      initialPosts={posts}
      initialMetadata={metadata}
      client:load
    />
  </main>
</Layout>