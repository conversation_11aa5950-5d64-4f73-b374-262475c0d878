// Types for API responses - Migrated from Next.js project

// Model info from /model/info endpoint
export interface ModelInfo {
  id: string;
  name: string; // Corresponds to model_name
  provider: string; // Corresponds to litellm_provider
  description?: string; // Optional, as not present in all mock data
  capabilities?: string[]; // Optional
  modelGroup?: string; // Optional
  isAvailable?: boolean; // Optional
  confidentiality?: string;
  mode?: string;
  status?: string;
  maxTokens?: number | null;
  maxInputTokens?: number | null;
  maxOutputTokens?: number | null;
  inputCostPerToken?: number | null;
  outputCostPerToken?: number | null;
  
  // Support properties (camelCase versions)
  supportsFunctionCalling?: boolean | null;
  supportsParallelFunctionCalling?: boolean | null;
  supportsResponseSchema?: boolean | null;
  supportsToolChoice?: boolean | null;
  supportsVision?: boolean | null;
  supportsPromptCaching?: boolean | null;
  supportsSystemMessages?: boolean | null;
  supportsWebSearch?: boolean | null;
  supportsNativeStreaming?: boolean | null;
  supportsPdfInput?: boolean | null;
  supportsAudioInput?: boolean | null;
  supportsAudioOutput?: boolean | null;
  supportsAssistantPrefill?: boolean | null;
  supportsEmbeddingImageInput?: boolean | null;
  supportsReasoning?: boolean | null;
  
  // Endpoints and modalities
  supportedEndpoints?: string[] | null;
  supportedModalities?: string[] | null;
  supportedOutputModalities?: string[] | null;
  
  key?: string;
  recommendation?: boolean;
  db_model?: boolean;
  base_model?: string;
  cache_creation_input_token_cost?: number | null;
  cache_read_input_token_cost?: number | null;
  input_cost_per_character?: number | null;
  input_cost_per_token_above_128k_tokens?: number | null;
  input_cost_per_query?: number | null;
  input_cost_per_second?: number | null;
  input_cost_per_audio_token?: number | null;
  input_cost_per_token_batches?: number | null;
  output_cost_per_token_batches?: number | null;
  output_cost_per_audio_token?: number | null;
  output_cost_per_character?: number | null;
  output_cost_per_token_above_128k_tokens?: number | null;
  output_cost_per_character_above_128k_tokens?: number | null;
  output_cost_per_second?: number | null;
  output_cost_per_image?: number | null;
  output_vector_size?: number | null;
  
  // Support properties (snake_case versions for compatibility)
  supports_function_calling?: boolean | null;
  supports_parallel_function_calling?: boolean | null;
  supports_response_schema?: boolean | null;
  supports_tool_choice?: boolean | null;
  supports_vision?: boolean | null;
  supports_system_messages?: boolean | null;
  supports_prompt_caching?: boolean | null;
  supports_audio_input?: boolean | null;
  supports_audio_output?: boolean | null;
  supports_pdf_input?: boolean | null;
  supports_embedding_image_input?: boolean | null;
  supports_assistant_prefill?: boolean | null;
  supports_native_streaming?: boolean | null;
  supports_web_search?: boolean | null;
  supports_reasoning?: boolean | null;
  
  // Endpoints and modalities (snake_case versions)
  supported_endpoints?: string[] | null;
  supported_modalities?: string[] | null;
  supported_output_modalities?: string[] | null;
  
  tpm?: number | null;
  rpm?: number | null;
  supported_openai_params?: string[] | null;
  contextWindow?: number | null;
}

// Cost data from LiteLLM
export interface ModelCost {
  model: string; // This is the key for matching, e.g., "azure/gpt-4-turbo" or "gpt-4"
  inputCostPer1kTokens?: number;
  outputCostPer1kTokens?: number;
  contextWindow?: number; // This can also come from LiteLLM, will be merged/prioritized
  maxTokens?: number | null;
  maxInputTokens?: number | null; // Often same as contextWindow in LiteLLM
  maxOutputTokens?: number | null;
  litellmProvider?: string; // Can be different from ModelInfo's provider if LiteLLM is an intermediary
  mode?: string; // Can also be in LiteLLM data
  
  // Supported endpoints and modalities
  supportedEndpoints?: string[];
  supportedModalities?: string[]; // e.g., ["text", "image"]
  supportedOutputModalities?: string[]; // e.g., ["text"]
  
  // Support function properties from LiteLLM
  supportsFunctionCalling?: boolean | null;
  supportsParallelFunctionCalling?: boolean | null;
  supportsResponseSchema?: boolean | null;
  supportsVision?: boolean | null;
  supportsPromptCaching?: boolean | null;
  supportsSystemMessages?: boolean | null;
  supportsToolChoice?: boolean | null;
  supportsWebSearch?: boolean | null;
  supportsNativeStreaming?: boolean | null;
  supportsPdfInput?: boolean | null;
  supportsAudioInput?: boolean | null;
  supportsAudioOutput?: boolean | null;
  supportsAssistantPrefill?: boolean | null;
  supportsEmbeddingImageInput?: boolean | null;
  
  // Search context cost data
  searchContextCostPerQuery?: {
    search_context_size_low?: number;
    search_context_size_medium?: number;
    search_context_size_high?: number;
  };
  
  // Additional cost properties
  input_cost_per_token_batches?: number | null;
  output_cost_per_token_batches?: number | null;
  cache_read_input_token_cost?: number | null;
  cache_creation_input_token_cost?: number | null;
  
  // Rate limiting
  tpm?: number | null; // Tokens per minute
  rpm?: number | null; // Requests per minute
  
  // Additional pricing models
  input_cost_per_character?: number | null;
  output_cost_per_character?: number | null;
  input_cost_per_query?: number | null;
  input_cost_per_second?: number | null;
  output_cost_per_second?: number | null;
  input_cost_per_audio_token?: number | null;
  output_cost_per_audio_token?: number | null;
  output_cost_per_image?: number | null;
  input_cost_per_token_above_128k_tokens?: number | null;
  output_cost_per_token_above_128k_tokens?: number | null;
  output_cost_per_character_above_128k_tokens?: number | null;
  
  // Vector/embedding specific
  output_vector_size?: number | null;
  
  // OpenAI specific parameters
  supported_openai_params?: string[] | null;
}

// Benchmark data from Aider-Polyglot benchmark
export interface BenchmarkData {
  model: string;
  modelid: string;  // Hinzugefügt für das Matching über die modelID
  pass_rate_2: number;
  percent_cases_well_formed: number;
  total_cost: number;
  command: string;
  edit_format: string;
  details: {
    test_cases?: number;
    pass_rate_1?: number;
    pass_num_1?: number;
    pass_num_2?: number;
    error_outputs?: number;
    seconds_per_case?: number;
    [key: string]: unknown;
  };
}

// Combined model data - Renamed back to ModelData
export interface ModelData extends ModelInfo {
  // Inherits all from ModelInfo
  // Add optional cost properties that will be merged from ModelCost
  inputCostPer1kTokens?: number;
  outputCostPer1kTokens?: number;
  // contextWindow is already in ModelInfo, will prioritize LiteLLM if available during merge
  
  // Additional properties from LiteLLM
  litellmProvider_cost?: string; // Example if provider name differs
  mode_cost?: string; // Example if mode differs
  displayName?: string; // For UI purposes
  
  // Supported endpoints and modalities
  supportedEndpoints?: string[];
  supportedModalities?: string[]; // e.g., ["text", "image"]
  supportedOutputModalities?: string[]; // e.g., ["text"]
  
  // Support function properties that might come from LiteLLM
  supportsParallelFunctionCalling?: boolean | null;
  supportsResponseSchema?: boolean | null;
  supportsPromptCaching?: boolean | null;
  supportsSystemMessages?: boolean | null;
  supportsWebSearch?: boolean | null;
  supportsNativeStreaming?: boolean | null;
  supportsPdfInput?: boolean | null;
  supportsAudioInput?: boolean | null;
  supportsAudioOutput?: boolean | null;
  supportsAssistantPrefill?: boolean | null;
  supportsEmbeddingImageInput?: boolean | null;
  
  // Search context cost data
  searchContextCostPerQuery?: {
    search_context_size_low?: number;
    search_context_size_medium?: number;
    search_context_size_high?: number;
  };
  
  // Additional cost properties
  input_cost_per_token_batches?: number | null;
  output_cost_per_token_batches?: number | null;
  cache_read_input_token_cost?: number | null;
  cache_creation_input_token_cost?: number | null;
  
  // Rate limiting
  tpm?: number | null; // Tokens per minute
  rpm?: number | null; // Requests per minute
  
  // Additional pricing models
  input_cost_per_character?: number | null;
  output_cost_per_character?: number | null;
  input_cost_per_query?: number | null;
  input_cost_per_second?: number | null;
  output_cost_per_second?: number | null;
  input_cost_per_audio_token?: number | null;
  output_cost_per_audio_token?: number | null;
  output_cost_per_image?: number | null;
  input_cost_per_token_above_128k_tokens?: number | null;
  output_cost_per_token_above_128k_tokens?: number | null;
  output_cost_per_character_above_128k_tokens?: number | null;
  
  // Vector/embedding specific
  output_vector_size?: number | null;
  
  // OpenAI specific parameters
  supported_openai_params?: string[] | null;
  
  // LiteLLM provisioning status
  'liteLLM-provisioning'?: boolean;
  
  // Deprecation information
  shutdownDate?: string;
  
  // Benchmark data
  benchmarkData?: BenchmarkData;
}

// API response types
export interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

// These interfaces extend ApiResponse with specific types
export type ModelInfoResponse = ApiResponse<ModelInfo[]>;
export type ModelCostResponse = ApiResponse<ModelCost[]>;

// Static data interfaces for Astro build-time generation
export interface StaticModelData {
  models: ModelData[];
  lastUpdated: string;
  source: 'api' | 'mock' | 'cache';
}

export interface StaticBenchmarkData {
  benchmarks: BenchmarkData[];
  lastUpdated: string;
  version: string;
}