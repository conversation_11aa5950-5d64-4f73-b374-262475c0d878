<PERSON><PERSON><PERSON> den Match der Datenbasis sollte der model_name immer nach dem Provider/<modelname> verwendet werden.

   {
            "model_name": "azure/gpt-4o",
            "litellm_params": {},
            "model_info": {
                "id": "1384597a6446d53ec9618bb6b4cb66e6dce29601166c128e63f4f4001de0a7d4",
                "db_model": false,
                "base_model": "azure/gpt-4o-mini",


<PERSON><PERSON> kann dann mit gpt-4o zusammengeführt werden von model_prices_and_context. Behalte auch alle Informationen zu den Supports-Funktionen:

 "gpt-4o": {
        "max_tokens": 16384,
        "max_input_tokens": 128000,
        "max_output_tokens": 16384,
        "input_cost_per_token": 0.0000025,
        "output_cost_per_token": 0.000010,
        "input_cost_per_token_batches": 0.00000125,
        "output_cost_per_token_batches": 0.00000500,
        "cache_read_input_token_cost": 0.00000125,
        "litellm_provider": "openai",
        "mode": "chat",
        "supports_function_calling": true,
        "supports_parallel_function_calling": true,
        "supports_response_schema": true,
        "supports_vision": true,
        "supports_prompt_caching": true,
        "supports_system_messages": true,
        "supports_tool_choice": true,
        "supports_web_search": true,
        "search_context_cost_per_query": {
            "search_context_size_low": 0.030,
            "search_context_size_medium": 0.035,
            "search_context_size_high": 0.050
        }
    },

<info-Endpunkt>
{
    "data": [
        {
            "model_name": "azure/gpt-4-turbo",
            "litellm_params": {},
            "model_info": {
                "id": "3c0069b0c8cd325fc9efb62d6ca4a57f53a9109c24e75b3015a22b50e524e266",
                "db_model": false,
                "base_model": "azure/gpt-4-1106-preview",
                "mode": "chat",
                "confidentiality": "internal",
                "key": "azure/gpt-4-1106-preview",
                "max_tokens": 4096,
                "max_input_tokens": 128000,
                "max_output_tokens": 4096,
                "input_cost_per_token": 1e-05,
                "cache_creation_input_token_cost": null,
                "cache_read_input_token_cost": null,
                "input_cost_per_character": null,
                "input_cost_per_token_above_128k_tokens": null,
                "input_cost_per_query": null,
                "input_cost_per_second": null,
                "input_cost_per_audio_token": null,
                "input_cost_per_token_batches": null,
                "output_cost_per_token_batches": null,
                "output_cost_per_token": 3e-05,
                "output_cost_per_audio_token": null,
                "output_cost_per_character": null,
                "output_cost_per_token_above_128k_tokens": null,
                "output_cost_per_character_above_128k_tokens": null,
                "output_cost_per_second": null,
                "output_cost_per_image": null,
                "output_vector_size": null,
                "litellm_provider": "azure",
                "supports_system_messages": null,
                "supports_response_schema": null,
                "supports_vision": false,
                "supports_function_calling": true,
                "supports_tool_choice": true,
                "supports_assistant_prefill": false,
                "supports_prompt_caching": false,
                "supports_audio_input": false,
                "supports_audio_output": false,
                "supports_pdf_input": false,
                "supports_embedding_image_input": false,
                "supports_native_streaming": null,
                "tpm": null,
                "rpm": null,
                "supported_openai_params": [
                    "temperature",
                    "n",
                    "stream",
                    "stream_options",
                    "stop",
                    "max_tokens",
                    "max_completion_tokens",
                    "tools",
                    "tool_choice",
                    "presence_penalty",
                    "frequency_penalty",
                    "logit_bias",
                    "user",
                    "function_call",
                    "functions",
                    "tools",
                    "tool_choice",
                    "top_p",
                    "logprobs",
                    "top_logprobs",
                    "response_format",
                    "seed",
                    "extra_headers",
                    "parallel_tool_calls",
                    "prediction",
                    "modalities",
                    "audio"
                ]
            }
        },
        {
            "model_name": "azure/text-embedding-ada-002",
            "litellm_params": {},
            "model_info": {
                "id": "bc323220bd2abd5c265f5a3a7ac229bd82b24a3c8a64c508dfd8e14a1f68d425",
                "db_model": false,
                "mode": "embedding",
                "confidentiality": "internal",
                "key": "azure/text-embedding-ada-002",
                "max_tokens": 8191,
                "max_input_tokens": 8191,
                "max_output_tokens": null,
                "input_cost_per_token": 1e-07,
                "cache_creation_input_token_cost": null,
                "cache_read_input_token_cost": null,
                "input_cost_per_character": null,
                "input_cost_per_token_above_128k_tokens": null,
                "input_cost_per_query": null,
                "input_cost_per_second": null,
                "input_cost_per_audio_token": null,
                "input_cost_per_token_batches": null,
                "output_cost_per_token_batches": null,
                "output_cost_per_token": 0.0,
                "output_cost_per_audio_token": null,
                "output_cost_per_character": null,
                "output_cost_per_token_above_128k_tokens": null,
                "output_cost_per_character_above_128k_tokens": null,
                "output_cost_per_second": null,
                "output_cost_per_image": null,
                "output_vector_size": null,
                "litellm_provider": "azure",
                "supports_system_messages": null,
                "supports_response_schema": null,
                "supports_vision": false,
                "supports_function_calling": false,
                "supports_tool_choice": false,
                "supports_assistant_prefill": false,
                "supports_prompt_caching": false,
                "supports_audio_input": false,
                "supports_audio_output": false,
                "supports_pdf_input": false,
                "supports_embedding_image_input": false,
                "supports_native_streaming": null,
                "tpm": null,
                "rpm": null,
                "supported_openai_params": [
                    "temperature",
                    "n",
                    "stream",
                    "stream_options",
                    "stop",
                    "max_tokens",
                    "max_completion_tokens",
                    "tools",
                    "tool_choice",
                    "presence_penalty",
                    "frequency_penalty",
                    "logit_bias",
                    "user",
                    "function_call",
                    "functions",
                    "tools",
                    "tool_choice",
                    "top_p",
                    "logprobs",
                    "top_logprobs",
                    "response_format",
                    "seed",
                    "extra_headers",
                    "parallel_tool_calls",
                    "prediction",
                    "modalities",
                    "audio"
                ]
            }
        },
        {
            "model_name": "azure/gpt-4o",
            "litellm_params": {},
            "model_info": {
                "id": "a71d34a1a02f35df7fe78cc81817d993bb41333cc3429ebed5ab40fda829baca",
                "db_model": false,
                "base_model": "azure/gpt-4o",
                "mode": "chat",
                "confidentiality": "internal",
                "recommendation": true,
                "key": "azure/gpt-4o",
                "max_tokens": 16384,
                "max_input_tokens": 128000,
                "max_output_tokens": 16384,
                "input_cost_per_token": 2.5e-06,
                "cache_creation_input_token_cost": null,
                "cache_read_input_token_cost": 1.25e-06,
                "input_cost_per_character": null,
                "input_cost_per_token_above_128k_tokens": null,
                "input_cost_per_query": null,
                "input_cost_per_second": null,
                "input_cost_per_audio_token": null,
                "input_cost_per_token_batches": null,
                "output_cost_per_token_batches": null,
                "output_cost_per_token": 1e-05,
                "output_cost_per_audio_token": null,
                "output_cost_per_character": null,
                "output_cost_per_token_above_128k_tokens": null,
                "output_cost_per_character_above_128k_tokens": null,
                "output_cost_per_second": null,
                "output_cost_per_image": null,
                "output_vector_size": null,
                "litellm_provider": "azure",
                "supports_system_messages": null,
                "supports_response_schema": true,
                "supports_vision": true,
                "supports_function_calling": true,
                "supports_tool_choice": true,
                "supports_assistant_prefill": false,
                "supports_prompt_caching": true,
                "supports_audio_input": false,
                "supports_audio_output": false,
                "supports_pdf_input": false,
                "supports_embedding_image_input": false,
                "supports_native_streaming": null,
                "tpm": null,
                "rpm": null,
                "supported_openai_params": [
                    "temperature",
                    "n",
                    "stream",
                    "stream_options",
                    "stop",
                    "max_tokens",
                    "max_completion_tokens",
                    "tools",
                    "tool_choice",
                    "presence_penalty",
                    "frequency_penalty",
                    "logit_bias",
                    "user",
                    "function_call",
                    "functions",
                    "tools",
                    "tool_choice",
                    "top_p",
                    "logprobs",
                    "top_logprobs",
                    "response_format",
                    "seed",
                    "extra_headers",
                    "parallel_tool_calls",
                    "prediction",
                    "modalities",
                    "audio"
                ]
            }
        },
    ]
}
</info>

<models>
{
    "sample_spec": {
        "max_tokens": "LEGACY parameter. set to max_output_tokens if provider specifies it. IF not set to max_input_tokens, if provider specifies it.", 
        "max_input_tokens": "max input tokens, if the provider specifies it. if not default to max_tokens",
        "max_output_tokens": "max output tokens, if the provider specifies it. if not default to max_tokens", 
        "input_cost_per_token": 0.0000,
        "output_cost_per_token": 0.000,
        "output_cost_per_reasoning_token": 0.000,
        "litellm_provider": "one of https://docs.litellm.ai/docs/providers",
        "mode": "one of: chat, embedding, completion, image_generation, audio_transcription, audio_speech, image_generation, moderation, rerank",
        "supports_function_calling": true,
        "supports_parallel_function_calling": true,
        "supports_vision": true,
        "supports_audio_input": true, 
        "supports_audio_output": true,
        "supports_prompt_caching": true,
        "supports_response_schema": true,
        "supports_system_messages": true,
        "supports_reasoning": true,
        "supports_web_search": true,
        "search_context_cost_per_query": {
            "search_context_size_low": 0.0000,
            "search_context_size_medium": 0.0000,
            "search_context_size_high": 0.0000
        },
        "deprecation_date": "date when the model becomes deprecated in the format YYYY-MM-DD"
    },   
    "gpt-4.1": {
        "max_tokens": 32768,
        "max_input_tokens": 1047576,
        "max_output_tokens": 32768,
        "input_cost_per_token": 2e-6,
        "output_cost_per_token": 8e-6,
        "input_cost_per_token_batches": 1e-6,
        "output_cost_per_token_batches": 4e-6,
        "cache_read_input_token_cost": 0.5e-6,
        "litellm_provider": "openai",
        "mode": "chat",
        "supported_endpoints": ["/v1/chat/completions", "/v1/batch", "/v1/responses"],
        "supported_modalities": ["text", "image"],
        "supported_output_modalities": ["text"],
        "supports_function_calling": true,
        "supports_parallel_function_calling": true,
        "supports_response_schema": true,
        "supports_vision": true,
        "supports_prompt_caching": true,
        "supports_system_messages": true,
        "supports_tool_choice": true,
        "supports_native_streaming": true,
        "supports_web_search": true,
        "search_context_cost_per_query": {
            "search_context_size_low": 30e-3,
            "search_context_size_medium": 35e-3,
            "search_context_size_high": 50e-3
        }
    },
}

</models>