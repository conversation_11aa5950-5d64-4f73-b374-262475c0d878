{"basicInfo": {"modelId": "claude-3-7-sonnet@20250219", "displayName": "Claude 3.7 Sonnet", "provider": "Anthropic", "modelFamily": "<PERSON>", "version": "20250219", "description": "Das bisher intelligenteste Modell von Anthropic und das erste Claude-Modell, das erweitertes Denken bietet – die Fähigkeit, komplexe Probleme mit sorgfältiger, schrittweiser Argumentation zu lösen", "releaseDate": "2025-03-20", "status": "GA", "knowledgeCutoff": "November 2024"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 128000, "architecture": "Transformer", "parameterCount": "<PERSON><PERSON> ver<PERSON><PERSON><PERSON>", "supportedInputTypes": ["text", "image", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/webp"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": true, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 55, "tokensPerMinute": 500000, "contextLength": 200000}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 3.0, "outputCostPer1MTokens": 15.0, "cachingCosts": {"cacheWrites": 3.75, "cacheHits": 0.3}, "currency": "USD"}, "availability": {"regions": [{"region": "us-east5", "availability": "GA"}, {"region": "europe-west1", "availability": "GA"}, {"region": "global", "availability": "GA"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI", "AWS Bedrock", "Anthropic API"], "platformSpecificIds": {"anthropicApi": "claude-3-7-sonnet-20250219", "awsBedrock": "anthropic.claude-3-7-sonnet-20250219-v1:0", "vertexAi": "claude-3-7-sonnet@20250219"}}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": true, "fixedQuota": true}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 62.3, "alternativeScores": {"multipleAttempts": 70.3}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Mit bash/editor tools, durchschnittlich über 10 Versuche"}, {"benchmarkName": "Terminal-bench", "category": "Agentic terminal coding", "score": 35.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-16", "notes": "Claude Code agent framework, ±1.3% confidence interval"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 78.2, "alternativeScores": {"multipleAttempts": 83.3}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Mit Extended Thinking (bis zu 64k Tokens)"}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic tool use", "score": 81.2, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Mit Extended Thinking und Tool Use"}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic tool use", "score": 58.4, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "Mit Extended Thinking und Tool Use"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 85.9, "metric": "Accuracy", "attemptType": "average", "date": "2025-05-22", "notes": "Durchschnitt über 14 nicht-englische Sprachen, mit Extended Thinking"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 76.0, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "MMMU benchmark from vals.ai (Claude 3.7 Sonnet Thinking)"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 54.8, "alternativeScores": {"multipleAttempts": 85.0}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-22", "notes": "<PERSON>t Extended Thinking, nucleus sampling top_p=0.95"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 82.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-15", "notes": "MATH 500 benchmark"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 64.9, "alternativeScores": {"withThinking": 64.9, "noThinking": 60.4}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-02-24", "notes": "Aider polyglot benchmark with diff edit format (32k thinking tokens vs no thinking)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 97.8, "alternativeScores": {"withThinking": 97.8, "noThinking": 93.3}, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-02-24", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1357.13, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #5"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 63.8, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 8.04, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "<PERSON> 3.7 <PERSON><PERSON> (Thinking): 8.04±1.07"}], "metadata": {"lastUpdated": "2025-06-09T18:22:00Z", "dataSource": "Google Cloud Vertex AI Documentation, Anthropic Claude 4 Blog Post, Anthropic Models Overview, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard, MATH 500 Benchmark Update", "version": "1.4"}}