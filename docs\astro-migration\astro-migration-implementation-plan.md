# Astro Migration Implementation Plan

## Überblick

Dieser Plan beschreibt die schrittweise Migration des LLM Browser Projekts von Next.js 15 zu Astro, während wir im selben Repository arbeiten. Die Migration erfolgt durch parallele Entwicklung in einem Unterverzeichnis, um Risiken zu minimieren und kontinuierliche Funktionalität zu gewährleisten.

## Grundprinzipien

1. **Parallele Entwicklung**: Neue Astro-Implementierung in `/astro` Unterverzeichnis
2. **Inkrementeller Ansatz**: Komponente für Komponente migrieren und testen
3. **Gemeinsame Assets**: Wiederverwendung von Daten, Bildern und Konfigurationen
4. **Feature-Parität**: Vollständige Funktionalitätsabdeckung vor dem Switch
5. **Performance-Fokus**: Optimierung für statische Generierung und GitLab Pages

## Phase 1: Setup & Grundstruktur

### 1.1 Astro-Projekt initialisieren

- [ ] Astro-Projekt in `/astro` Unterverzeichnis erstellen
```bash
mkdir -p astro
cd astro
npm create astro@latest .
```

- [ ] Grundlegende Konfiguration in `astro.config.mjs` einrichten
```javascript
import { defineConfig } from 'astro/config';
import react from '@astrojs/react';
import tailwind from '@astrojs/tailwind';

export default defineConfig({
  integrations: [
    react(), // Für React-Komponenten-Support
    tailwind({
      config: { path: './tailwind.config.mjs' },
    }),
  ],
  outDir: '../public/astro', // Output in separates Verzeichnis
});
```

- [ ] TypeScript-Konfiguration anpassen
```bash
cp ../tsconfig.json astro/tsconfig.json
```
Dann anpassen für Astro-Spezifika.

### 1.2 Abhängigkeiten einrichten

- [ ] Basis-Abhängigkeiten installieren
```bash
cd astro
npm install
```

- [ ] React und React DOM installieren
```bash
npm install react react-dom
```

- [ ] Tailwind CSS und Konfiguration einrichten
```bash
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
```

- [ ] ShadCN UI Komponenten installieren
```bash
npx shadcn@latest init
```

### 1.3 Projektstruktur erstellen

- [ ] Verzeichnisstruktur anlegen
```bash
mkdir -p src/{components,layouts,pages,styles,utils,data}
mkdir -p src/components/{ui,models,layout}
mkdir -p src/data/{models,benchmarks}
```

- [ ] Basis-Layout erstellen
```bash
touch src/layouts/Layout.astro
touch src/styles/globals.css
```

- [ ] Routing-Struktur anlegen
```bash
mkdir -p src/pages/{models,benchmark}
touch src/pages/index.astro
touch src/pages/models/index.astro
touch src/pages/benchmark/index.astro
```

### 1.4 Build-Pipeline konfigurieren

- [ ] Scripts für Daten-Generierung erstellen
```bash
mkdir -p scripts
touch scripts/generate-static-data.ts
```

- [ ] Package.json Scripts anpassen
```json
{
  "scripts": {
    "dev": "astro dev",
    "build": "astro build",
    "preview": "astro preview",
    "generate:data": "tsx scripts/generate-static-data.ts",
    "prebuild": "npm run generate:data"
  }
}
```

- [ ] GitLab CI/CD Konfiguration für Astro erstellen
```bash
touch .gitlab-ci.yml
```

## Phase 2: Daten-Migration

### 2.1 Statische Daten-Generierung

- [ ] API-Service für Build-Time Fetching implementieren
```bash
mkdir -p src/services
touch src/services/api.ts
```

- [ ] Daten-Generierungs-Script implementieren
```typescript
// scripts/generate-static-data.ts
import { fetchModelInfo, fetchCombinedModelData } from '../src/services/api';
import fs from 'fs/promises';
import path from 'path';

async function generateStaticModelData() {
  // Implementierung...
}

generateStaticModelData();
```

- [ ] Benchmark-Daten kopieren und transformieren
```bash
mkdir -p src/data/benchmarks
cp ../static/polyglot-leaderboard-*.json src/data/benchmarks/
```

### 2.2 Daten-Utilities migrieren

- [ ] Deduplication-Algorithmen portieren
```bash
touch src/utils/deduplication.ts
```

- [ ] Matching-Algorithmen portieren
```bash
touch src/utils/matching.ts
```

- [ ] Daten-Transformation-Utilities erstellen
```bash
touch src/utils/transformations.ts
```

### 2.3 Typdefinitionen migrieren

- [ ] Model-Interfaces definieren
```bash
mkdir -p src/types
touch src/types/models.ts
```

- [ ] Benchmark-Interfaces definieren
```bash
touch src/types/benchmarks.ts
```

- [ ] Utility-Typen definieren
```bash
touch src/types/utils.ts
```

## Phase 3: UI-Komponenten Migration

### 3.1 Basis-UI-Komponenten

- [ ] ShadCN UI Komponenten installieren und konfigurieren
```bash
npx shadcn@latest init
```

- [ ] Basis-Komponenten installieren
```bash
npx shadcn@latest add button input label select card dialog form tooltip
```

- [ ] Tailwind-Konfiguration anpassen
```bash
cp ../tailwind.config.js astro/tailwind.config.mjs
```
Dann für Astro anpassen.

### 3.2 Layout-Komponenten

- [ ] Header-Komponente migrieren
```bash
touch src/components/layout/Header.astro
```

- [ ] Footer-Komponente migrieren
```bash
touch src/components/layout/Footer.astro
```

- [ ] Navigation-Komponente migrieren
```bash
touch src/components/layout/Navigation.astro
```

- [ ] Theme-Toggle implementieren
```bash
touch src/components/layout/ThemeToggle.tsx
```

### 3.3 Model-Komponenten

- [ ] ModelTable als React-Island implementieren
```bash
touch src/components/models/ModelTable.tsx
```

- [ ] ModelDetails als React-Island implementieren
```bash
touch src/components/models/ModelDetails.tsx
```

- [ ] BenchmarkDetails als React-Island implementieren
```bash
touch src/components/models/BenchmarkDetails.tsx
```

### 3.4 Interaktive Komponenten

- [ ] Filter-Komponenten migrieren
```bash
touch src/components/models/ModelFilter.tsx
```

- [ ] Vergleichs-Komponenten migrieren
```bash
touch src/components/models/ModelComparison.tsx
```

- [ ] Export-Funktionalität implementieren
```bash
touch src/components/models/ExportButton.tsx
```

## Phase 4: Seiten-Migration

### 4.1 Hauptseiten

- [ ] Homepage implementieren
```astro
---
// src/pages/index.astro
import Layout from '../layouts/Layout.astro';
---

<Layout title="LLM Browser">
  <main>
    <h1>LLM Browser</h1>
    <!-- Content -->
  </main>
</Layout>
```

- [ ] Models-Übersichtsseite implementieren
```bash
touch src/pages/models/index.astro
```

- [ ] Benchmark-Übersichtsseite implementieren
```bash
touch src/pages/benchmark/index.astro
```

### 4.2 Dynamische Seiten

- [ ] Model-Detailseite mit getStaticPaths implementieren
```bash
touch src/pages/models/[id].astro
```

- [ ] Benchmark-Detailseite implementieren
```bash
touch src/pages/benchmark/[id].astro
```

- [ ] Vergleichsseite implementieren
```bash
touch src/pages/compare.astro
```

### 4.3 API-Endpunkte

- [ ] Statische JSON-Endpunkte erstellen
```bash
mkdir -p src/pages/api
touch src/pages/api/models.json.ts
touch src/pages/api/benchmarks.json.ts
```

## Phase 5: State Management

### 5.1 Client-Side State

- [ ] Nanostores für globalen State einrichten
```bash
npm install nanostores @nanostores/react
touch src/stores/modelStore.ts
```

- [ ] Filter-State implementieren
```bash
touch src/stores/filterStore.ts
```

- [ ] Vergleichs-State implementieren
```bash
touch src/stores/compareStore.ts
```

### 5.2 Persistenz

- [ ] LocalStorage-Integration für Benutzereinstellungen
```bash
touch src/utils/storage.ts
```

- [ ] Theme-Persistenz implementieren
```bash
touch src/utils/theme.ts
```

## Phase 6: Testing & Optimierung

### 6.1 Unit Tests

- [ ] Test-Setup einrichten
```bash
npm install -D vitest @testing-library/react
mkdir -p src/tests
touch vitest.config.ts
```

- [ ] Komponenten-Tests erstellen
```bash
mkdir -p src/tests/components
touch src/tests/components/ModelTable.test.tsx
```

- [ ] Utility-Tests erstellen
```bash
mkdir -p src/tests/utils
touch src/tests/utils/deduplication.test.ts
```

### 6.2 Performance-Optimierung

- [ ] Bild-Optimierung implementieren
```bash
npm install -D sharp
```

- [ ] CSS-Optimierung konfigurieren
```bash
touch postcss.config.mjs
```

- [ ] Bundle-Analyse durchführen
```bash
npm install -D rollup-plugin-visualizer
```

### 6.3 Lighthouse-Tests

- [ ] Performance-Baseline erstellen
- [ ] Accessibility-Tests durchführen
- [ ] SEO-Optimierung implementieren

## Phase 7: Deployment & Integration

### 7.1 GitLab Pages Setup

- [ ] CI/CD-Pipeline konfigurieren
```yaml
# .gitlab-ci.yml
stages:
  - build
  - deploy

variables:
  NODE_VERSION: "20"

build:
  stage: build
  image: node:${NODE_VERSION}
  script:
    - cd astro
    - npm ci
    - npm run generate:data
    - npm run build
    - cp -r dist/ ../public/
  artifacts:
    paths:
      - public/
  only:
    - main

pages:
  stage: deploy
  script:
    - echo "Deploying to GitLab Pages"
  artifacts:
    paths:
      - public/
  only:
    - main
```

### 7.2 Routing-Integration

- [ ] Base-Path-Konfiguration für GitLab Pages
```javascript
// astro.config.mjs
export default defineConfig({
  site: 'https://your-username.gitlab.io',
  base: '/your-repo-name',
  // ...
});
```

### 7.3 Umschaltung vorbereiten

- [ ] Redirect-Strategie implementieren
```bash
touch public/_redirects
```

## Phase 8: Cutover & Cleanup

### 8.1 Vollständige Migration

- [ ] Feature-Parität validieren
- [ ] Performance-Vergleich durchführen
- [ ] Benutzer-Feedback einholen

### 8.2 Cutover durchführen

- [ ] Astro-Build zum Hauptverzeichnis verschieben
```bash
rm -rf public/*
cp -r astro/dist/* public/
```

- [ ] Routing-Umleitung aktivieren
```bash
# In der Root index.html
<meta http-equiv="refresh" content="0;url=/astro/index.html">
```

### 8.3 Cleanup

- [ ] Next.js-spezifische Abhängigkeiten entfernen
- [ ] Nicht mehr benötigte Dateien archivieren
- [ ] Dokumentation aktualisieren

## Checkliste für die Migration

### Vorbereitungsphase
- [ ] Aktuelle Codebase analysieren und dokumentieren
- [ ] Feature-Inventar erstellen
- [ ] Kritische Pfade identifizieren
- [ ] Team-Schulung für Astro durchführen

### Entwicklungsphase
- [ ] Astro-Projekt in `/astro` initialisieren
- [ ] Grundlegende Projektstruktur erstellen
- [ ] Daten-Generierungs-Pipeline implementieren
- [ ] UI-Komponenten migrieren
- [ ] Seiten-Struktur implementieren
- [ ] State-Management-Lösung implementieren
- [ ] Tests schreiben und ausführen

### Testphase
- [ ] Funktionale Tests durchführen
- [ ] Performance-Tests durchführen
- [ ] Accessibility-Tests durchführen
- [ ] Cross-Browser-Tests durchführen
- [ ] Mobile-Responsiveness testen

### Deployment-Phase
- [ ] GitLab CI/CD-Pipeline konfigurieren
- [ ] Staging-Deployment durchführen
- [ ] UAT (User Acceptance Testing) durchführen
- [ ] Performance-Optimierungen implementieren
- [ ] Finale Anpassungen vornehmen

### Cutover-Phase
- [ ] Go/No-Go-Entscheidung treffen
- [ ] Cutover-Plan kommunizieren
- [ ] Cutover durchführen
- [ ] Post-Cutover-Tests durchführen
- [ ] Monitoring einrichten

### Post-Migration
- [ ] Dokumentation aktualisieren
- [ ] Lessons Learned dokumentieren
- [ ] Nicht mehr benötigte Ressourcen bereinigen
- [ ] Performance-Metriken vergleichen
- [ ] Erfolg der Migration bewerten

## Zeitplan

| Phase | Dauer | Start | Ende |
|-------|-------|-------|------|
| 1: Setup & Grundstruktur | 1 Woche | Tag 1 | Tag 7 |
| 2: Daten-Migration | 1 Woche | Tag 8 | Tag 14 |
| 3: UI-Komponenten | 2 Wochen | Tag 15 | Tag 28 |
| 4: Seiten-Migration | 1 Woche | Tag 29 | Tag 35 |
| 5: State Management | 3 Tage | Tag 36 | Tag 38 |
| 6: Testing & Optimierung | 1 Woche | Tag 39 | Tag 45 |
| 7: Deployment & Integration | 3 Tage | Tag 46 | Tag 48 |
| 8: Cutover & Cleanup | 2 Tage | Tag 49 | Tag 50 |

**Gesamtdauer: 7-8 Wochen**

## Risiken & Mitigation

### Technische Risiken

| Risiko | Wahrscheinlichkeit | Auswirkung | Mitigation |
|--------|-------------------|------------|------------|
| API-Endpunkte nicht zur Build-Zeit verfügbar | Mittel | Hoch | Fallback-Daten vorbereiten, Caching-Strategie implementieren |
| Performance-Regression durch React-Islands | Niedrig | Mittel | Selektive Hydration, Lazy Loading, Bundle-Analyse |
| ShadCN UI Kompatibilitätsprobleme | Mittel | Mittel | Komponenten-Tests, Fallback-Styling vorbereiten |
| Routing-Unterschiede zwischen Next.js und Astro | Hoch | Mittel | Routing-Mapping dokumentieren, Redirects implementieren |
| State-Management-Komplexität | Mittel | Hoch | Nanostores schrittweise einführen, Zustandsübergänge testen |

### Projektrisiken

| Risiko | Wahrscheinlichkeit | Auswirkung | Mitigation |
|--------|-------------------|------------|------------|
| Zeitüberschreitung | Mittel | Mittel | Puffer einplanen, Phasen priorisieren |
| Feature-Parität nicht erreicht | Niedrig | Hoch | Feature-Inventar pflegen, regelmäßige Überprüfungen |
| Team-Wissenstransfer unzureichend | Mittel | Hoch | Dokumentation, Pair Programming, Schulungen |
| GitLab Pages Deployment-Probleme | Mittel | Hoch | Lokale Tests, Staging-Umgebung, CI/CD-Tests |
| Benutzerakzeptanz | Niedrig | Mittel | Frühes Feedback einholen, A/B-Tests durchführen |

## Erfolgskriterien

Die Migration gilt als erfolgreich, wenn folgende Kriterien erfüllt sind:

1. **Funktionale Parität**: Alle Features der Next.js-Version sind in der Astro-Version verfügbar
2. **Performance-Verbesserung**: Lighthouse-Score von mindestens 90 in allen Kategorien
3. **Build-Zeit**: Unter 5 Minuten für vollständigen Build
4. **Deployment-Erfolg**: Erfolgreiche Bereitstellung auf GitLab Pages
5. **Benutzer-Feedback**: Positive Rückmeldungen zur Geschwindigkeit und Benutzererfahrung

## Ressourcenbedarf

| Ressource | Beschreibung | Verfügbarkeit |
|-----------|-------------|---------------|
| Entwickler | Frontend-Entwickler mit React/TypeScript-Erfahrung | Vollzeit |
| Astro-Experte | Entwickler mit Astro-Erfahrung für Beratung | Teilzeit/Beratend |
| UX-Tester | Für Benutzertests und Feedback | Nach Bedarf |
| CI/CD-Spezialist | Für GitLab Pages Deployment-Optimierung | Nach Bedarf |
| Infrastruktur | Entwicklungsumgebung, Staging-Umgebung | Durchgehend |

## Fazit

Dieser Implementierungsplan bietet einen strukturierten Ansatz für die Migration des LLM Browser Projekts von Next.js zu Astro, während die Entwicklung im selben Repository fortgesetzt wird. Durch die parallele Entwicklung in einem Unterverzeichnis minimieren wir Risiken und gewährleisten kontinuierliche Funktionalität.

Die schrittweise Migration ermöglicht es, Komponente für Komponente zu migrieren und zu testen, während wir gleichzeitig von den Vorteilen des Astro-Frameworks profitieren. Mit diesem Plan können wir eine erfolgreiche Migration durchführen und eine optimierte, GitLab Pages-kompatible Version des LLM Browsers bereitstellen.