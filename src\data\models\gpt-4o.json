{"basicInfo": {"modelId": "gpt-4o-2024-08-06", "displayName": "GPT-4o", "provider": "OpenAI", "modelFamily": "GPT", "version": "2024-08-06", "description": "Vielseitiges, hochintelligentes Flaggschiff-Modell. Akzeptiert Text- und Bildeingaben und produziert Textausgaben. Das beste Modell für die meisten Aufgaben.", "releaseDate": "2024-05-13", "status": "GA", "knowledgeCutoff": "Oktober 2023"}, "technicalSpecs": {"contextWindow": 128000, "maxOutputTokens": 16384, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": true, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": false, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": true, "codeInterpreter": true, "dalleIntegration": true, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 10000, "tokensPerMinute": 30000000, "batchQueueLimit": 5000000000}, "temperature": {"min": 0, "max": 2, "default": 1.0}, "topP": 1.0}, "pricing": {"inputCostPer1MTokens": 2.5, "outputCostPer1MTokens": 10.0, "cachingCosts": {"cacheHits": 1.25}, "batchProcessingCosts": {"inputCostPer1MTokens": 1.25, "outputCostPer1MTokens": 5.0}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "Azure OpenAI", "OpenAI Batch API"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 57.2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Software engineering benchmark for real-world coding tasks"}, {"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 88.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-08-06"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 56.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-08-06"}, {"benchmarkName": "DROP", "category": "Reasoning & Knowledge", "score": 83.4, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-08-06"}, {"benchmarkName": "MGSM", "category": "Mathematics", "score": 90.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-08-06"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 60.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-15", "notes": "MATH 500 benchmark - updated score"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 69.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-08-06"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 63.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-08-06"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 69.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-05-13", "notes": "Multimodal understanding and reasoning"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 49.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-17", "notes": "Aider-Polyglot benchmark with diff edit format"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 99.6, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-01-17", "notes": "Percentage of well-formed responses in Aider-Polyglot benchmark"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 32.9, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Graphwalks BFS <128k accuracy", "category": "Long context", "score": 45.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Multi-round co-reference resolution in langen Kontexten"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1350.0, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #5"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 2.72, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "GPT-4o (November 2024): 2.72±0.64"}], "metadata": {"lastUpdated": "2025-06-09T18:22:00Z", "dataSource": "OpenAI Platform Documentation, SWE-bench Verified Benchmark, MATH 500 Benchmark Update", "version": "1.2"}}