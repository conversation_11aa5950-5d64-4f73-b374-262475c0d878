---
title: "Release-Notes"
excerpt: "Das Changelog beschreibt die letzten Änderungen kurz und knapp für den LLM-Browser"
category: "release-notes"
tags: ["launch", "model-comparison", "benchmarks", "astro", "react", "performance"]
publishDate: "2025-06-15T09:00:00Z"
lastUpdated: "2025-06-15T09:00:00Z"
author:
  name: "LLM Browser Entwicklungsteam"
  role: "Product & Engineering"
readingTime: 3
featured: false
relatedModelIds: ["o3", "claude-opus-4", "gemini-2.5-pro", "deepseek-v3", "deepseek-r1"]
releaseVersion: "0.5"

# i18n-spezifische Felder
lang: "de"
translationKey: "llm-browser-release-notes"
availableLanguages: ["de", "en", "pl"]

metaDescription: "Kurze knappe Release-Note"
metaKeywords: ["LLM Browser", "KI-Modelle", "Benchmarks", "Model Comparison", "Performance Analysis"]
featuredImage: "/images/blog/2025-06-1st-release.png"
changelog:
  - type: "added"
    description: "Releaes-Note-Beschreibung für die 0.0.3 vom 15.06.2025"
    impact: "minor"
    technicalDetails: "Kurzbeschreibung der Änderungen"

---

## Roadmap

- [Minor] Weitere Justierung des Empfehlungs-Systems hinsichtlich verwendeter Benchmarks

## Releases

### Release 21.06.2025 (v0.5)
- Für Demonstrationszwecken Implementierung auch einer I18N-Funktionalität
  - Intiale Version (zunächst server-seitig) von Paweł Kamiński
  - Überarbeite Version um mit clientseitigen I18N-Funktionen arbeiten zu können aufgrund der GitLab Pages Limitierung
- Aktualisierung der Gemini Model-Cards und Preisdaten mit dem nun finalen Modellen mit deutlichen Preis-Nachlässen
  ![](/images/blog/2025-06-21-gemini-prices.png)


### Release 19.06.2025 (v0.4.1)

- Model-Cards
  - GPT 4.1-Mini in den Vergleich aufgenommen inkl. Benchmarkwerte
  - 4.1*, gpt-4o PDF-Support, Vision-Support, Image-Embedding fixed (false->true)
  
### Release 15.06.2025 (v0.4)

- UI-Optimierungen
  - [Minor] Dark/Light-Thema umgesetzt, der über die Toolbar aktiviert werden kann
  - [Minor] GitLab-Direkt-Link in die Top-Navigation
  - [Minor] Aider-Benchmarks wieder direkt in der Top-Secondary-Navigation aufgenommen
- Codebasis
  - [Minor] Refactoring der Components, so dass die entstandene aktuelle Fachlichkeit auch technisch besser repräsentiert ist
  - [Minor] Einführung Linter-Setup
  - [Minor] Umbau auf Type-Safety-Setup (Typescript)
  - [Minor] Initial-Setup Vitetest
- Features
  - [Minor] Start der Release-Notes
  - [Minor] Blog-Funktion nicht mit Filter-Funktionen und Kategorien-Filter-Nutzung (war nur angedeutet aber nicht ausprogrammiert)
- Model-Cards
  - [Minor] Einführung Model-Card von https://mistral.ai/news/magistral (Neues Reasoning-Modell von Mistral)


### Release 11.06.2025 (v0.3)

- Features
  - [Major] Aufbau von KI-Anwendungsfällen und den jeweils zugehörigen berechneten Modell-Empfehlungen
    - Gewichtete Benchmarks + Capabilities + Kosten + Performance + Verfügbarkeit als Grundansatz
  - [Major] Aufbau eines Blog-Systems, um detailliert auf Entwicklungen und Empfehlungen eingehen zu können
    - Inhalte werden einfach als MD-Dateien gepflegt mit einem Frontmatter-Ansatz  
  - [Minor] Aktualisierung der Model-Card-Benchmark-Daten für Claude 4, OpenAI o4, o4-mini 

### Release 06.06.2025 (v0.2)

- Features
  - [Major] Einführung von Model-Cards, um unabhängig neben dem LLM-Proxy-Daten und unabhängig von den Polyglot-Benchmarkdaten Informationen bereitstellen zu können
  - [Major] Iterativer Aufbau der Modell-Card-Daten zur Nutzung
- Dev | DevOps
  - [Minor] GitLab-Publishing
  - [Major] Komplettes Refactoring von der NEXT-App auf Astroframework d.h. generierten Websites mit JS-Support, um über einfache GitLab-Page puliziert werden zu können


### Release 16.05.2025 (v.0.1)

- Initialversion
  - [Major] Erste Demo-Version für die _NEXT25 mit Darstellung der LLM-Proxy-Modell-Daten sowie dem Aider-Polygot-Benchmark als Ausgangsbasis. Primäres Ziel war mit einer Prompt-Kaskade eine lauffähige Demo aufzubauen


## Weiterentwicklung
Ihr habt Wünsche zur Weiterentwicklung, einfach melden!

**Nützliche Links:**

- **[GitLab Repository](https://gitlab.com/iteratec/llm-browser)** - Source Code
