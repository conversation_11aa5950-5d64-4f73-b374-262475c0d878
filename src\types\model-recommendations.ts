// Types for model recommendations based on standard company use cases
export interface UseCase {
  id: string;
  name: string;
  description: string;
  category: UseCaseCategory;
  requiredCapabilities: string[];
  optimalBenchmarks: string[];
  priority: 'high' | 'medium' | 'low';
}

export type UseCaseCategory = 
  | 'code-development' 
  | 'analysis-review' 
  | 'documentation' 
  | 'automation' 
  | 'data-processing' 
  | 'learning';

export interface ModelRecommendation {
  modelId: string;
  useCase: string;
  score: number; // 0-100
  reasoning: string;
  strengths: string[];
  limitations: string[];
  costEffectiveness: 'high' | 'medium' | 'low';
  suitability: 'excellent' | 'good' | 'acceptable' | 'limited';
}

export interface UseCaseRecommendations {
  useCase: UseCase;
  recommendedModels: ModelRecommendation[];
  alternativeModels: ModelRecommendation[];
  notRecommendedModels?: ModelRecommendation[];
}

// Extended Model Card with recommendations
export interface ModelCardWithRecommendations {
  recommendations: {
    primaryUseCases: string[]; // Use case IDs this model excels at
    secondaryUseCases: string[]; // Use cases where it's suitable
    strengths: string[];
    idealFor: string[];
    considerations: string[];
    costProfile: 'budget' | 'standard' | 'premium';
    complexityLevel: 'simple' | 'moderate' | 'complex' | 'enterprise';
  };
}

// Standard company use cases
export const STANDARD_USE_CASES: UseCase[] = [
  {
    id: 'code-generation',
    name: 'Code-Generierung',
    description: 'Entwicklung neuer Features, Implementierung von Anforderungen, Prototyping',
    category: 'code-development',
    requiredCapabilities: ['functionCalling', 'reasoning'],
    optimalBenchmarks: ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
    priority: 'high'
  },
  {
    id: 'code-review',
    name: 'Code Review & Analyse',
    description: 'Code-Qualitätsprüfung, Sicherheitsanalyse, Architektur-Reviews',
    category: 'analysis-review',
    requiredCapabilities: ['reasoning', 'thinking'],
    optimalBenchmarks: ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
    priority: 'high'
  },
  {
    id: 'debugging',
    name: 'Debugging & Troubleshooting',
    description: 'Fehlersuche, Root-Cause-Analyse, Problemlösung in bestehenden Systemen',
    category: 'analysis-review',
    requiredCapabilities: ['reasoning', 'thinking', 'codeExecution'],
    optimalBenchmarks: ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
    priority: 'high'
  },
  {
    id: 'documentation',
    name: 'Dokumentation',
    description: 'API-Dokumentation, Code-Kommentare, technische Spezifikationen',
    category: 'documentation',
    requiredCapabilities: ['multilingualSupport'],
    optimalBenchmarks: ['LiveCodeBench v2025', 'MMLU', 'IFEval'],
    priority: 'medium'
  },
  {
    id: 'refactoring',
    name: 'Refactoring & Optimierung',
    description: 'Code-Umstrukturierung, Performance-Optimierung, Modernisierung',
    category: 'code-development',
    requiredCapabilities: ['reasoning', 'codeExecution'],
    optimalBenchmarks: ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
    priority: 'medium'
  },
  {
    id: 'testing',
    name: 'Test-Entwicklung',
    description: 'Unit-Tests, Integrationstests, Test-Automatisierung',
    category: 'code-development',
    requiredCapabilities: ['functionCalling', 'reasoning'],
    optimalBenchmarks: ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
    priority: 'medium'
  },
  {
    id: 'api-integration',
    name: 'API-Integration',
    description: 'Integration externer Services, Microservice-Entwicklung',
    category: 'code-development',
    requiredCapabilities: ['functionCalling', 'webBrowsing'],
    optimalBenchmarks: ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'ComplexFuncBench', 'TAU-bench Retail', 'TAU-bench Airline'],
    priority: 'medium'
  },
  {
    id: 'data-analysis',
    name: 'Datenanalyse',
    description: 'Datenverarbeitung, Business Intelligence, Reporting',
    category: 'data-processing',
    requiredCapabilities: ['codeExecution', 'vision', 'reasoning'],
    optimalBenchmarks: ['MATH', 'MMMU', 'MathVista'],
    priority: 'medium'
  },
  {
    id: 'devops-automation',
    name: 'DevOps & Automatisierung',
    description: 'CI/CD-Pipelines, Infrastructure-as-Code, Deployment-Automation',
    category: 'automation',
    requiredCapabilities: ['reasoning', 'codeExecution'],
    optimalBenchmarks: ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
    priority: 'low'
  },
  {
    id: 'learning-support',
    name: 'Lernen & Schulung',
    description: 'Code-Erklärungen, Tutorials, Team-Onboarding',
    category: 'learning',
    requiredCapabilities: ['multilingualSupport', 'reasoning'],
    optimalBenchmarks: ['MMLU', 'IFEval'],
    priority: 'low'
  }
];