import type { ModelCard } from '@/types/model-cards';

// Import der Benchmark-Mappings aus model-recommendations
export const USE_CASE_BENCHMARK_MAPPING: { [key: string]: string[] } = {
  'code-generation': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
  'code-review': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
  'debugging': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
  'documentation': ['LiveCodeBench v2025', 'MMLU', 'IFEval', 'MultiChallenge','Graphwalks BFS <128k'],
  'refactoring': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
  'testing': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
  'api-integration': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'ComplexFuncBench', 'TAU-bench Retail', 'TAU-bench Airline', 'Terminal-bench'],
  'data-analysis': ['MATH', 'MMMU', 'MathVista', 'LiveCodeBench v2025'],
  'devops-automation': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench', 'TAU-bench Retail', 'TAU-bench Airline'],
  'learning-support': ['MMLU', 'IFEval', 'MMLU']
};

export const CRITICAL_CODING_BENCHMARKS = [
  'SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'
];

export interface BenchmarkAnalysis {
  benchmarkName: string;
  category: string;
  totalModels: number;
  modelsWithData: number;
  coverage: number; // Prozentsatz
  averageScore: number;
  minScore: number;
  maxScore: number;
  relevantUseCases: string[];
  isCritical: boolean;
  scoreDistribution: {
    excellent: number; // >= 80
    good: number;      // 60-79
    fair: number;      // 40-59
    poor: number;      // < 40
  };
}

export interface ModelBenchmarkData {
  modelId: string;
  displayName: string;
  provider: string;
  benchmarkScores: { [benchmarkName: string]: { score: number; metric: string; category: string } };
  totalBenchmarks: number;
  averageScore: number;
  coveragePercentage: number;
}

// Analysiert alle Benchmarks und gibt detaillierte Statistiken zurück
export const analyzeBenchmarks = (modelCards: ModelCard[]): BenchmarkAnalysis[] => {
  const benchmarkMap = new Map<string, {
    category: string;
    scores: number[];
    relevantUseCases: string[];
    isCritical: boolean;
  }>();

  // Sammle alle Benchmark-Daten
  modelCards.forEach(card => {
    if (card.benchmarks) {
      card.benchmarks.forEach(benchmark => {
        if (!benchmarkMap.has(benchmark.benchmarkName)) {
          // Finde relevante Use Cases
          const relevantUseCases: string[] = [];
          Object.entries(USE_CASE_BENCHMARK_MAPPING).forEach(([useCase, benchmarkList]) => {
            if (benchmarkList.some(b => 
              benchmark.benchmarkName.toLowerCase().includes(b.toLowerCase()) ||
              benchmark.category.toLowerCase().includes(b.toLowerCase())
            )) {
              relevantUseCases.push(useCase);
            }
          });

          benchmarkMap.set(benchmark.benchmarkName, {
            category: benchmark.category,
            scores: [],
            relevantUseCases,
            isCritical: CRITICAL_CODING_BENCHMARKS.some(cb =>
              benchmark.benchmarkName.toLowerCase().includes(cb.toLowerCase())
            )
          });
        }

        const benchmarkData = benchmarkMap.get(benchmark.benchmarkName);
        if (benchmarkData) {
          benchmarkData.scores.push(benchmark.score);
        }
      });
    }
  });

  // Konvertiere zu BenchmarkAnalysis Array
  return Array.from(benchmarkMap.entries()).map(([benchmarkName, data]) => {
    const scores = data.scores;
    const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const minScore = Math.min(...scores);
    const maxScore = Math.max(...scores);
    
    // Score-Verteilung berechnen
    const scoreDistribution = {
      excellent: scores.filter(s => s >= 80).length,
      good: scores.filter(s => s >= 60 && s < 80).length,
      fair: scores.filter(s => s >= 40 && s < 60).length,
      poor: scores.filter(s => s < 40).length
    };

    return {
      benchmarkName,
      category: data.category,
      totalModels: modelCards.length,
      modelsWithData: scores.length,
      coverage: (scores.length / modelCards.length) * 100,
      averageScore,
      minScore,
      maxScore,
      relevantUseCases: data.relevantUseCases,
      isCritical: data.isCritical,
      scoreDistribution
    };
  }).sort((a, b) => b.coverage - a.coverage); // Sortiere nach Abdeckung
};

// Analysiert Modell-Performance über alle Benchmarks
export const analyzeModelPerformance = (modelCards: ModelCard[]): ModelBenchmarkData[] => {
  return modelCards.map(card => {
    const benchmarkScores: { [benchmarkName: string]: { score: number; metric: string; category: string } } = {};
    let totalScore = 0;
    let benchmarkCount = 0;

    if (card.benchmarks) {
      card.benchmarks.forEach(benchmark => {
        benchmarkScores[benchmark.benchmarkName] = {
          score: benchmark.score,
          metric: benchmark.metric,
          category: benchmark.category
        };
        totalScore += benchmark.score;
        benchmarkCount++;
      });
    }

    return {
      modelId: card.basicInfo.modelId,
      displayName: card.basicInfo.displayName,
      provider: card.basicInfo.provider,
      benchmarkScores,
      totalBenchmarks: benchmarkCount,
      averageScore: benchmarkCount > 0 ? totalScore / benchmarkCount : 0,
      coveragePercentage: benchmarkCount > 0 ? (benchmarkCount / getAllUniqueBenchmarks(modelCards).length) * 100 : 0
    };
  }).sort((a, b) => b.averageScore - a.averageScore);
};

// Hilfsfunktion: Alle einzigartigen Benchmarks extrahieren
export const getAllUniqueBenchmarks = (modelCards: ModelCard[]): string[] => {
  const benchmarks = new Set<string>();
  modelCards.forEach(card => {
    if (card.benchmarks) {
      card.benchmarks.forEach(benchmark => {
        benchmarks.add(benchmark.benchmarkName);
      });
    }
  });
  return Array.from(benchmarks).sort();
};

// Findet Top-Performer für einen spezifischen Benchmark
export const getTopPerformersForBenchmark = (
  modelCards: ModelCard[], 
  benchmarkName: string, 
  limit: number = 5
): { modelId: string; displayName: string; score: number; metric: string }[] => {
  const performers: { modelId: string; displayName: string; score: number; metric: string }[] = [];

  modelCards.forEach(card => {
    if (card.benchmarks) {
      const benchmark = card.benchmarks.find(b => b.benchmarkName === benchmarkName);
      if (benchmark) {
        performers.push({
          modelId: card.basicInfo.modelId,
          displayName: card.basicInfo.displayName,
          score: benchmark.score,
          metric: benchmark.metric
        });
      }
    }
  });

  return performers
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);
};

// Exportiere Use Case Namen für UI
export const getUseCaseDisplayNames = (): { [key: string]: string } => {
  return {
    'code-generation': 'Code-Generierung',
    'code-review': 'Code-Review',
    'debugging': 'Debugging',
    'documentation': 'Dokumentation',
    'refactoring': 'Refactoring',
    'testing': 'Testing',
    'api-integration': 'API-Integration',
    'data-analysis': 'Datenanalyse',
    'devops-automation': 'DevOps-Automatisierung',
    'learning-support': 'Lernunterstützung'
  };
};