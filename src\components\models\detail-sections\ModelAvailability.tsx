import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../../ui/card";
import { Globe, Shield, Server, CheckCircle, AlertCircle } from "lucide-react";
import type { EnrichedModelData } from "../types";
import { useTranslation, t } from "../../../contexts/TranslationContext";
import type { Translations } from "../../../contexts/TranslationContext";

// Define global window interface
declare global {
  interface Window {
    __TRANSLATIONS__?: Translations;
    __CURRENT_LANG__?: string;
  }
}

interface ModelAvailabilityProps {
  model: EnrichedModelData;
}

export function ModelAvailability({ model }: ModelAvailabilityProps) {
  const { translations } = useTranslation();
  const modelCard = model.modelCard;

  // Use global translations as fallback if context translations are empty
  const getTranslation = (key: string, replacements: Record<string, string | number> = {}) => {
    // Check if context translations have data
    if (translations && Object.keys(translations).length > 0) {
      return t(translations, key, replacements);
    }
    
    // Fallback to global translations if available
    if (typeof window !== 'undefined' && window.__TRANSLATIONS__) {
      return t(window.__TRANSLATIONS__ as Translations, key, replacements);
    }
    
    // Last resort - return the key itself
    return key;
  };
  
  if (!modelCard?.availability) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">{getTranslation('models.detail_dialog.availability.no_availability_data')}</h3>
            <p className="text-muted-foreground">
              {getTranslation('models.detail_dialog.availability.no_availability_description')}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const availability = modelCard.availability;

  // Get status color for availability
  const getAvailabilityColor = (status: string) => {
    switch (status) {
      case 'GA':
        return 'bg-green-100 text-green-800';
      case 'Preview':
        return 'bg-yellow-100 text-yellow-800';
      case 'Limited':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Supported Platforms */}
      {availability.supportedPlatforms && availability.supportedPlatforms.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="w-5 h-5 text-blue-600" />
              {getTranslation('models.detail_dialog.availability.supported_platforms')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {availability.supportedPlatforms.map((platform, _index) => (
                <div key={`platform-${platform}`} className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
                  <CheckCircle className="w-4 h-4 text-blue-600 flex-shrink-0" />
                  <span className="text-blue-800 font-medium">{platform}</span>
                </div>
              ))}
            </div>
            
            {/* Platform-specific IDs */}
            {availability.platformSpecificIds && Object.keys(availability.platformSpecificIds).length > 0 && (
              <div className="mt-6">
                <h3 className="font-semibold text-gray-700 mb-3">{getTranslation('models.detail_dialog.availability.platform_specific_ids')}</h3>
                <div className="space-y-2">
                  {Object.entries(availability.platformSpecificIds).map(([platform, id]) => (
                    <div key={platform} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <span className="font-medium text-gray-700">{platform}:</span>
                      <code className="text-sm bg-gray-200 px-2 py-1 rounded">{id}</code>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Regional Availability */}
      {availability.regions && availability.regions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5 text-green-600" />
              {getTranslation('models.detail_dialog.availability.regional_availability')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {availability.regions.map((regionInfo, _index) => (
                <div key={`region-${regionInfo.region}`} className="flex justify-between items-center p-3 border rounded-lg">
                  <div>
                    <span className="font-medium text-foreground">{regionInfo.region}</span>
                    {regionInfo.region === 'global' && (
                      <span className="ml-2 text-sm text-muted-foreground">({getTranslation('models.detail_dialog.availability.global_available')})</span>
                    )}
                  </div>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getAvailabilityColor(regionInfo.availability)}`}>
                    {regionInfo.availability}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Data Processing Regions */}
      {availability.dataProcessingRegions && availability.dataProcessingRegions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-purple-600" />
              {getTranslation('models.detail_dialog.availability.data_processing_regions')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {availability.dataProcessingRegions.map((region, _index) => (
                <span key={`data-region-${region}`} className="bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 px-3 py-1 rounded-full text-sm">
                  {region}
                </span>
              ))}
            </div>
            <p className="text-sm text-muted-foreground mt-3">
              {getTranslation('models.detail_dialog.availability.data_processing_description')}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Security Features */}
      {modelCard.security && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-red-600" />
              {getTranslation('models.detail_dialog.availability.security_features')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {modelCard.security.dataResidency !== undefined && (
                <div className="flex items-center gap-2">
                  <span className={`w-3 h-3 rounded-full ${
                    modelCard.security.dataResidency ? 'bg-green-500' : 'bg-red-500'
                  }`}></span>
                  <span>{getTranslation('models.detail_dialog.availability.data_residency')}</span>
                  <span className={`text-sm ${
                    modelCard.security.dataResidency ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {modelCard.security.dataResidency ? getTranslation('models.detail_dialog.availability.available') : getTranslation('models.detail_dialog.availability.not_available')}
                  </span>
                </div>
              )}
              
              {modelCard.security.cmekSupport !== undefined && (
                <div className="flex items-center gap-2">
                  <span className={`w-3 h-3 rounded-full ${
                    modelCard.security.cmekSupport ? 'bg-green-500' : 'bg-red-500'
                  }`}></span>
                  <span>{getTranslation('models.detail_dialog.availability.cmek_support')}</span>
                  <span className={`text-sm ${
                    modelCard.security.cmekSupport ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {modelCard.security.cmekSupport ? getTranslation('models.detail_dialog.availability.available') : getTranslation('models.detail_dialog.availability.not_available')}
                  </span>
                </div>
              )}
              
              {modelCard.security.vpcSupport !== undefined && (
                <div className="flex items-center gap-2">
                  <span className={`w-3 h-3 rounded-full ${
                    modelCard.security.vpcSupport ? 'bg-green-500' : 'bg-red-500'
                  }`}></span>
                  <span>{getTranslation('models.detail_dialog.availability.vpc_support')}</span>
                  <span className={`text-sm ${
                    modelCard.security.vpcSupport ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {modelCard.security.vpcSupport ? getTranslation('models.detail_dialog.availability.available') : getTranslation('models.detail_dialog.availability.not_available')}
                  </span>
                </div>
              )}
              
              {modelCard.security.accessTransparency !== undefined && (
                <div className="flex items-center gap-2">
                  <span className={`w-3 h-3 rounded-full ${
                    modelCard.security.accessTransparency ? 'bg-green-500' : 'bg-red-500'
                  }`}></span>
                  <span>{getTranslation('models.detail_dialog.availability.access_transparency')}</span>
                  <span className={`text-sm ${
                    modelCard.security.accessTransparency ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {modelCard.security.accessTransparency ? getTranslation('models.detail_dialog.availability.available') : getTranslation('models.detail_dialog.availability.not_available')}
                  </span>
                </div>
              )}
            </div>

            {/* Compliance Standards */}
            {modelCard.security.complianceStandards && modelCard.security.complianceStandards.length > 0 && (
              <div className="mt-6">
                <h3 className="font-semibold text-muted-foreground mb-3">{getTranslation('models.detail_dialog.availability.compliance_standards')}</h3>
                <div className="flex flex-wrap gap-2">
                  {modelCard.security.complianceStandards.map((standard, _index) => (
                    <span key={`compliance-${standard}`} className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-3 py-1 rounded-full text-sm">
                      {standard}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Usage Types */}
      {modelCard.usageTypes && (
        <Card>
          <CardHeader>
            <CardTitle>{getTranslation('models.detail_dialog.availability.usage_types')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {modelCard.usageTypes.dynamicSharedQuota !== undefined && (
                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <span className="font-medium">{getTranslation('models.detail_dialog.availability.dynamic_shared_quota')}</span>
                  <span className={`px-3 py-1 rounded-full text-sm ${
                    modelCard.usageTypes.dynamicSharedQuota
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  }`}>
                    {modelCard.usageTypes.dynamicSharedQuota ? getTranslation('models.detail_dialog.availability.available') : getTranslation('models.detail_dialog.availability.not_available')}
                  </span>
                </div>
              )}
              
              {modelCard.usageTypes.provisionedThroughput !== undefined && (
                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <span className="font-medium">{getTranslation('models.detail_dialog.availability.provisioned_throughput')}</span>
                  <span className={`px-3 py-1 rounded-full text-sm ${
                    modelCard.usageTypes.provisionedThroughput
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  }`}>
                    {modelCard.usageTypes.provisionedThroughput ? getTranslation('models.detail_dialog.availability.available') : getTranslation('models.detail_dialog.availability.not_available')}
                  </span>
                </div>
              )}
              
              {modelCard.usageTypes.fixedQuota !== undefined && (
                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <span className="font-medium">{getTranslation('models.detail_dialog.availability.fixed_quota')}</span>
                  <span className={`px-3 py-1 rounded-full text-sm ${
                    modelCard.usageTypes.fixedQuota
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  }`}>
                    {modelCard.usageTypes.fixedQuota ? getTranslation('models.detail_dialog.availability.available') : getTranslation('models.detail_dialog.availability.not_available')}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Additional Information */}
      <Card>
        <CardHeader>
          <CardTitle>{getTranslation('models.detail_dialog.availability.additional_info')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm text-muted-foreground">
            <p>
              <strong>{getTranslation('models.detail_dialog.availability.availability_note')}</strong>
            </p>
            {modelCard.metadata?.dataSource && (
              <p>
                <strong>{getTranslation('models.detail_dialog.availability.data_source')}:</strong> {modelCard.metadata.dataSource}
              </p>
            )}
            {modelCard.metadata?.lastUpdated && (
              <p>
                <strong>{getTranslation('models.detail_dialog.availability.last_updated')}:</strong> {new Date(modelCard.metadata.lastUpdated).toLocaleDateString()}
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}