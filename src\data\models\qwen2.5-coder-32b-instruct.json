{"basicInfo": {"modelId": "qwen2.5-coder-32b-instruct", "displayName": "Qwen2.5-Coder-32B-Instruct", "provider": "Alibaba", "modelFamily": "<PERSON><PERSON>", "version": "2.5", "description": "Ein spezialisiertes Code-Modell der Qwen2.5-Familie mit 32 Milliarden Parametern, optimiert für Code-Generierung, Code-Verständnis und programmierungsbezogene Aufgaben", "releaseDate": "2024-11-12", "status": "GA", "knowledgeCutoff": "September 2024"}, "technicalSpecs": {"contextWindow": 33000, "maxOutputTokens": 8192, "architecture": "Transformer", "parameterCount": "32B", "supportedInputTypes": ["text"], "supportedOutputTypes": ["text"]}, "capabilities": {"functionCalling": true, "vision": false, "pdfSupport": false, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": false, "batchProcessing": false, "reasoning": true, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": true, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "temperature": {"min": 0, "max": 2, "default": 0.7}, "topP": 0.8, "topK": 20}, "pricing": {"inputCostPer1MTokens": 0.07, "outputCostPer1MTokens": 0.16, "currency": "USD"}, "availability": {"supportedPlatforms": ["Lambda AI Inference"], "platformSpecificIds": {"lambdaAi": "qwen25-coder-32b-instruct"}}, "benchmarks": [{"benchmarkName": "GPQA Diamond", "category": "Science", "score": 46.0, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Graduate-level reasoning in science"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 31.4, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 16.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Aider polyglot benchmark with whole edit format"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 99.6, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 902.25, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #31"}], "metadata": {"lastUpdated": "2025-06-08T21:06:00Z", "dataSource": "<PERSON><PERSON>, Benchmark Results, Lambda AI Documentation", "version": "1.0"}}