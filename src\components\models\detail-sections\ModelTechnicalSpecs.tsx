import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../../ui/card";
import type { EnrichedModelData } from "../types";
import { useTranslation, t } from "../../../contexts/TranslationContext";
import type { Translations } from "../../../contexts/TranslationContext";

// Define global window interface
declare global {
  interface Window {
    __TRANSLATIONS__?: Translations;
    __CURRENT_LANG__?: string;
  }
}

interface ModelTechnicalSpecsProps {
  model: EnrichedModelData;
}

export function ModelTechnicalSpecs({ model }: ModelTechnicalSpecsProps) {
  const { translations } = useTranslation();
  const modelCard = model.modelCard;

  // Use global translations as fallback if context translations are empty
  const getTranslation = (key: string, replacements: Record<string, string | number> = {}) => {
    // Check if context translations have data
    if (translations && Object.keys(translations).length > 0) {
      return t(translations, key, replacements);
    }
    
    // Fallback to global translations if available
    if (typeof window !== 'undefined' && window.__TRANSLATIONS__) {
      return t(window.__TRANSLATIONS__ as Translations, key, replacements);
    }
    
    // Last resort - return the key itself
    return key;
  };

  // Helper function to format large numbers
  const formatNumber = (value: number | undefined | null) => {
    if (value == null) return "N/A";
    return value.toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Token Limits */}
      <Card>
        <CardHeader>
          <CardTitle>{getTranslation('models.detail_dialog.technical_specs.token_limits')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.technical_specs.context_window')}</h3>
              <p className="text-lg">
                {formatNumber(modelCard?.technicalSpecs.contextWindow || model.contextWindow)} Tokens
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.technical_specs.max_output_tokens')}</h3>
              <p className="text-lg">
                {formatNumber(modelCard?.technicalSpecs.maxOutputTokens || model.maxOutputTokens || model.maxTokens)}
              </p>
            </div>
            {model.maxInputTokens && (
              <div>
                <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.technical_specs.max_input_tokens')}</h3>
                <p className="text-lg">{formatNumber(model.maxInputTokens)}</p>
              </div>
            )}
            {modelCard?.technicalSpecs && 'maxReasoningTokens' in modelCard.technicalSpecs && (
              <div>
                <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.technical_specs.max_reasoning_tokens')}</h3>
                <p className="text-lg">{formatNumber(modelCard.technicalSpecs.maxReasoningTokens ?? 0)}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Architecture & Parameters */}
      {modelCard?.technicalSpecs && (
        <Card>
          <CardHeader>
            <CardTitle>{getTranslation('models.detail_dialog.technical_specs.architecture_params')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {modelCard.technicalSpecs.architecture && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.technical_specs.architecture')}</h3>
                  <p className="text-lg">{modelCard.technicalSpecs.architecture}</p>
                </div>
              )}
              {modelCard.technicalSpecs.parameterCount && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.technical_specs.parameter_count')}</h3>
                  <p className="text-lg">{modelCard.technicalSpecs.parameterCount}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Input/Output Types */}
      {modelCard?.technicalSpecs && (
        <Card>
          <CardHeader>
            <CardTitle>{getTranslation('models.detail_dialog.technical_specs.supported_modalities')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-muted-foreground mb-3">{getTranslation('models.detail_dialog.technical_specs.input_types')}</h3>
                <div className="flex flex-wrap gap-2">
                  {modelCard.technicalSpecs.supportedInputTypes.map((type, _index) => (
                    <span key={`input-${type}`} className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-3 py-1 rounded-full text-sm">
                      {type}
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-muted-foreground mb-3">{getTranslation('models.detail_dialog.technical_specs.output_types')}</h3>
                <div className="flex flex-wrap gap-2">
                  {modelCard.technicalSpecs.supportedOutputTypes.map((type, _index) => (
                    <span key={`output-${type}`} className="bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 px-3 py-1 rounded-full text-sm">
                      {type}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Input Limitations */}
      {modelCard?.technicalSpecs.inputLimitations && (
        <Card>
          <CardHeader>
            <CardTitle>{getTranslation('models.detail_dialog.technical_specs.input_limitations')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {modelCard.technicalSpecs.inputLimitations.maxImages && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.technical_specs.max_images')}</h3>
                  <p className="text-lg">{modelCard.technicalSpecs.inputLimitations.maxImages}</p>
                </div>
              )}
              {modelCard.technicalSpecs.inputLimitations.maxImageSize && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.technical_specs.max_image_size')}</h3>
                  <p className="text-lg">{modelCard.technicalSpecs.inputLimitations.maxImageSize}</p>
                </div>
              )}
              {modelCard.technicalSpecs.inputLimitations.maxAudioLength && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.technical_specs.max_audio_length')}</h3>
                  <p className="text-lg">{modelCard.technicalSpecs.inputLimitations.maxAudioLength}</p>
                </div>
              )}
              {modelCard.technicalSpecs.inputLimitations.maxVideoLength && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.technical_specs.max_video_length')}</h3>
                  <p className="text-lg">{modelCard.technicalSpecs.inputLimitations.maxVideoLength}</p>
                </div>
              )}
            </div>

            {/* MIME Types */}
            {modelCard.technicalSpecs.inputLimitations.supportedMimeTypes && (
              <div className="mt-6">
                <h3 className="font-semibold text-muted-foreground mb-3">{getTranslation('models.detail_dialog.technical_specs.supported_mime_types')}</h3>
                <div className="space-y-3">
                  {Object.entries(modelCard.technicalSpecs.inputLimitations.supportedMimeTypes).map(([category, types]) => (
                    <div key={category}>
                      <h4 className="text-sm font-medium text-muted-foreground capitalize mb-1">{category}:</h4>
                      <div className="flex flex-wrap gap-1">
                        {types.map((type, _index) => (
                          <span key={`type-${type}`} className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded">
                            {type}
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Legacy Data from API */}
      {!modelCard && (
        <Card>
          <CardHeader>
            <CardTitle>{getTranslation('models.detail_dialog.technical_specs.legacy_api_data')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {model.supportedModalities && (
                <div>
                  <h3 className="font-semibold text-muted-foreground mb-2">{getTranslation('models.detail_dialog.technical_specs.modalities')}</h3>
                  <div className="flex flex-wrap gap-1">
                    {model.supportedModalities.map((modality, _index) => (
                      <span key={`modality-${modality}`} className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 px-2 py-1 rounded text-sm">
                        {modality}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              {model.supportedOutputModalities && (
                <div>
                  <h3 className="font-semibold text-muted-foreground mb-2">{getTranslation('models.detail_dialog.technical_specs.output_modalities')}</h3>
                  <div className="flex flex-wrap gap-1">
                    {model.supportedOutputModalities.map((modality, _index) => (
                      <span key={`output-modality-${modality}`} className="bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 px-2 py-1 rounded text-sm">
                        {modality}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              {model.output_vector_size && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">{getTranslation('models.detail_dialog.technical_specs.vector_size')}</h3>
                  <p className="text-lg">{formatNumber(model.output_vector_size)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}