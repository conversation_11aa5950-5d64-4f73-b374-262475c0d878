[{"model": "gemini/gemini-2.0-pro-exp-02-05", "modelid": "gemini-2.0-pro-exp-02-05", "pass_rate_2": 35.6, "percent_cases_well_formed": 100.0, "total_cost": 0.0, "command": "aider --model gemini/gemini-2.0-pro-exp-02-05", "edit_format": "whole", "details": {"dirname": "2025-02-25-20-23-07--gemini-pro", "test_cases": 225, "commit_hash": "2fccd47", "pass_rate_1": 20.4, "pass_num_1": 46, "pass_num_2": 80, "error_outputs": 430, "num_malformed_responses": 0, "num_with_malformed_responses": 0, "user_asks": 13, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 5, "total_tests": 225, "date": "2025-02-25", "versions": "0.75.2.dev", "seconds_per_case": 34.8}}, {"model": "gpt-4o-mini-2024-07-18", "modelid": "gpt-4o-mini-2024-07-18", "pass_rate_2": 3.6, "percent_cases_well_formed": 100.0, "total_cost": 0.3236, "command": "aider --model gpt-4o-mini-2024-07-18", "edit_format": "whole", "details": {"dirname": "2024-12-21-18-41-18--polyglot-gpt-4o-mini", "test_cases": 225, "commit_hash": "a755079-dirty", "pass_rate_1": 0.9, "pass_num_1": 2, "pass_num_2": 8, "error_outputs": 0, "num_malformed_responses": 0, "num_with_malformed_responses": 0, "user_asks": 36, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 3, "total_tests": 225, "date": "2024-12-21", "versions": "0.69.2.dev", "seconds_per_case": 17.3}}, {"model": "claude-3-5-sonnet-20241022", "modelid": "claude-3-5-sonnet-20241022", "pass_rate_2": 51.6, "percent_cases_well_formed": 99.6, "total_cost": 14.4063, "command": "aider --model claude-3-5-sonnet-20241022", "edit_format": "diff", "details": {"dirname": "2025-01-17-19-44-33--sonnet-baseline-jan-17", "test_cases": 225, "commit_hash": "6451d59", "pass_rate_1": 22.2, "pass_num_1": 50, "pass_num_2": 116, "error_outputs": 2, "num_malformed_responses": 1, "num_with_malformed_responses": 1, "user_asks": 11, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 8, "total_tests": 225, "date": "2025-01-17", "versions": "0.71.2.dev", "seconds_per_case": 21.4}}, {"model": "gpt-4o-2024-11-20", "modelid": "gpt-4o-2024-11-20", "pass_rate_2": 18.2, "percent_cases_well_formed": 95.1, "total_cost": 6.7351, "command": "aider --model gpt-4o-2024-11-20", "edit_format": "diff", "details": {"dirname": "2024-12-30-20-57-12--gpt-4o-2024-11-20-ex-as-sys", "test_cases": 225, "commit_hash": "09ee197-dirty", "pass_rate_1": 4.9, "pass_num_1": 11, "pass_num_2": 41, "error_outputs": 12, "num_malformed_responses": 12, "num_with_malformed_responses": 11, "user_asks": 53, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 12, "total_tests": 225, "date": "2024-12-30", "versions": "0.70.1.dev", "seconds_per_case": 12.1}}, {"model": "gpt-4o-2024-08-06", "modelid": "gpt-4o-2024-08-06", "pass_rate_2": 23.1, "percent_cases_well_formed": 94.2, "total_cost": 7.0286, "command": "aider --model gpt-4o-2024-08-06", "edit_format": "diff", "details": {"dirname": "2024-12-30-20-44-54--gpt4o-ex-as-sys-clean-prompt", "test_cases": 225, "commit_hash": "09ee197-dirty", "pass_rate_1": 4.9, "pass_num_1": 11, "pass_num_2": 52, "error_outputs": 21, "num_malformed_responses": 21, "num_with_malformed_responses": 13, "user_asks": 65, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 3, "total_tests": 225, "date": "2024-12-30", "versions": "0.70.1.dev", "seconds_per_case": 16.0}}, {"model": "DeepSeek Chat V2.5", "modelid": "deepseek-chat-v2.5", "pass_rate_2": 17.8, "percent_cases_well_formed": 92.9, "total_cost": 0.5101, "command": "aider --model deepseek/deepseek-chat", "edit_format": "diff", "details": {"dirname": "2024-12-21-20-56-21--polyglot-deepseek-diff", "test_cases": 225, "commit_hash": "a755079-dirty", "pass_rate_1": 5.3, "pass_num_1": 12, "pass_num_2": 40, "error_outputs": 42, "num_malformed_responses": 37, "num_with_malformed_responses": 16, "user_asks": 23, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 5, "test_timeouts": 5, "total_tests": 225, "date": "2024-12-21", "versions": "0.69.2.dev", "seconds_per_case": 184.0}}, {"model": "claude-3-5-haiku-20241022", "modelid": "claude-3-5-haiku-20241022", "pass_rate_2": 28.0, "percent_cases_well_formed": 91.1, "total_cost": 6.0583, "command": "aider --model claude-3-5-haiku-20241022", "edit_format": "diff", "details": {"dirname": "2024-12-21-21-46-27--polyglot-haiku-diff", "test_cases": 225, "commit_hash": "a755079-dirty", "pass_rate_1": 7.1, "pass_num_1": 16, "pass_num_2": 63, "error_outputs": 31, "num_malformed_responses": 30, "num_with_malformed_responses": 20, "user_asks": 13, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 9, "total_tests": 225, "date": "2024-12-21", "versions": "0.69.2.dev", "seconds_per_case": 31.8}}, {"model": "Qwen2.5-Coder-32B-Instruct", "modelid": "qwen2.5-coder-32b-instruct", "pass_rate_2": 8.0, "percent_cases_well_formed": 71.6, "total_cost": 0.0, "command": "aider --model openai/Qwen/Qwen2.5-Coder-32B-Instruct # via hyperbolic", "edit_format": "diff", "details": {"dirname": "2024-12-22-13-22-32--polyglot-qwen-diff", "test_cases": 225, "commit_hash": "6d7e8be-dirty", "pass_rate_1": 4.4, "pass_num_1": 10, "pass_num_2": 18, "error_outputs": 158, "num_malformed_responses": 148, "num_with_malformed_responses": 64, "user_asks": 132, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 2, "total_tests": 225, "date": "2024-12-22", "versions": "0.69.2.dev", "seconds_per_case": 84.4}}, {"model": "o1-mini-2024-09-12", "modelid": "o1-mini-2024-09-12", "pass_rate_2": 32.9, "percent_cases_well_formed": 96.9, "total_cost": 18.577, "command": "aider --model o1-mini", "edit_format": "whole", "details": {"dirname": "2024-12-22-21-26-35--polyglot-o1mini-whole", "test_cases": 225, "commit_hash": "37df899", "pass_rate_1": 5.8, "pass_num_1": 13, "pass_num_2": 74, "error_outputs": 8, "num_malformed_responses": 8, "num_with_malformed_responses": 7, "user_asks": 27, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 3, "total_tests": 225, "date": "2024-12-22", "versions": "0.69.2.dev", "seconds_per_case": 34.7}}, {"model": "gemini-exp-1206", "modelid": "gemini-exp-1206", "pass_rate_2": 38.2, "percent_cases_well_formed": 98.2, "total_cost": 0.0, "command": "aider --model gemini/gemini-exp-1206", "edit_format": "whole", "details": {"dirname": "2024-12-22-18-43-25--gemini-exp-1206-polyglot-whole-2", "test_cases": 225, "commit_hash": "b1bc2f8", "pass_rate_1": 19.6, "pass_num_1": 44, "pass_num_2": 86, "error_outputs": 8, "num_malformed_responses": 8, "num_with_malformed_responses": 4, "user_asks": 32, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 9, "total_tests": 225, "date": "2024-12-22", "versions": "0.69.2.dev", "seconds_per_case": 45.5}}, {"model": "gemini-2.0-flash-exp", "modelid": "gemini-2.0-flash-exp", "pass_rate_2": 22.2, "percent_cases_well_formed": 100.0, "total_cost": 0.0, "command": "aider --model gemini/gemini-2.0-flash-exp", "edit_format": "whole", "details": {"dirname": "2024-12-22-20-08-13--gemini-2.0-flash-exp-polyglot-whole", "test_cases": 225, "commit_hash": "b1bc2f8", "pass_rate_1": 11.6, "pass_num_1": 26, "pass_num_2": 50, "error_outputs": 1, "num_malformed_responses": 0, "num_with_malformed_responses": 0, "user_asks": 9, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 8, "total_tests": 225, "date": "2024-12-22", "versions": "0.69.2.dev", "seconds_per_case": 12.2}}]