{"basicInfo": {"modelId": "gpt-4.1-nano-2025-04-14", "displayName": "GPT-4.1 nano", "provider": "OpenAI", "modelFamily": "GPT", "version": "2025-04-14", "description": "Fastest, most cost-effective GPT-4.1 model", "releaseDate": "2025-04-14", "status": "GA", "knowledgeCutoff": "Jun 01, 2024"}, "technicalSpecs": {"contextWindow": 1047576, "maxOutputTokens": 32768, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": true, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": false, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": false, "codeInterpreter": true, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fastest", "rateLimits": {"queriesPerMinute": 500, "tokensPerMinute": 200000, "batchQueueLimit": 2000000}, "temperature": {"min": 0, "max": 2, "default": 1.0}}, "pricing": {"inputCostPer1MTokens": 0.1, "outputCostPer1MTokens": 0.4, "cachingCosts": {"cacheHits": 0.025}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "OpenAI Batch API"]}, "benchmarks": [{"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 29.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "American Invitational Mathematics Examination"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 50.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Graduate-level reasoning in science"}, {"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 80.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Massive Multitask Language Understanding"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 66.9, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multilingual Q&A across 14 languages"}, {"benchmarkName": "Internal API instruction following (hard)", "category": "Instruction following", "score": 31.6, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Hard subset of instruction following eval"}, {"benchmarkName": "MultiChallenge", "category": "Instruction following", "score": 15.0, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multi-turn instruction following"}, {"benchmarkName": "IFEval", "category": "Instruction following", "score": 74.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Instruction following with verifiable instructions"}, {"benchmarkName": "Multi-IF", "category": "Instruction following", "score": 57.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multi-instruction following benchmark"}, {"benchmarkName": "Graphwalks BFS <128k accuracy", "category": "Long context", "score": 25.0, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multi-round co-reference resolution in langen Kontexten"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 55.4, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multimodal understanding and reasoning"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 56.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Visual mathematical tasks"}, {"benchmarkName": "CharXiv", "category": "Visual reasoning", "score": 40.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Questions about charts from scientific papers"}, {"benchmarkName": "ComplexFuncBench", "category": "Function calling", "score": 5.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Complex function calling benchmark"}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic coding", "score": 14.0, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Agentic tool use in airline scenarios"}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic coding", "score": 22.6, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Agentic tool use in retail scenarios"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 9.8, "alternativeScores": {"diff": 6.2}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Aider polyglot benchmark - whole: 9.8%, diff: 6.2%"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 94.2, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 23.8, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 1.5, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "GPT-4.1 nano score estimated based on positioning relative to other GPT models"}], "metadata": {"lastUpdated": "2025-06-09T12:31:00Z", "dataSource": "OpenAI Platform Documentation", "version": "1.1"}}