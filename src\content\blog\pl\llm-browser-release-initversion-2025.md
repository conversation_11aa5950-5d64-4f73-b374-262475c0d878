---
title: "LLM Browser: Wię<PERSON><PERSON> przejrzystości i porównywalności"
excerpt: "Powstały z demo Vise-Coding dla _NEXT25, p<PERSON><PERSON><PERSON> został dalej rozwin<PERSON>ty w ramach FreiDay, aby <PERSON><PERSON><PERSON><PERSON><PERSON> więcej prz<PERSON>, przeglądu i porównywalności dotyczącej aktualnych rozwojów w modelach AI"
category: "model-analysis"
tags: ["launch", "model-comparison", "benchmarks", "astro", "react", "performance"]
publishDate: "2025-06-11T09:00:00Z"
lastUpdated: "2025-06-11T09:00:00Z"
author:
  name: "Zespół Rozwoju LLM Browser"
  role: "Product & Engineering"
readingTime: 8
featured: true
relatedModelIds: ["o3", "claude-opus-4", "gemini-2.5-pro", "deepseek-v3", "deepseek-r1"]
releaseVersion: "0.1"

# pola specyficzne dla i18n
lang: "pl"
translationKey: "llm-browser-release-initversion-2025"
availableLanguages: ["de", "en", "pl"]
changelog:
  - type: "removed"
    description: "Benchmarki Aider Polyglot nie są już tak mocno wyeksponowane i dlatego usunięte z nawigacji"
    impact: "patch"
    technicalDetails: "Są nadal dostępne przez linki benchmarku Aider Polyglot lub bezpośrednio przez /benchmark"
  - type: "added"
    description: "Kompletna platforma porównywania modeli ze wszystkimi aktualnymi istotnymi modelami LLM od różnych dostawców hostingu"
    impact: "major"
    technicalDetails: "Generowanie kart modeli wspomagane przez AI (jeszcze nie w 100% zautomatyzowane)"
  - type: "added"
    description: "Skonsolidowana kolekcja benchmarków w mieszance dostępności i wartości dodanej"
    impact: "major"
    technicalDetails: "Aider-Polyglot Leaderboard, MMLU, MATH, Code Generation, Visual Reasoning i więcej"
  - type: "added"
    description: "System rekomendacji do wyboru modelu w różnych scenariuszach w 1. iteracji"
    impact: "major"
    technicalDetails: "Rekomendacje oparte na przypadkach użycia z analizą kosztów i korzyści, jednak nadal ograniczenia ze względu na bazę danych, która nadal wymaga weryfikacji"
  - type: "added"
    description: "Wprowadzenie kart modeli z kategoriami informacji na model"
    impact: "major"
    technicalDetails: "Ogólne, Techniczne, Modalności, Benchmarki, Możliwości, Ceny, Dostępność"
  - type: "added"
    description: "System kontroli jakości do walidacji danych i spójności przez stronę benchmarków"
    impact: "minor"
    technicalDetails: "Zautomatyzowana kontrola jakości z wizualną reprezentacją statystyk"
metaDescription: "Nowy LLM Browser - kompleksowa platforma do analizy i porównywania 38 modeli AI z ponad 55 kategoriami benchmarków"
metaKeywords: ["LLM Browser", "Modele AI", "Benchmarki", "Porównanie modeli", "Analiza wydajności"]
featuredImage: "/images/blog/2025-06-1st-release.png"
---

LLM Browser nadal jest projektem FreiDay, aby nauczyć się pracy z Vise-Coding, jednocześnie generując wartość dodaną w użytkowaniu, planowaniu i weryfikacji modeli AI w iteratec.

**LLM Browser jest obecnie nieoficjalny!** Celem jest, aby ta konfiguracja w tej lub zredukowanej formie została przejęta przez zespół ExG-R&D.

## Motywacja

Pomysłem było uzyskanie lepszego przeglądu szybko rozwijającego się krajobrazu LLM. Z obecną konfiguracją dostępnych jest **20 różnych modeli od 7 wiodących dostawców** połączonych z kartami modeli i podstawowymi informacjami. Od najnowszych modeli OpenAI o3 i o4-Mini przez Claude Opus 4 od Anthropic po Gemini 2.5 Pro od Google i imponujące modele DeepSeek - wszyscy główni gracze są reprezentowani.

Głównym wyzwaniem dzisiaj jest to, że baza danych, szczególnie dotycząca benchmarków modeli, jest bardzo rozproszona i niejednorodna, co sprawia, że proste porównanie jest prawie niemożliwe. Harmonizacja z bazą danych tworzy więc również warunki wstępne do budowania konkretnych rekomendacji dla modeli AI w różnych przypadkach użycia. Oparte na danych, nie na odczuciach.

## Podstawy danych

Pokazane informacje są oparte na danych LiteLLM dotyczących kosztów i obsługiwanych funkcji, tj. proxy LLM, który stoi za api.iteragpt.com.

Karty modeli i wartości benchmarków są natomiast prowadzone przez AI i ręcznie kuratorowane.

Czasami benchmarki są prowadzone TYLKO na specjalnych stronach, czasami również dostarczane tylko jako materiały obrazowe w porównaniu i obecnie praktycznie nigdy nie są naprawdę łatwo dostępne.

Karty modeli są tworzone wspomagane przez AI i ręcznie kuratorowane. Całkowicie automatyczny sposób byłby możliwy do pomyślenia, ale wysiłek nie pokryłby obecnie korzyści.

Schematyczny przepływ pracy dla karty modelu o3-pro:

```
Utwórz nową kartę modelu dla "o3-pro" pod src/data/models/o3-pro.json z podstawową strukturą opartą na schemacie pod @/src/data/models/model-card-json-schema.md. Zwróć uwagę na dokładną pisownię benchmarków z @/src/data/benchmarks/benchmark-descriptions.json! W przeciwnym razie dopasowanie nie jest możliwe! Upewnij się również, że koszty są poprawnie przejęte zgodnie ze schematem!

**Krok 1:**
Podstawowe informacje karty modelu:
- @https://platform.openai.com/docs/models/o3-pro
- @https://openai.com/index/introducing-o3-and-o4-mini/

Użyj jako przykład referencyjny: 
<example>
@/src/data/models/claude-sonnet-4.json 
</example>

**Krok 2:**
Sprawdź, czy dodatkowe dane benchmarków mogą być zaktualizowane.

- Benchmark: "AIME": @https://www.vals.ai/benchmarks/aime-2025-05-30 
- Benchmark: "MMMU": @https://www.vals.ai/benchmarks/mmmu-05-30-2025 
- Benchmark: "SWE-bench Verified": @https://www.swebench.com/index.html 
- Benchmark: "Terminal-Bench": @https://www.tbench.ai/leaderboard 
- Benchmark: "Webdev-Arena": @https://web.lmarena.ai/leaderboard 
- Benchmark: "GPQA-Diamond": @https://www.vellum.ai/llm-leaderboard 
- Benchmark: "LiveCodeBench v2025": @https://livecodebench.github.io/leaderboard.html 
- Benchmark: "Humanity's Last Exam": @https://scale.com/leaderboard/humanitys_last_exam_text_only

Zwróć uwagę na dokładną pisownię benchmarków z @/src/data/benchmarks/benchmark-descriptions.json! W przeciwnym razie dopasowanie nie jest możliwe!

**Krok 3:**
Następnie zaktualizuj plik mapowania @/src/data/models/model-mappings.json na podstawie istniejącego @/src/data/models/model-ids-reference.md.

**Krok 4:**
Zaktualizuj również benchmarki Polyglot odpowiednio @/src/data/benchmarks/benchmark-descriptions.json na podstawie @/src/data/polyglot_benchmarks.json w @/src/data/models/grok-v3.json. Upewnij się, że znajdziesz i napiszesz następujące wartości:

- "Aider-Polyglot-Wellformated"
- "Aider-Polyglot"
```

## Spektrum benchmarków

Następujące benchmarki są zintegrowane i dostępne z dalszymi szczegółami:

### Rozumowanie i wiedza
- **MMLU** (Massive Multitask Language Understanding)
- **DROP** (Discrete Reasoning Over Paragraphs)
- **BIG-Bench-Hard** dla złożonych zadań myślowych
- **Humanity's Last Exam** - ostateczny test wiedzy

### Doskonałość kodu
- **Aider-Polyglot Leaderboard** - nasz flagowiec do edycji kodu
- **LiveCodeBench v2025** do generowania kodu
- **SWE-bench Verified** do inżynierii oprogramowania
- **Terminal-bench** i **TAU-bench** do kodowania agentowego

### Matematyka i nauka
- **MATH** do rozwiązywania problemów matematycznych
- **AIME 2024/2025** do zaawansowanej matematyki
- **GPQA Diamond** do ekspertyzy naukowej

### Możliwości multimodalne
- **MathVista** do wizualnego zrozumienia matematycznego
- **CharXiv-Reasoning** do interpretacji diagramów
- **MMMU** do zrozumienia multimodalnego

## Rekomendacje dla każdego przypadku użycia

**System rekomendacji** ma pomagać w analizie konkretnych wymagań i sugeruje optymalne modele.

Rekomendacje składają się z różnych obszarów, które są ważone, aby dać ogólny wynik. Odpowiednie benchmarki i możliwości różnią się w zależności od przypadku użycia, np. edycja kodu lub dokumentacja.

![Przegląd systemu punktacji](/images/blog/2025-06-scoring.png)

Obecnie baza danych jest nadal w budowie i nie jest finalna.
Obecne rekomendacje są jednak zgodne z tymi z ExG w obecnym czasie.

## Dalszy rozwój

Jeśli masz życzenia dotyczące dalszego rozwoju, po prostu daj nam znać!

**Przydatne linki:**
- **[Repozytorium GitLab](https://gitlab.com/iteratec/llm-browser)** - Kod źródłowy