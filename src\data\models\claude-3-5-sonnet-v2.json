{"basicInfo": {"modelId": "claude-3-5-sonnet-v2@20241022", "displayName": "Claude 3.5 Sonnet v2", "provider": "Anthropic", "modelFamily": "<PERSON>", "version": "20241022", "description": "Ein hochmodernes Modell für reale Softwareentwicklungsaufgaben und für von KI-Agenten zu übernehmende Aufgaben. Setzt neue Industriestandards für Intelligenz mit 2x der Geschwindigkeit von Claude 3 Opus.", "releaseDate": "2024-10-22", "status": "Deprecated", "knowledgeCutoff": "August 2024", "deprecationDate": "2025-06-20", "shutdownDate": "2025-06-20"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 8000, "supportedInputTypes": ["text", "image", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 90, "tokensPerMinute": 540000}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 3.0, "outputCostPer1MTokens": 15.0, "cachingCosts": {"cacheWrites": 3.75, "cacheHits": 0.3}, "currency": "USD"}, "availability": {"regions": [{"region": "us-east5", "availability": "GA"}, {"region": "europe-west1", "availability": "GA"}, {"region": "global", "availability": "GA"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["Vertex AI", "AWS Bedrock", "Anthropic API"]}, "security": {"dataResidency": true, "cmekSupport": true, "vpcSupport": true, "accessTransparency": true, "complianceStandards": ["SOC2", "GDPR"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": true, "fixedQuota": true}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 49.0, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Software engineering benchmark for real-world coding tasks"}, {"benchmarkName": "GPQA Diamond", "category": "Reasoning & Knowledge", "score": 59.4, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-06-21", "notes": "0-shot CoT"}, {"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 88.7, "alternativeScores": {"average": 88.3}, "metric": "Accuracy", "attemptType": "multiple attempts", "date": "2024-06-21", "notes": "5-shot vs 0-shot CoT"}, {"benchmarkName": "MGSM", "category": "Mathematics", "score": 91.6, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-06-21", "notes": "0-shot CoT"}, {"benchmarkName": "DROP", "category": "Reasoning & Knowledge", "score": 87.1, "metric": "F1 Score", "attemptType": "multiple attempts", "date": "2024-06-21", "notes": "3-shot"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 78.0, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-15", "notes": "MATH 500 benchmark - updated score"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 51.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-17", "notes": "Aider-Polyglot benchmark with diff edit format"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 99.6, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-01-17", "notes": "Percentage of well-formed responses in Aider-Polyglot benchmark"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 37.2, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1150.0, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #9"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 4.08, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Claude 3.5 Sonnet (October 2024): 4.08±0.78"}], "metadata": {"lastUpdated": "2025-06-09T18:22:00Z", "dataSource": "Google Cloud Vertex AI Documentation, Anthropic Documentation, SWE-bench Verified Benchmark, MATH 500 Benchmark Update", "version": "1.2"}}