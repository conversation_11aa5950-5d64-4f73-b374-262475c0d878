import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "../ui/dialog";
import { Button } from "../ui/button";
import { Calculator } from "lucide-react";
import { useTranslation, t } from "../../contexts/TranslationContext";
import type { Translations } from "../../contexts/TranslationContext";

// Define global window interface
declare global {
  interface Window {
    __TRANSLATIONS__?: Translations;
    __CURRENT_LANG__?: string;
  }
}

interface CalculationMethodologyProps {
  trigger?: React.ReactNode;
}

export const CalculationMethodology: React.FC<CalculationMethodologyProps> = ({
  trigger,
}) => {
  const { translations } = useTranslation();

  // Use global translations as fallback if context translations are empty
  const getTranslation = (key: string, replacements: Record<string, string | number> = {}) => {
    // Check if context translations have data
    if (translations && Object.keys(translations).length > 0) {
      return t(translations, key, replacements);
    }
    
    // Fallback to global translations if available
    if (typeof window !== 'undefined' && window.__TRANSLATIONS__) {
      return t(window.__TRANSLATIONS__ as Translations, key, replacements);
    }
    
    // Last resort - return the key itself
    return key;
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="gap-2">
      <Calculator className="w-4 h-4" />
      {getTranslation('calculation_methodology.title')}
    </Button>
  );

  return (
    <Dialog>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-foreground flex items-center gap-2">
            <Calculator className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            {getTranslation('calculation_methodology.dialog_title')}
          </DialogTitle>
          <DialogDescription>
            {getTranslation('calculation_methodology.dialog_description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 mt-6">
          {/* Übersicht des Scoring-Algorithmus */}
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
              <span className="text-2xl mr-2">🎯</span>
              {getTranslation('calculation_methodology.scoring_overview')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {getTranslation('calculation_methodology.scoring_description')}
            </p>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  60%
                </div>
                <div className="text-sm text-muted-foreground">
                  {getTranslation('calculation_methodology.benchmark_performance')}
                </div>
              </div>
              <div className="text-center p-4 bg-green-50 dark:bg-green-900/30 rounded-lg">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  15%
                </div>
                <div className="text-sm text-muted-foreground">
                  {getTranslation('calculation_methodology.required_capabilities')}
                </div>
              </div>
              <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/30 rounded-lg">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  10%
                </div>
                <div className="text-sm text-muted-foreground">
                  {getTranslation('calculation_methodology.cost_efficiency')}
                </div>
              </div>
              <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/30 rounded-lg">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  10%
                </div>
                <div className="text-sm text-muted-foreground">
                  {getTranslation('calculation_methodology.latency_speed')}
                </div>
              </div>
              <div className="text-center p-4 bg-teal-50 dark:bg-teal-900/30 rounded-lg">
                <div className="text-2xl font-bold text-teal-600 dark:text-teal-400">
                  5%
                </div>
                <div className="text-sm text-muted-foreground">
                  {getTranslation('calculation_methodology.availability')}
                </div>
              </div>
            </div>
          </div>

          {/* Benchmark-Performance Berechnung */}
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
              <span className="text-2xl mr-2">📊</span>
              {getTranslation('calculation_methodology.benchmark_calculation')}
            </h3>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                {getTranslation('calculation_methodology.benchmark_description')}
              </p>

              <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  🎯 {getTranslation('calculation_methodology.critical_coding_benchmarks')}
                </h4>
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  • SWE-bench Verified • LiveCodeBench v2025 • Aider-Polyglot •
                  WebDev-Arena • Terminal-bench
                </p>
              </div>

              <div className="bg-purple-50 dark:bg-purple-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-2">
                  📏 {getTranslation('calculation_methodology.score_normalization')}
                </h4>
                <div className="text-sm text-purple-800 dark:text-purple-200 space-y-1">
                  <div>
                    <strong>{getTranslation('calculation_methodology.arena_scores')}</strong>
                  </div>
                  <div>
                    <strong>{getTranslation('calculation_methodology.elo_ratings')}</strong>
                  </div>
                  <div>
                    <strong>{getTranslation('calculation_methodology.standard_benchmarks')}</strong>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Weitere Bewertungsfaktoren */}
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
              <span className="text-2xl mr-2">⚙️</span>
              {getTranslation('calculation_methodology.other_factors')}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
                  ⚙️ {getTranslation('calculation_methodology.capabilities_score')}
                </h4>
                <p className="text-sm text-green-800 dark:text-green-200">
                  {getTranslation('calculation_methodology.capabilities_description')}
                </p>
              </div>
              <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  💰 {getTranslation('calculation_methodology.cost_score')}
                </h4>
                <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <div>
                    <strong>{getTranslation('calculation_methodology.budget_range')}</strong>
                  </div>
                  <div>
                    <strong>{getTranslation('calculation_methodology.standard_range')}</strong>
                  </div>
                  <div>
                    <strong>{getTranslation('calculation_methodology.premium_range')}</strong>
                  </div>
                </div>
              </div>
              <div className="bg-purple-50 dark:bg-purple-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-2">
                  ⚡ {getTranslation('calculation_methodology.latency_score')}
                </h4>
                <div className="text-sm text-purple-800 dark:text-purple-200 space-y-1">
                  <div>
                    <strong>{getTranslation('calculation_methodology.fastest')}</strong>
                  </div>
                  <div>
                    <strong>{getTranslation('calculation_methodology.fast')}</strong>
                  </div>
                  <div>
                    <strong>{getTranslation('calculation_methodology.moderately_fast')}</strong>
                  </div>
                  <div>
                    <strong>{getTranslation('calculation_methodology.slow_slowest')}</strong>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div className="bg-teal-50 dark:bg-teal-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-teal-900 dark:text-teal-100 mb-2">
                  📄 {getTranslation('calculation_methodology.context_window_score')}
                </h4>
                <div className="text-sm text-teal-800 dark:text-teal-200 space-y-1">
                  <div>
                    <strong>500k+:</strong> 120pts • <strong>200k+:</strong>{" "}
                    100pts • <strong>128k+:</strong> 90pts •{" "}
                    <strong>64k+:</strong> 80pts
                  </div>
                  <div>
                    <strong>32k+:</strong> 70pts • <strong>16k+:</strong> 60pts
                    • <strong>&lt;16k:</strong> 40pts
                  </div>
                </div>
              </div>
              <div className="bg-indigo-50 dark:bg-indigo-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-indigo-900 dark:text-indigo-100 mb-2">
                  🏢 {getTranslation('calculation_methodology.availability_score')}
                </h4>
                <div className="text-sm text-indigo-800 dark:text-indigo-200 space-y-1">
                  <div>
                    <strong>{getTranslation('calculation_methodology.ga_score')}</strong> • <strong>{getTranslation('calculation_methodology.preview_score')}</strong>{" "}
                     • <strong>{getTranslation('calculation_methodology.other_score')}</strong>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Qualitätsfaktoren & Coding-Optimierungen */}
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
              <span className="text-2xl mr-2">🔧</span>
              {getTranslation('calculation_methodology.quality_factors')}
            </h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-red-50 dark:bg-red-900/30 p-4 rounded-lg">
                  <h4 className="font-medium text-red-900 dark:text-red-100 mb-2">
                    ❌ {getTranslation('calculation_methodology.benchmark_penalties')}
                  </h4>
                  <p className="text-sm text-red-800 dark:text-red-200">
                    {getTranslation('calculation_methodology.penalty_description')}
                  </p>
                </div>
                <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg">
                  <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
                    🎯 {getTranslation('calculation_methodology.multimodal_bonus')}
                  </h4>
                  <p className="text-sm text-green-800 dark:text-green-200">
                    {getTranslation('calculation_methodology.multimodal_description')}
                  </p>
                </div>
              </div>

              <div className="bg-indigo-50 dark:bg-indigo-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-indigo-900 dark:text-indigo-100 mb-2">
                  💻 {getTranslation('calculation_methodology.coding_adjustments')}
                </h4>
                <div className="text-sm text-indigo-800 dark:text-indigo-200 space-y-2">
                  <div>
                    <strong>{getTranslation('calculation_methodology.coding_use_cases')}</strong>
                  </div>
                  <div>
                    <strong>{getTranslation('calculation_methodology.context_bonus')}</strong>
                  </div>
                  <div>
                    <strong>{getTranslation('calculation_methodology.affected_use_cases')}</strong>
                  </div>
                </div>
              </div>

              <div className="bg-amber-50 dark:bg-amber-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-amber-900 dark:text-amber-100 mb-2">
                  🔄 {getTranslation('calculation_methodology.final_calculation')}
                </h4>
                <div className="text-sm text-amber-800 dark:text-amber-200">
                  <strong>{getTranslation('calculation_methodology.total_score_formula')}</strong>
                </div>
              </div>
            </div>
          </div>

          {/* Use Case Benchmark-Mappings */}
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
              <span className="text-2xl mr-2">🗺️</span>
              {getTranslation('calculation_methodology.use_case_mappings')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {getTranslation('calculation_methodology.mappings_description')}
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded">
                  <strong>
                    {getTranslation('calculation_methodology.code_generation')}
                  </strong>
                  <br />
                  SWE-bench Verified, LiveCodeBench v2025, Aider-Polyglot,
                  WebDev-Arena, Terminal-bench
                </div>
                <div className="bg-green-50 dark:bg-green-900/30 p-3 rounded">
                  <strong>{getTranslation('calculation_methodology.api_integration')}</strong>
                  <br />+ ComplexFuncBench, TAU-bench Retail/Airline
                </div>
              </div>
              <div className="space-y-2">
                <div className="bg-purple-50 dark:bg-purple-900/30 p-3 rounded">
                  <strong>{getTranslation('calculation_methodology.devops_automation')}</strong>
                  <br />
                  Code-Benchmarks + TAU-bench Retail/Airline
                </div>
                <div className="bg-orange-50 dark:bg-orange-900/30 p-3 rounded">
                  <strong>{getTranslation('calculation_methodology.data_analysis')}</strong>
                  <br />
                  MATH, MMMU, MathVista, LiveCodeBench v2025
                </div>
                <div className="bg-teal-50 dark:bg-teal-900/30 p-3 rounded">
                  <strong>{getTranslation('calculation_methodology.learning_documentation')}</strong>
                  <br />
                  MMLU, IFEval, MMLU, MultiChallenge
                </div>
              </div>
            </div>
          </div>

          {/* Bewertungsskala und Transparenz */}
          <div className="bg-card rounded-lg shadow-md p-6 border">
            <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
              <span className="text-2xl mr-2">🏆</span>
              {getTranslation('calculation_methodology.rating_scale')}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-foreground mb-3">
                  {getTranslation('calculation_methodology.suitability_categories')}
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>⭐ {getTranslation('calculation_methodology.excellent')}</span>
                    <span className="font-bold text-green-600 dark:text-green-400">
                      ≥ 80 Punkte
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>✅ {getTranslation('calculation_methodology.good')}</span>
                    <span className="font-bold text-blue-600 dark:text-blue-400">
                      65-79 Punkte
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>⚠️ {getTranslation('calculation_methodology.acceptable')}</span>
                    <span className="font-bold text-yellow-600 dark:text-yellow-400">
                      50-64 Punkte
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>❌ {getTranslation('calculation_methodology.limited')}</span>
                    <span className="font-bold text-red-600 dark:text-red-400">
                      &lt; 50 Punkte
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-foreground mb-3">
                  {getTranslation('calculation_methodology.recommendation_categories')}
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>🎯 {getTranslation('calculation_methodology.recommended')}</span>
                    <span className="font-bold text-green-600 dark:text-green-400">
                      ≥ 65 Punkte
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>🔄 {getTranslation('calculation_methodology.alternative')}</span>
                    <span className="font-bold text-yellow-600 dark:text-yellow-400">
                      50-64 Punkte
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>⛔ {getTranslation('calculation_methodology.not_recommended')}</span>
                    <span className="font-bold text-red-600 dark:text-red-400">
                      &lt; 50 Punkte
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-foreground mb-3">
                  {getTranslation('calculation_methodology.cost_effectiveness')}
                </h4>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div>
                    💰 <strong>{getTranslation('calculation_methodology.high_cost_eff')}</strong>
                  </div>
                  <div>
                    📊 <strong>{getTranslation('calculation_methodology.medium_cost_eff')}</strong>
                  </div>
                  <div>
                    💎 <strong>{getTranslation('calculation_methodology.low_cost_eff')}</strong>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-3 flex items-center">
              <span className="text-xl mr-2">⚠️</span>
              {getTranslation('calculation_methodology.disclaimer_title')}
            </h3>
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              {getTranslation('calculation_methodology.disclaimer_text')}{" "}
              {new Date().toLocaleDateString()}.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
