# API Authentication

To access the Iteratec GPT API endpoints (e.g., `/model_group/info`), authentication is required. The API typically expects one of the following authentication methods:

## 1. API Key Authentication

Include your API key in the request header:

```
x-api-key: YOUR_API_KEY
```

**Example:**
```bash
curl -X GET "https://api.iteragpt.iteratec.de/model_group/info" \
  -H "x-api-key: YOUR_API_KEY"
```

## 2. Bearer Token (OAuth2) Authentication

Alternatively, you may need to authenticate using a Bearer token (OAuth2):

```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**Example:**
```bash
curl -X GET "https://api.iteragpt.iteratec.de/model_group/info" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Obtaining Credentials

- You must log in via the web interface to obtain your API key or access token.
- If you do not have access, visit the [login page](https://api.iteragpt.iteratec.de/) and follow the authentication process.
- For further details, consult your administrator or the official API documentation.

## Notes

- All API requests require authentication. Unauthenticated requests will be rejected.
- Protect your API key or access token; do not share them publicly.
- The exact authentication method may depend on your organization's configuration.