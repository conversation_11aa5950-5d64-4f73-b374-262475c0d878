import { TranslationProvider } from "../../contexts/TranslationContext";
import { BlogOverviewIsland } from "./BlogOverviewIsland";
import type { BlogPost, BlogMetadata } from "@/types/blog";

interface BlogOverviewIslandWrapperProps {
  initialPosts?: BlogPost[];
  initialMetadata?: BlogMetadata;
  postsPerPage?: number;
}

export function BlogOverviewIslandWrapper(props: BlogOverviewIslandWrapperProps) {
  return (
    <TranslationProvider>
      <BlogOverviewIsland {...props} />
    </TranslationProvider>
  );
}