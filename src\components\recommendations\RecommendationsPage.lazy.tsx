import { lazy } from 'react';

// Lazy load the heavy RecommendationsPage component
export const RecommendationsPageLazy = lazy(() =>
  import('./RecommendationsPage').then(module => ({
    default: module.RecommendationsPage
  }))
);

// Loading fallback component
export const RecommendationsPageSkeleton = () => (
  <div className="container mx-auto px-4 py-8">
    <div className="space-y-8">
      {/* Header skeleton */}
      <div className="text-center space-y-4">
        <div className="h-10 w-96 bg-muted animate-pulse rounded mx-auto"></div>
        <div className="h-6 w-[600px] bg-muted animate-pulse rounded mx-auto"></div>
      </div>
      
      {/* Use case cards skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }, (_, i) => `use-case-skeleton-${i}`).map((key) => (
          <div key={key} className="rounded-xl border bg-card p-6">
            <div className="space-y-4">
              <div className="h-6 w-32 bg-muted animate-pulse rounded"></div>
              <div className="h-4 w-full bg-muted animate-pulse rounded"></div>
              <div className="h-4 w-3/4 bg-muted animate-pulse rounded"></div>
              
              {/* Model recommendations skeleton */}
              <div className="space-y-2">
                <div className="h-5 w-24 bg-muted animate-pulse rounded"></div>
                {Array.from({ length: 3 }, (_, j) => `model-rec-skeleton-${j}`).map((key) => (
                  <div key={key} className="flex items-center gap-2">
                    <div className="h-4 w-4 bg-muted animate-pulse rounded"></div>
                    <div className="h-4 w-32 bg-muted animate-pulse rounded"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Detailed recommendations skeleton */}
      <div className="space-y-6">
        <div className="h-8 w-48 bg-muted animate-pulse rounded"></div>
        
        {Array.from({ length: 3 }, (_, i) => `detailed-rec-skeleton-${i}`).map((key) => (
          <div key={key} className="rounded-lg border bg-card p-6">
            <div className="space-y-4">
              <div className="h-6 w-40 bg-muted animate-pulse rounded"></div>
              <div className="h-4 w-full bg-muted animate-pulse rounded"></div>
              <div className="h-4 w-5/6 bg-muted animate-pulse rounded"></div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Array.from({ length: 3 }, (_, j) => `detailed-rec-item-skeleton-${j}`).map((key) => (
                  <div key={key} className="space-y-2">
                    <div className="h-5 w-24 bg-muted animate-pulse rounded"></div>
                    <div className="h-4 w-32 bg-muted animate-pulse rounded"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);