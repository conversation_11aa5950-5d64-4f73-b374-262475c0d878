import type { BlogPost, BlogMetadata, BlogFilters, BlogSearchResult, ModelBackgroundInfo } from '@/types/blog';
import { getModelCards, matchModelIdToCard } from './model-cards';

let blogPostsCache: BlogPost[] | null = null;
let blogMetadataCache: BlogMetadata | null = null;

// Reset cache for development/testing
export function resetBlogCache() {
  blogPostsCache = null;
  blogMetadataCache = null;
}

/**
 * Load all blog posts from JSON fallback (CLIENT-SAFE)
 */
export async function getBlogPosts(): Promise<BlogPost[]> {
  if (blogPostsCache) {
    return blogPostsCache;
  }

  try {
    // Client-side: try to fetch from JSON fallback
    const response = await fetch(`/data/blog-posts.json`);
    if (response.ok) {
      const data = await response.json();
      blogPostsCache = data.posts || [];
    } else {
      console.warn('Could not load blog-posts.json, using empty array');
      return [];
    }
    
    // Sort by publish date (newest first)
    if (blogPostsCache) {
      blogPostsCache.sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime());
      return blogPostsCache;
    }
    
    return [];
  } catch (error) {
    console.error('Error loading blog posts:', error);
    return [];
  }
}

/**
 * Get blog metadata including statistics (CLIENT-SAFE)
 */
export async function getBlogMetadata(): Promise<BlogMetadata> {
  if (blogMetadataCache) {
    return blogMetadataCache;
  }

  const posts = await getBlogPosts();
  
  // Calculate categories
  const categories: { [key: string]: number } = {};
  const tags: { [key: string]: number } = {};
  
  posts.forEach(post => {
    categories[post.category] = (categories[post.category] || 0) + 1;
    post.tags.forEach(tag => {
      tags[tag] = (tags[tag] || 0) + 1;
    });
  });

  blogMetadataCache = {
    totalPosts: posts.length,
    categories,
    tags,
    latestPosts: posts.slice(0, 5),
    featuredPosts: posts.filter(post => post.featured).slice(0, 3)
  };

  return blogMetadataCache;
}

/**
 * Get a single blog post by slug (CLIENT-SAFE)
 */
export async function getBlogPost(slug: string): Promise<BlogPost | null> {
  const posts = await getBlogPosts();
  return posts.find(post => post.slug === slug) || null;
}

/**
 * Search blog posts with filters (CLIENT-SAFE)
 */
export async function searchBlogPosts(
  query?: string,
  filters: BlogFilters = {},
  limit: number = 20,
  offset: number = 0
): Promise<BlogSearchResult> {
  const allPosts = await getBlogPosts();
  
  let filteredPosts = allPosts;
  
  // Apply filters
  if (filters.category) {
    filteredPosts = filteredPosts.filter(post => post.category === filters.category);
  }
  
  if (filters.tags && filters.tags.length > 0) {
    filteredPosts = filteredPosts.filter(post => 
      filters.tags?.some(tag => post.tags.includes(tag)) ?? false
    );
  }
  
  if (filters.relatedModel) {
    filteredPosts = filteredPosts.filter(post => 
      filters.relatedModel ? post.relatedModelIds?.includes(filters.relatedModel) ?? false : false
    );
  }
  
  if (filters.featured !== undefined) {
    filteredPosts = filteredPosts.filter(post => post.featured === filters.featured);
  }
  
  if (filters.dateRange) {
    const startDate = new Date(filters.dateRange.start);
    const endDate = new Date(filters.dateRange.end);
    filteredPosts = filteredPosts.filter(post => {
      const postDate = new Date(post.publishDate);
      return postDate >= startDate && postDate <= endDate;
    });
  }
  
  // Apply text search
  if (query) {
    const lowercaseQuery = query.toLowerCase();
    filteredPosts = filteredPosts.filter(post => 
      post.title.toLowerCase().includes(lowercaseQuery) ||
      post.excerpt.toLowerCase().includes(lowercaseQuery) ||
      post.content.toLowerCase().includes(lowercaseQuery) ||
      post.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  }
  
  // Calculate facets from filtered results
  const categories: { [key: string]: number } = {};
  const tags: { [key: string]: number } = {};
  const models: { [key: string]: number } = {};
  
  filteredPosts.forEach(post => {
    categories[post.category] = (categories[post.category] || 0) + 1;
    post.tags.forEach(tag => {
      tags[tag] = (tags[tag] || 0) + 1;
    });
    post.relatedModelIds?.forEach(modelId => {
      models[modelId] = (models[modelId] || 0) + 1;
    });
  });
  
  // Apply pagination
  const paginatedPosts = filteredPosts.slice(offset, offset + limit);
  
  return {
    posts: paginatedPosts,
    totalCount: filteredPosts.length,
    facets: { categories, tags, models }
  };
}

/**
 * Get posts related to a specific model (CLIENT-SAFE)
 */
export async function getModelRelatedPosts(modelId: string): Promise<BlogPost[]> {
  const posts = await getBlogPosts();
  return posts.filter(post => post.relatedModelIds?.includes(modelId));
}

/**
 * Get model background information (enhanced from model cards) (CLIENT-SAFE)
 */
export async function getModelBackgroundInfo(modelId: string): Promise<ModelBackgroundInfo | null> {
  try {
    const modelCards = await getModelCards();
    const modelCard = matchModelIdToCard(modelId, modelCards.modelCards);
    
    if (!modelCard) {
      return null;
    }
    
    // Load additional background info from separate file if available
    try {
      // Client-side: use fetch
      const cacheBuster = Date.now();
      const response = await fetch(`/data/model-backgrounds/${modelId}.json?v=${cacheBuster}`);
      if (response.ok) {
        const backgroundData = await response.json();
        return backgroundData;
      }
    } catch {
      console.log(`No specific background file for ${modelId}, generating from model card`);
    }
    
    // Generate basic background info from model card
    const backgroundInfo: ModelBackgroundInfo = {
      modelId: modelCard.basicInfo.modelId,
      developmentHistory: {
        releaseDate: modelCard.basicInfo.releaseDate,
        keyMilestones: [
          {
            date: modelCard.basicInfo.releaseDate,
            title: `${modelCard.basicInfo.displayName} Release`,
            description: modelCard.basicInfo.description,
            impact: 'major' as const
          }
        ]
      },
      technicalInsights: {
        architecture: modelCard.technicalSpecs.architecture || "Unknown",
        innovations: [],
        limitations: []
      },
      competitiveAnalysis: {
        directCompetitors: [],
        differentiators: Object.entries(modelCard.capabilities)
          .filter(([_, supported]) => supported)
          .map(([capability, _]) => capability),
        marketPosition: `${modelCard.basicInfo.provider} ${modelCard.basicInfo.modelFamily} series`
      },
      useCaseSpotlight: {
        primaryUseCases: inferUseCasesFromCapabilities(modelCard.capabilities),
        bestPractices: []
      }
    };
    
    return backgroundInfo;
  } catch (error) {
    console.error('Error loading model background info:', error);
    return null;
  }
}

/**
 * Get recent release notes (CLIENT-SAFE)
 */
export async function getRecentReleaseNotes(limit: number = 5): Promise<BlogPost[]> {
  const posts = await getBlogPosts();
  return posts
    .filter(post => post.category === 'release-notes')
    .slice(0, limit);
}

/**
 * Get featured blog posts for homepage (CLIENT-SAFE)
 */
export async function getFeaturedPosts(limit: number = 3): Promise<BlogPost[]> {
  const posts = await getBlogPosts();
  return posts.filter(post => post.featured).slice(0, limit);
}

/**
 * Helper function to infer use cases from model capabilities
 */
function inferUseCasesFromCapabilities(capabilities: {
  vision?: boolean;
  functionCalling?: boolean;
  audioInput?: boolean;
  audioOutput?: boolean;
  pdfSupport?: boolean;
  webSearch?: boolean;
  [key: string]: unknown;
}): string[] {
  const useCases: string[] = [];
  
  if (capabilities.vision) {
    useCases.push('Bildanalyse und Computer Vision');
  }
  
  if (capabilities.functionCalling) {
    useCases.push('API-Integration und Tool-Usage');
  }
  
  if (capabilities.codeExecution || capabilities.codeInterpreter) {
    useCases.push('Code-Generierung und -Ausführung');
  }
  
  if (capabilities.reasoning || capabilities.thinking) {
    useCases.push('Komplexe Problemlösung und Analyse');
  }
  
  if (capabilities.audioInput || capabilities.audioOutput) {
    useCases.push('Audio-Verarbeitung und Sprachinteraktion');
  }
  
  if (capabilities.pdfSupport) {
    useCases.push('Dokumentenanalyse und -verarbeitung');
  }
  
  if (capabilities.multilingualSupport) {
    useCases.push('Mehrsprachige Anwendungen');
  }
  
  // Always include basic text use cases
  useCases.push('Textverarbeitung und Content-Erstellung');
  
  return useCases;
}

/**
 * Calculate reading time based on content length
 */
export function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}

/**
 * Generate blog post slug from title
 */
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

/**
 * Format blog post date for display
 */
export function formatBlogDate(dateString: string, locale: string = 'de-DE'): string {
  const date = new Date(dateString);
  return date.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Get related posts based on tags and category (CLIENT-SAFE)
 */
export async function getRelatedPosts(currentPost: BlogPost, limit: number = 3): Promise<BlogPost[]> {
  const allPosts = await getBlogPosts();
  
  // Filter out current post
  const otherPosts = allPosts.filter(post => post.id !== currentPost.id);
  
  // Score posts by relevance
  const scoredPosts = otherPosts.map(post => {
    let score = 0;
    
    // Same category gets higher score
    if (post.category === currentPost.category) {
      score += 3;
    }
    
    // Shared tags increase score
    const sharedTags = post.tags.filter(tag => currentPost.tags.includes(tag));
    score += sharedTags.length * 2;
    
    // Shared models increase score
    if (post.relatedModelIds && currentPost.relatedModelIds) {
      const sharedModels = post.relatedModelIds.filter(id => currentPost.relatedModelIds?.includes(id) ?? false);
      score += sharedModels.length * 4;
    }
    
    return { post, score };
  });
  
  // Sort by score and return top results
  return scoredPosts
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.post);
}