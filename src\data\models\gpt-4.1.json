{"basicInfo": {"modelId": "gpt-4.1", "displayName": "GPT-4.1", "provider": "OpenAI", "modelFamily": "GPT", "version": "2025-08-14", "description": "Flagship GPT model for complex tasks. It is well suited for problem solving across domains", "releaseDate": "2025-01-30", "status": "GA", "knowledgeCutoff": "Juni 2024"}, "technicalSpecs": {"contextWindow": 1047576, "maxOutputTokens": 32768, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": true, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": true, "codeInterpreter": true, "dalleIntegration": true, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 500, "tokensPerMinute": 20000, "batchQueueLimit": 90000}, "temperature": {"min": 0, "max": 2, "default": 1.0}, "topP": 1.0}, "pricing": {"inputCostPer1MTokens": 2.0, "outputCostPer1MTokens": 8.0, "cachingCosts": {"cacheHits": 0.5}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "OpenAI Batch API"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 54.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Real software engineering tasks"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 66.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Graduate-level reasoning in science"}, {"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 90.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Massive Multitask Language Understanding"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 87.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Multilingual Q&A across 14 languages"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 74.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Multimodal understanding and reasoning"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 72.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Visual mathematical tasks"}, {"benchmarkName": "CharXiv", "category": "Visual reasoning", "score": 56.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Questions about charts from scientific papers"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 48.1, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-30", "notes": "American Invitational Mathematics Examination"}, {"benchmarkName": "Internal API instruction following (hard)", "category": "Instruction following", "score": 49.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Hard subset of instruction following eval"}, {"benchmarkName": "MultiChallenge", "category": "Instruction following", "score": 38.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Multi-turn instruction following"}, {"benchmarkName": "IFEval", "category": "Instruction following", "score": 87.4, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Instruction following with verifiable instructions"}, {"benchmarkName": "Multi-IF", "category": "Instruction following", "score": 70.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Multi-instruction following benchmark"}, {"benchmarkName": "Graphwalks BFS <128k accuracy", "category": "Long context", "score": 61.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Multi-round co-reference resolution in langen Kontexten"}, {"benchmarkName": "ComplexFuncBench", "category": "Function calling", "score": 65.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Complex function calling benchmark"}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic coding", "score": 49.4, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Agentic tool use in airline scenarios"}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic coding", "score": 68.0, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-01-30", "notes": "Agentic tool use in retail scenarios"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 30.3, "alternativeScores": {"codexCli": 8.3}, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-15", "notes": "Terminus agent: 30.3% ±2.1%, Codex CLI: 8.3% ±1.4%"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 51.6, "alternativeScores": {"diff": 52.9}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Aider polyglot benchmark - whole: 51.6%, diff: 52.9%"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 98.2, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 71.0, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen (High reasoning effort)"}, {"benchmarkName": "MRCR", "category": "Long context", "score": 48.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Multi-hop Reading Comprehension with Reasoning"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1389.18, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #3"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 5.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "GPT-4.1: 5.40±0.89"}], "metadata": {"lastUpdated": "2025-06-09T12:47:00Z", "dataSource": "OpenAI Platform Documentation, Terminal-Bench Leaderboard", "version": "1.2"}}