import { lazy } from 'react';

// Lazy load the heavy ModelTableIsland component
export const ModelTableIslandLazy = lazy(() => 
  import('./ModelTableIsland').then(module => ({
    default: module.default
  }))
);

// Loading fallback component
export const ModelTableIslandSkeleton = () => (
  <div className="w-full max-w-[1400px] mx-auto">
    <div className="rounded-xl border bg-card text-card-foreground shadow">
      <div className="p-6">
        <div className="space-y-4">
          {/* Header skeleton */}
          <div className="flex justify-between items-center">
            <div className="h-8 w-48 bg-muted animate-pulse rounded"></div>
            <div className="flex gap-2">
              <div className="h-8 w-24 bg-muted animate-pulse rounded"></div>
              <div className="h-8 w-24 bg-muted animate-pulse rounded"></div>
            </div>
          </div>
          
          {/* Filters skeleton */}
          <div className="flex gap-4 flex-wrap">
            <div className="h-10 w-32 bg-muted animate-pulse rounded"></div>
            <div className="h-10 w-32 bg-muted animate-pulse rounded"></div>
            <div className="h-10 w-32 bg-muted animate-pulse rounded"></div>
            <div className="h-10 w-48 bg-muted animate-pulse rounded"></div>
          </div>
          
          {/* Table skeleton */}
          <div className="border rounded-lg">
            <div className="h-12 bg-muted/50 border-b"></div>
            {Array.from({ length: 10 }, (_, i) => `table-skeleton-row-${i}`).map((key) => (
              <div key={key} className="h-16 border-b last:border-b-0 bg-background">
                <div className="flex items-center gap-4 p-4">
                  <div className="h-4 w-4 bg-muted animate-pulse rounded"></div>
                  <div className="h-4 w-32 bg-muted animate-pulse rounded"></div>
                  <div className="h-4 w-24 bg-muted animate-pulse rounded"></div>
                  <div className="h-4 w-20 bg-muted animate-pulse rounded"></div>
                  <div className="h-4 w-16 bg-muted animate-pulse rounded"></div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Pagination skeleton */}
          <div className="flex justify-between items-center">
            <div className="h-4 w-32 bg-muted animate-pulse rounded"></div>
            <div className="flex gap-2">
              <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
              <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);