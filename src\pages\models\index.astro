---
import Layout from "../../layouts/Layout.astro";
import ModelTableIsland from "../../components/models/ModelTableIsland";
import CollapsibleHeaderIsland from "../../components/models/CollapsibleHeaderIsland";
import type { ModelData, BenchmarkData } from "../../types/api";
import * as fs from "node:fs/promises";
import * as path from "node:path";
import LanguageSelector from "../../components/LanguageSelector.jsx";

// Statische Daten zur Build-Zeit laden (GitLab Pages kompatibel)
// Verwende absoluten Pfad basierend auf dem Projekt-Root
const projectRoot = path.resolve(process.cwd());
const dataPath = path.join(projectRoot, "src", "data");

const modelsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "models.json"), "utf-8")
);
const enrichedModelsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "enriched-models.json"), "utf-8")
);
const benchmarksData = JSON.parse(
  await fs.readFile(path.join(dataPath, "polyglot_benchmarks.json"), "utf-8")
);
const statisticsData = JSON.parse(
  await fs.readFile(path.join(dataPath, "statistics.json"), "utf-8")
);

// Type assertions für die JSON-Daten - extrahiere models Array aus dem JSON
const models = (modelsData.models || modelsData) as ModelData[];
const enrichedModels = (enrichedModelsData.models ||
  enrichedModelsData) as ModelData[];
const benchmarks = (benchmarksData.benchmarks ||
  benchmarksData) as BenchmarkData[];
const statistics = {
  totalModels: statisticsData.models?.totalModels || 0,
  totalBenchmarks: statisticsData.benchmarks?.totalBenchmarks || 0,
  averageScore: statisticsData.benchmarks?.averagePassRate || 0,
  topPerformers:
    statisticsData.benchmarks?.topPerformers?.map((p: any) => p.model) || [],
};

---

<Layout title="LLM Model Browser">
  <main class="container mx-auto px-4 py-8">
    <!-- Kollabierbare Header-Komponente -->
    <CollapsibleHeaderIsland
      titleKey="models.header"
      descriptionKey="models.description"
      totalModels={statistics.totalModels}
      client:only="react"
    >
      <!-- Statistics are now handled dynamically in the React component -->
      <div></div>
    </CollapsibleHeaderIsland>

    <!-- React Island für die interaktive Model Table -->
    <ModelTableIsland
      models={enrichedModels}
      benchmarks={benchmarks}
      averageScore={statistics.averageScore}
      client:only="react"
    />
  </main>
</Layout>

<style>
  .container {
    max-width: 1400px;
  }
</style>
