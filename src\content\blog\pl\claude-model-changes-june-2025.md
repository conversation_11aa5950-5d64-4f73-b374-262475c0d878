---
title: "Aktualizacje modeli w iteratec od 12.06.2025: Claude 4 i generacja OpenAI o4"
excerpt: "Od 12 czerwca 2025 iteratec wprowadza kompleksowe aktualizacje modeli: modele Claude 3.5 zostaną zastąpione przez Claude 4 Sonnet/Opus, OpenAI o1/o3-mini zostaną zastąpione przez nową generację o3/o4-mini. Wszystkie nowe modele są dostępne w iteraGPT i przez API."
category: "release-notes"
tags: ["claude", "openai", "model-updates", "iteragpt", "api", "non-eu-models", "claude-4", "o4", "o4-mini"]
publishDate: "2025-06-11T15:30:00Z"
lastUpdated: "2025-06-11T15:30:00Z"
author:
  name: "Zespół AI iteratec"
  role: "Platform Engineering"
readingTime: 4
featured: true
relatedModelIds: ["claude-sonnet-4", "claude-opus-4", "o4-mini-2025-04-16", "o3-2025-04-16"]
releaseVersion: "2025.06.12"

# pola specyficzne dla i18n
lang: "pl"
translationKey: "claude-model-changes-june-2025"
availableLanguages: ["de", "en", "pl"]
changelog:
  - type: "removed"
    description: "Wszystkie modele Claude 3.5 zostaną usunięte z portfolio iteratec"
    impact: "major"
    technicalDetails: "Dotyczy Claude 3.7 Sonnet, Claude 3.7 Sonnet v2 i Claude 3.5 Haiku - zarówno w iteraGPT jak i przez API"
  - type: "removed"
    description: "Modele OpenAI o1 i o3-mini zostaną zastąpione przez nowsze generacje"
    impact: "major"
    technicalDetails: "o1 zostanie całkowicie usunięty, o3-mini zostanie zastąpiony przez o4-mini - lepsza wydajność przy tych samych kosztach"
  - type: "added"
    description: "Claude 4 Sonnet dostępny jako model EU"
    impact: "major"
    technicalDetails: "Dostarczany jako otwarty model bez ograniczeń ochrony danych UE, dostępny w iteraGPT i przez API"
  - type: "added"
    description: "Claude 4 Opus dostępny jako model Non-EU (tylko USA bezpośrednio przez Anthropic)"
    impact: "major"
    technicalDetails: "Dostarczany jako otwarty model bez ograniczeń ochrony danych UE, dostępny w iteraGPT i przez API"
  - type: "added"
    description: "OpenAI o4-mini dostępny jako następca o3-mini"
    impact: "major"
    technicalDetails: "Ulepszona wydajność rozumowania przy tych samych kosztach, rozszerzone możliwości wizyjne i wsparcie przeglądania sieci"
  - type: "added"
    description: "OpenAI o3 dostępny jako nowy model premium do rozumowania"
    impact: "major"
    technicalDetails: "Zastępuje o1 ze znacznie ulepszoną wydajnością w matematyce, kodowaniu i rozumowaniu naukowym"
metaDescription: "Od 12 czerwca 2025 iteratec wprowadza kompleksowe aktualizacje modeli: Claude 4 Sonnet/Opus zastępują Claude 3.5, OpenAI o4/o4-mini zastępują o1/o3-mini. Dostępne w iteraGPT i przez API."
metaKeywords: ["Claude 4", "OpenAI o4", "iteratec", "Modele Non-EU", "Platforma AI", "Enterprise AI", "Modele rozumowania"]
featuredImage: "/images/blog/2025-06-anthropic-claude.png"
---

**12 czerwca 2025** iteratec wprowadza kompleksowe zmiany w portfolio modeli AI. Ta strategiczna reorganizacja dotyczy zarówno modeli Anthropic Claude jak i OpenAI i przynosi znacznie potężniejsze możliwości rozumowania oraz rozszerzone opcje dostępności dla naszych klientów korporacyjnych.

## Przegląd najważniejszych zmian

### ❌ Usunięcie starszych modeli

**Modele Claude 3.5** zostaną całkowicie usunięte z portfolio iteratec:
**Modele OpenAI** poprzedniej generacji zostaną zastąpione (**o1** zostanie całkowicie usunięty, **o3-mini** zostanie zastąpiony przez **o4-mini**)

Te zmiany dotyczą zarówno interfejsu użytkownika iteraGPT jak i bezpośredniego dostępu przez API.

### ✅ Nowe modele następnej generacji

| Model | Dostępność | Wydajność | Następca |
|-------|------------|-----------|----------|
| **Claude 4 Sonnet** | Wewnętrzny: GCP (hosting EU)<br/>Otwarty: Anthropic (hosting USA) | Znacznie ulepszone możliwości rozumowania<br/>72.7% SWE-bench<br/>76.3% AIME 2024 | Claude 3.5/3.7 Sonnet |
| **Claude 4 Opus** | Otwarty: Anthropic (hosting USA) | Bardzo dobra wydajność dla złożonych zadań<br/>Premium wydajność we wszystkich obszarach | Claude 3.5 Opus |
| **o4-mini** | Wewnętrzny: Azure (hosting EU)<br/>Otwarty: OpenAI (hosting USA) | 93.4% AIME 2024<br/>68.1% SWE-bench<br/>Wsparcie wizji, przeglądanie sieci<br/>$1.10/$4.40 za 1M tokenów | o3-mini |
| **o3** | Wewnętrzny: Azure (hosting EU)<br/>Otwarty: OpenAI (hosting USA) | 91.6% AIME 2024<br/>69.1% SWE-bench<br/>Bardzo silne rozumowanie z 2M tokenami rozumowania<br/>Również silny w matematyce, rozumowaniu naukowym | o1 (całkowite zastąpienie) |

**Ważne ulepszenia:**
- **Claude 4**: 65% mniej skrótów w rozwiązywaniu zadań, pamięć trwała, integracja MCP
- **o4-mini**: Nowe możliwości wizyjne (79.7% MMMU, 84.3% MathVista), te same koszty co poprzednik

## Specyfika Claude 4 w porównaniu do Claude 3.7 Sonnet

**Claude 4** (szczególnie Sonnet 4 i Opus 4) przynosi liczne ulepszenia i nowe funkcje w porównaniu do **Claude 3.7 Sonnet**, które wpływają zarówno na wydajność jak i praktyczne zastosowanie.

### Ulepszone możliwości myślenia i rozwiązywania problemów

- **Bardziej ostrożne i stopniowe myślenie:** Modele Claude 4 znacznie lepiej unikają skrótów lub sztuczek w rozwiązywaniu zadań – prawdopodobieństwo tego jest o 65% niższe niż w przypadku Sonnet 3.7[1][4][7].
- **Bardziej złożone zadania:** Sonnet 4 może realizować znacznie bardziej złożone instrukcje i zadania, szczególnie w obszarze kodowania i rozumowania[4][5][7].
- **Rozszerzone kroki myślenia:** Oba modele (Sonnet 4 i Opus 4) są w stanie zatrzymać się i rozważyć wiele kroków przed odpowiedzią – szczególnie cenne dla procesów wieloetapowych lub treści strukturalnych[1][5].

### Poprawa wydajności w kodowaniu

- **Wyniki benchmarków:** Sonnet 4 osiąga 72.7% dokładności w SWE-bench, podczas gdy Sonnet 3.7 miał 62.3%[2][7]. To znaczący skok, który jest szczególnie zauważalny w rozwoju oprogramowania, debugowaniu i automatyzacji.
- **Lepsza nawigacja kodu i rozwiązywanie problemów:** Według partnerów takich jak GitHub i Sourcegraph, Sonnet 4 pokazuje znacznie ulepszoną zdolność do nawigacji kodu i wykonywania złożonych instrukcji[7].

### Nowe i ulepszone funkcje

- **Pamięć trwała:** Claude 4 (szczególnie Opus 4) może tworzyć pliki pamięci trwałej, gdy programiści pozwalają na dostęp do plików lokalnych. W ten sposób model może przechowywać i ponownie wykorzystywać ważne szczegóły w wielu sesjach[1][4][5].
- **Integracja narzędzi:** Claude 4 może wchodzić w interakcje z API i systemami plików przez Model Context Protocol (MCP) i używać zewnętrznych narzędzi jako część przepływu pracy[1][7].
- **Files API i buforowanie promptów:** Nowe funkcje API umożliwiają przesyłanie i odwoływanie się do dokumentów w wielu sesjach oraz znacznie dłuższe buforowanie promptów (do godziny)[7].
- **Narzędzie wykonywania kodu:** Kod Python może być wykonywany w środowisku piaskownicy, włączając analizę danych i wizualizację w jednym kroku[7].

### Ulepszone przetwarzanie kontekstu i pamięć

- **Lepsze zrozumienie kontekstu:** Sonnet 4 opiera się na solidnym przetwarzaniu języka Sonnet 3.7, ale oferuje ulepszoną pamięć kontekstową i może lepiej śledzić dłuższe, złożone zadania[8].
- **Podsumowania myślenia:** Dla szczególnie długich procesów myślowych tworzone są podsumowania w celu skondensowania łańcucha kroków myślowych – ale jest to konieczne tylko w około 5% przypadków[7].

### Bezpieczeństwo i niezawodność

- **Zmniejszona podatność na błędy:** Modele są mniej podatne na klasyczne błędy, takie jak przegapienie szczegółów w zagadkach lub nieprawidłowe liczenie znaków, co wcześniej musiało być kompensowane przez jawne prompty systemowe[3].
- **Bardziej rygorystyczne standardy bezpieczeństwa:** Wraz z wprowadzeniem modeli Claude 4 aktywowano również bardziej rygorystyczne standardy bezpieczeństwa[7].

### Podsumowanie najważniejszych różnic

| Cecha                        | Claude 3.7 Sonnet           | Claude 4 (Sonnet/Opus)           |
|------------------------------|-----------------------------|---------------------------------|
| Staranność w zadaniach       | Dobra                       | Znacznie ulepszona, 65% mniej skrótów[1][4][7] |
| Wydajność kodowania (SWE-bench)| 62.3%                      | 72.7%[2][7]                     |
| Pamięć kontekstu             | Solidna                     | Rozszerzona, pamięć trwała[1][4][5][8] |
| Integracja narzędzi          | Ograniczona                 | MCP, Files API, wykonywanie kodu[1][7] |
| Buforowanie promptów         | 5 minut                     | Do 1 godziny[7]                 |
| Zakres zastosowań            | Ogólnego przeznaczenia, kodowanie | Złożone, wieloetapowe zadania, przepływy pracy agentów, kodowanie[6][7] |

**Wniosek:**  
Claude 4 (Sonnet 4 i Opus 4) to zauważalne ulepszenie w stosunku do Claude 3.7 Sonnet w prawie wszystkich obszarach: myśli bardziej starannie, rozwiązuje bardziej złożone zadania, jest znacznie silniejszy w kodowaniu i przynosi nowe funkcje dla programistów i przepływów pracy agentów[1][4][5][6][7].

## Szczegółowe ulepszenia wydajności na poziomie benchmarków

Nowe modele oferują znaczące ulepszenia w porównaniu do swoich poprzedników:

### Porównanie benchmarków Claude 4
- **SWE-bench Verified**: Claude 4 Sonnet osiąga 72.7% (vs. 62.3% dla Claude 3.7 Sonnet)
- **AIME 2024 (Matematyka)**: Claude 4 Sonnet osiąga 76.3% (vs. 54.8% dla Claude 3.7 Sonnet)
- **LiveCodeBench v2025**: Claude 4 Sonnet osiąga 70.9% (vs. 63.8% dla Claude 3.7 Sonnet)
- **Terminal-bench**: Claude 4 Sonnet osiąga 35.5% (vs. 35.2% dla Claude 3.7 Sonnet)
- **MMLU (Wielojęzyczny)**: Claude 4 Sonnet osiąga 86.5% (vs. 85.9% dla Claude 3.7 Sonnet)

### Porównanie benchmarków generacji OpenAI o4
- **AIME 2024**: o4-mini osiąga 93.4% (vs. 86.5% dla o3-mini), o3 osiąga 91.6% (vs. 74.3% dla o1)
- **SWE-bench Verified**: o4-mini osiąga 68.1% (vs. 48.0% dla o3-mini), o3 osiąga 69.1% (vs. 48.9% dla o1)
- **GPQA Diamond**: o4-mini osiąga 81.4% (vs. 75.0% dla o3-mini), o3 osiąga 83.3% (vs. 78.0% dla o1)
- **LiveCodeBench v2025**: o4-mini osiąga 68.3% (vs. 69.5% dla o3-mini), o3 osiąga 75.9%
- **Możliwości wizyjne**: o4-mini nowy z MMMU 79.7%, MathVista 84.3% (o3-mini nie miał wizji)

## Porównanie: OpenAI o1 vs. o3

OpenAI o3 to bezpośrednia ewolucja o1 i przynosi znaczące ulepszenia w prawie wszystkich istotnych obszarach. Najważniejsze różnice i cechy można podsumować następująco:

**Wydajność i benchmarki**

- **Myślenie wizualne i logiczne:**  
  o3 przewyższa o1 w wymagających benchmarkach takich jak MMMU College-level Visual Problem-Solving (82.9% vs. 77.6%), MathVista Visual Math Reasoning (86.8% vs. 71.8%) i CharXiv-Reasoning Scientific Figure Reasoning (78.6% vs. o1)[1][2].

- **Zadania matematyczne i naukowe:**  
  o3 osiąga 91.6% dokładności w AIME 2024 (o1: 74.3%) i 83.3% w GPQA Diamond (pytania naukowe na poziomie doktorskim) (o1: 78%)[1][2][4].

- **Kodowanie i rozwój oprogramowania:**  
  o3 osiąga 69.1% dokładności w SWE-Bench Verified Software Engineering Benchmark (o1: 48.9%) i wartość ELO 2706 w Competitive Programming (o1: 1891)[1][2]. o3 jest również znacznie lepszy w benchmarkach edycji kodu[5].

**Praktyczne różnice i funkcje**

| Cecha                        | OpenAI o1                  | OpenAI o3                      |
|------------------------------|----------------------------|--------------------------------|
| Szybkość                     | Solidna, ale wolniejsza    | Znacznie szybsza (np. o3-mini: 24% szybszy niż o1-mini)[6][4] |
| Dokładność i niezawodność    | Dobra dla prostych zadań   | Wyższa dokładność, nawet dla złożonych problemów (np. 88% w benchmarku ARC-AGI vs. 32% dla o1)[4] |
| Głębokość zrozumienia        | Powierzchowne sprawdzenia  | Rozpoznaje nawet subtelne błędy, np. w przeglądach kodu[5] |
| Zdolność adaptacji           | Ograniczona                | Wszechstronna, odpowiednia dla złożonych i różnych zadań[4] |
| Możliwości generatywne       | Ograniczone                | Wysokiej jakości treści, złożone rozwiązywanie problemów[4] |
| Bezpieczeństwo               | Wysoki standard            | Nowe mechanizmy bezpieczeństwa, deliberative alignment[6] |
| Kompatybilność narzędzi      | Ograniczona                | Szerokie wsparcie narzędzi i platform[4] |

**Podsumowanie:**
- **o3** jest znacznie lepszy od swojego poprzednika **o1** we wszystkich benchmarkach, szczególnie w myśleniu logicznym, kodowaniu i zadaniach matematyczno-naukowych[1][2][4][5].
- **o3** w praktyce rozpoznaje również bardziej subtelne błędy i problemy, takie jak w przeglądach kodu lub złożonych zadaniach, podczas gdy o1 jest raczej ograniczony do powierzchownych sprawdzeń[5].
- **o3** jest szybszy, bardziej wszechstronny i oferuje bardziej zaawansowane mechanizmy bezpieczeństwa i adaptacji[4][6].

**Wniosek:**  
OpenAI o3 to jasny wybór dla zaawansowanych, złożonych i profesjonalnych zastosowań, podczas gdy o1 pozostaje solidną opcją dla prostszych, ekonomicznych zadań.