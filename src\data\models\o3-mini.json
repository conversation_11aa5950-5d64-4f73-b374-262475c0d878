{"basicInfo": {"modelId": "o3-mini-2025-01-31", "displayName": "o3-mini", "provider": "OpenAI", "modelFamily": "o3", "version": "2025-01-31", "description": "Das neueste kleine Reasoning-<PERSON><PERSON> von OpenAI, das hohe Intelligenz bei gleichen Kosten- und Latenzzielen wie o1-mini bietet. WIRD AB 12.06.2025 DURCH O4-MINI ERSETZT.", "releaseDate": "2025-01-31", "status": "DEPRECATED", "knowledgeCutoff": "Oktober 2023"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 100000, "maxReasoningTokens": 100000, "maxCompletionTokens": 100000, "supportedInputTypes": ["text"], "supportedOutputTypes": ["text"]}, "capabilities": {"functionCalling": true, "vision": false, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": true, "codeExecution": true, "systemInstructions": false, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": false, "codeInterpreter": true, "dalleIntegration": true, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 1000, "tokensPerMinute": 100000, "batchQueueLimit": 1000000}, "reasoningPerformance": {"averageReasoningTime": "5-15 Se<PERSON><PERSON>", "maxReasoningTime": "60 Sekunden", "reasoningEfficiency": "High"}, "temperature": {"min": 0, "max": 2, "default": 1.0}}, "pricing": {"inputCostPer1MTokens": 1.1, "outputCostPer1MTokens": 4.4, "cachingCosts": {"cacheHits": 0.55}, "batchProcessingCosts": {"inputCostPer1MTokens": 0.55, "outputCostPer1MTokens": 2.2}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "Azure OpenAI", "OpenAI Batch API"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 48.0, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-31", "notes": "Wird durch o4-mini ersetzt (68.1% SWE-bench). o3-mini wird ab 12.06.2025 nicht mehr verfügbar sein."}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 75.0, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-31", "notes": "Geschätzt basierend auf o3-mini Positionierung"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 86.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "AIME 2024 and 2025 combined benchmark from vals.ai"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 97.9, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-15", "notes": "MATH 500 benchmark"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 78.0, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-01-31", "notes": "Geschätzt basierend auf o3-mini Positionierung"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 53.8, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-01-31", "notes": "Aider polyglot benchmark with diff edit format (medium reasoning effort)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 95.1, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-01-31", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 69.5, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen (High reasoning effort)"}, {"benchmarkName": "MRCR", "category": "Long context", "score": 36.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Multi-hop Reading Comprehension with Reasoning"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1250.0, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #7"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 8.0, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "o3-mini score not in the current leaderboard data, estimated based on positioning"}], "metadata": {"lastUpdated": "2025-06-09T18:22:00Z", "dataSource": "OpenAI Platform Documentation, vals.ai AIME Benchmark, MATH 500 Benchmark Update", "version": "1.2"}}