import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "../ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "../ui/tabs";
import type { EnrichedModelData } from "./types";
import { ModelRecommendations } from "../recommendations/ModelRecommendations";
import {
  ModelBasicInfo,
  ModelTechnicalSpecs,
  ModelCapabilities,
  ModelPerformance,
  ModelPricing,
  ModelBenchmarks,
  ModelAvailability
} from "./detail-sections";
import { useTranslation, t } from "../../contexts/TranslationContext";
import type { Translations } from "../../contexts/TranslationContext";

// Define global window interface
declare global {
  interface Window {
    __TRANSLATIONS__?: Translations;
    __CURRENT_LANG__?: string;
  }
}

interface ModelDetailDialogProps {
  model: EnrichedModelData | null;
  isOpen: boolean;
  onClose: () => void;
  formatCurrency: (value: number | null | undefined) => string;
}

export function ModelDetailDialog({ model, isOpen, onClose, formatCurrency }: ModelDetailDialogProps) {
  const { translations } = useTranslation();

  // Use global translations as fallback if context translations are empty
  const getTranslation = (key: string, replacements: Record<string, string | number> = {}) => {
    // Check if context translations have data
    if (translations && Object.keys(translations).length > 0) {
      return t(translations, key, replacements);
    }
    
    // Fallback to global translations if available
    if (typeof window !== 'undefined' && window.__TRANSLATIONS__) {
      return t(window.__TRANSLATIONS__ as Translations, key, replacements);
    }
    
    // Last resort - return the key itself
    return key;
  };

  if (!model) return null;

  // Determine number of tabs based on available data
  const hasModelCard = !!model.modelCard;
  const hasRecommendations = hasModelCard;
  const hasAvailability = hasModelCard && model.modelCard?.availability;
  
  // Use fixed grid classes to avoid dynamic class issues
  const getGridClass = () => {
    const tabCount = 6 + (hasAvailability ? 1 : 0) + (hasRecommendations ? 1 : 0);
    switch (tabCount) {
      case 6: return "grid-cols-6";
      case 7: return "grid-cols-7";
      case 8: return "grid-cols-8";
      default: return "grid-cols-6";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl flex items-center gap-3">
            {model.name}
            <span className="text-sm bg-gray-100 px-2 py-1 rounded">
              {model.provider}
            </span>
            {model.modelCard?.basicInfo.status && (
              <span className={`text-xs px-2 py-1 rounded ${
                model.modelCard.basicInfo.status === 'GA' 
                  ? 'bg-green-100 text-green-800' 
                  : model.modelCard.basicInfo.status === 'Preview' 
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {model.modelCard.basicInfo.status}
              </span>
            )}
          </DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className={`grid w-full ${getGridClass()}`}>
            <TabsTrigger value="overview">{getTranslation('models.detail_dialog.tabs.overview')}</TabsTrigger>
            <TabsTrigger value="technical">{getTranslation('models.detail_dialog.tabs.technical')}</TabsTrigger>
            <TabsTrigger value="capabilities">{getTranslation('models.detail_dialog.tabs.capabilities')}</TabsTrigger>
            <TabsTrigger value="performance">{getTranslation('models.detail_dialog.tabs.performance')}</TabsTrigger>
            <TabsTrigger value="pricing">{getTranslation('models.detail_dialog.tabs.pricing')}</TabsTrigger>
            <TabsTrigger value="benchmarks">{getTranslation('models.detail_dialog.tabs.benchmarks')}</TabsTrigger>
            {hasAvailability && (
              <TabsTrigger value="availability">{getTranslation('models.detail_dialog.tabs.availability')}</TabsTrigger>
            )}
            {hasRecommendations && (
              <TabsTrigger value="recommendations">{getTranslation('models.detail_dialog.tabs.recommendations')}</TabsTrigger>
            )}
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <ModelBasicInfo model={model} />
          </TabsContent>
          
          <TabsContent value="technical" className="space-y-4">
            <ModelTechnicalSpecs model={model} />
          </TabsContent>
          
          <TabsContent value="capabilities" className="space-y-4">
            <ModelCapabilities model={model} />
          </TabsContent>
          
          <TabsContent value="performance" className="space-y-4">
            <ModelPerformance model={model} />
          </TabsContent>
          
          <TabsContent value="pricing" className="space-y-4">
            <ModelPricing model={model} formatCurrency={formatCurrency} />
          </TabsContent>
          
          <TabsContent value="benchmarks" className="space-y-4">
            <ModelBenchmarks model={model} />
          </TabsContent>
          
          {hasAvailability && (
            <TabsContent value="availability" className="space-y-4">
              <ModelAvailability model={model} />
            </TabsContent>
          )}
          
          {hasRecommendations && (
            <TabsContent value="recommendations" className="space-y-4">
              <ModelRecommendations model={model} />
            </TabsContent>
          )}
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}