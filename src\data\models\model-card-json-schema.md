# AI Model Card JSON Schema

Dieses Dokument definiert eine allgemeingültige JSON-Struktur zur Beschreibung von AI-Modellen basierend auf der Analyse von Google Cloud Vertex AI und Anthropic Model Cards.

## JSON Schema Struktur

```json
{
  "$schema": "https://json-schema.org/draft/2019-09/schema",
  "type": "object",
  "title": "AI Model Card Schema",
  "description": "Allgemeingültige Struktur zur Beschreibung von AI-Modellen",
  "properties": {
    "basicInfo": {
      "type": "object",
      "properties": {
        "modelId": {
          "type": "string",
          "description": "Eindeutige Modell-ID"
        },
        "displayName": {
          "type": "string",
          "description": "Anzeigename des Modells"
        },
        "provider": {
          "type": "string",
          "description": "Anbieter/Hersteller (z.B. Google, Anthropic, Meta, Mistral, OpenAI)"
        },
        "modelFamily": {
          "type": "string",
          "description": "Modell-Familie (<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, o1, o3)"
        },
        "version": {
          "type": "string",
          "description": "Modell-Version oder Snapshot-Datum"
        },
        "description": {
          "type": "string",
          "description": "Kurze Beschreibung der Modell-Fähigkeiten"
        },
        "releaseDate": {
          "type": "string",
          "format": "date",
          "description": "Veröffentlichungsdatum (YYYY-MM-DD)"
        },
        "status": {
          "type": "string",
          "enum": ["GA", "Preview", "Deprecated", "Beta"],
          "description": "Verfügbarkeitsstatus"
        },
        "knowledgeCutoff": {
          "type": "string",
          "description": "Wissensstichtag (z.B. 'März 2025', 'November 2024')"
        },
        "deprecationDate": {
          "type": "string",
          "format": "date",
          "description": "Datum der Deprecation-Ankündigung (YYYY-MM-DD) - nur relevant wenn status='Deprecated'"
        },
        "shutdownDate": {
          "type": "string",
          "format": "date",
          "description": "Geplantes Abschaltdatum (YYYY-MM-DD) - nur relevant wenn status='Deprecated'"
        }
      },
      "required": ["modelId", "displayName", "provider"]
    },
    "technicalSpecs": {
      "type": "object",
      "properties": {
        "contextWindow": {
          "type": "integer",
          "description": "Maximale Eingabe-Tokens"
        },
        "maxOutputTokens": {
          "type": "integer",
          "description": "Maximale Ausgabe-Tokens"
        },
        "maxReasoningTokens": {
          "type": "integer",
          "description": "Maximale Reasoning-Tokens (für o1/o3 Modelle)"
        },
        "maxCompletionTokens": {
          "type": "integer",
          "description": "Maximale Completion-Tokens (getrennt von Reasoning-Tokens)"
        },
        "architecture": {
          "type": "string",
          "description": "Modell-Architektur (z.B. MoE, Transformer)"
        },
        "parameterCount": {
          "type": "string",
          "description": "Anzahl Parameter (z.B. '90B', '17B-16E')"
        },
        "supportedInputTypes": {
          "type": "array",
          "items": {
            "type": "string",
            "enum": ["text", "image", "audio", "video", "document", "pdf"]
          },
          "description": "Unterstützte Eingabeformate"
        },
        "supportedOutputTypes": {
          "type": "array",
          "items": {
            "type": "string",
            "enum": ["text", "image", "audio"]
          },
          "description": "Unterstützte Ausgabeformate"
        },
        "inputLimitations": {
          "type": "object",
          "properties": {
            "maxImages": {
              "type": "integer",
              "description": "Maximale Anzahl Bilder pro Prompt"
            },
            "maxImageSize": {
              "type": "string",
              "description": "Maximale Bildgröße (z.B. '7 MB')"
            },
            "maxAudioLength": {
              "type": "string",
              "description": "Maximale Audiolänge (z.B. '8.4 Stunden')"
            },
            "maxVideoLength": {
              "type": "string",
              "description": "Maximale Videolänge (z.B. '45 Minuten')"
            },
            "supportedMimeTypes": {
              "type": "object",
              "properties": {
                "image": {
                  "type": "array",
                  "items": {"type": "string"}
                },
                "audio": {
                  "type": "array",
                  "items": {"type": "string"}
                },
                "video": {
                  "type": "array",
                  "items": {"type": "string"}
                },
                "document": {
                  "type": "array",
                  "items": {"type": "string"}
                }
              }
            }
          }
        }
      },
      "required": ["contextWindow", "maxOutputTokens"]
    },
    "capabilities": {
      "type": "object",
      "properties": {
        "functionCalling": {
          "type": "boolean",
          "description": "Unterstützt Funktionsaufrufe"
        },
        "vision": {
          "type": "boolean",
          "description": "Unterstützt Bildverarbeitung"
        },
        "pdfSupport": {
          "type": "boolean",
          "description": "Unterstützt PDF-Verarbeitung"
        },
        "audioInput": {
          "type": "boolean",
          "description": "Unterstützt Audio-Eingabe"
        },
        "audioOutput": {
          "type": "boolean",
          "description": "Unterstützt Audio-Ausgabe"
        },
        "imageGeneration": {
          "type": "boolean",
          "description": "Unterstützt Bildgenerierung"
        },
        "codeExecution": {
          "type": "boolean",
          "description": "Unterstützt Code-Ausführung"
        },
        "systemInstructions": {
          "type": "boolean",
          "description": "Unterstützt System-Anweisungen"
        },
        "promptCaching": {
          "type": "boolean",
          "description": "Unterstützt Prompt-Caching"
        },
        "batchProcessing": {
          "type": "boolean",
          "description": "Unterstützt Batch-Verarbeitung"
        },
        "reasoning": {
          "type": "boolean",
          "description": "Unterstützt erweiterte Reasoning-Fähigkeiten"
        },
        "thinking": {
          "type": "boolean",
          "description": "Unterstützt Extended Thinking"
        },
        "grounding": {
          "type": "boolean",
          "description": "Unterstützt Grounding (z.B. Google Search)"
        },
        "multilingualSupport": {
          "type": "boolean",
          "description": "Unterstützt mehrere Sprachen"
        },
        "embeddingImageInput": {
          "type": "boolean",
          "description": "Unterstützt Embedding von Bildern"
        },
        "structuredOutputs": {
          "type": "boolean",
          "description": "Unterstützt strukturierte JSON-Ausgaben"
        },
        "webBrowsing": {
          "type": "boolean",
          "description": "Unterstützt Web-Browsing"
        },
        "codeInterpreter": {
          "type": "boolean",
          "description": "Unterstützt Code-Interpreter/Advanced Data Analysis"
        },
        "dalleIntegration": {
          "type": "boolean",
          "description": "Unterstützt DALL-E Bildgenerierung"
        },
        "realTimeAPI": {
          "type": "boolean",
          "description": "Unterstützt Real-time API"
        },
        "liteLLM-provisioning": {
          "type": "boolean",
          "description": "Gibt an, ob das Modell über LiteLLM bereitgestellt wird",
          "default": true
        }
      }
    },
    "performance": {
      "type": "object",
      "properties": {
        "latency": {
          "type": "string",
          "enum": ["Fastest", "Fast", "Moderately Fast", "Slow"],
          "description": "Relative Latenz-Kategorie"
        },
        "rateLimits": {
          "type": "object",
          "properties": {
            "queriesPerMinute": {
              "type": "integer",
              "description": "Abfragen pro Minute"
            },
            "tokensPerMinute": {
              "type": "integer",
              "description": "Tokens pro Minute"
            },
            "inputTokensPerMinute": {
              "type": "integer",
              "description": "Eingabe-Tokens pro Minute"
            },
            "outputTokensPerMinute": {
              "type": "integer",
              "description": "Ausgabe-Tokens pro Minute"
            },
            "reasoningTokensPerMinute": {
              "type": "integer",
              "description": "Reasoning-Tokens pro Minute (für o1/o3 Modelle)"
            },
            "batchQueueLimit": {
              "type": "integer",
              "description": "Maximale Anzahl Batch-Jobs in der Warteschlange"
            }
          }
        },
        "reasoningPerformance": {
          "type": "object",
          "properties": {
            "averageReasoningTime": {
              "type": "string",
              "description": "Durchschnittliche Reasoning-Zeit (z.B. '10-30 Sekunden')"
            },
            "maxReasoningTime": {
              "type": "string",
              "description": "Maximale Reasoning-Zeit"
            },
            "reasoningEfficiency": {
              "type": "string",
              "enum": ["High", "Medium", "Standard"],
              "description": "Reasoning-Effizienz-Kategorie"
            }
          }
        },
        "temperature": {
          "type": "object",
          "properties": {
            "min": {"type": "number"},
            "max": {"type": "number"},
            "default": {"type": "number"}
          }
        },
        "topP": {
          "type": "number",
          "description": "Standard Top-P Wert"
        },
        "topK": {
          "type": "integer",
          "description": "Standard Top-K Wert"
        }
      }
    },
    "pricing": {
      "type": "object",
      "properties": {
        "inputCostPerToken": {
          "type": "number",
          "description": "Kosten pro Input-Token"
        },
        "outputCostPerToken": {
          "type": "number",
          "description": "Kosten pro Output-Token"
        },
        "inputCostPer1MTokens": {
          "type": "number",
          "description": "Kosten pro 1 Million Input-Tokens"
        },
        "outputCostPer1MTokens": {
          "type": "number",
          "description": "Kosten pro 1 Million Output-Tokens"
        },
        "cachingCosts": {
          "type": "object",
          "properties": {
            "cacheWrites": {
              "type": "number",
              "description": "Kosten für Cache-Writes pro 1M Tokens"
            },
            "cacheHits": {
              "type": "number",
              "description": "Kosten für Cache-Hits pro 1M Tokens"
            }
          }
        },
        "reasoningCosts": {
          "type": "object",
          "properties": {
            "reasoningTokensPerMillion": {
              "type": "number",
              "description": "Kosten pro 1M Reasoning-Tokens"
            },
            "completionTokensPerMillion": {
              "type": "number",
              "description": "Kosten pro 1M Completion-Tokens (getrennt von Reasoning)"
            }
          }
        },
        "batchProcessingCosts": {
          "type": "object",
          "properties": {
            "inputCostPer1MTokens": {
              "type": "number",
              "description": "Batch-Kosten pro 1M Input-Tokens (oft 50% Rabatt)"
            },
            "outputCostPer1MTokens": {
              "type": "number",
              "description": "Batch-Kosten pro 1M Output-Tokens (oft 50% Rabatt)"
            }
          }
        },
        "currency": {
          "type": "string",
          "default": "USD",
          "description": "Währung der Preisangaben"
        }
      }
    },
    "availability": {
      "type": "object",
      "properties": {
        "regions": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "region": {
                "type": "string",
                "description": "Region (z.B. us-central1, europe-west1)"
              },
              "availability": {
                "type": "string",
                "enum": ["GA", "Preview", "Limited"],
                "description": "Verfügbarkeitsstatus in dieser Region"
              }
            }
          }
        },
        "dataProcessingRegions": {
          "type": "array",
          "items": {"type": "string"},
          "description": "Regionen für ML-Verarbeitung"
        },
        "supportedPlatforms": {
          "type": "array",
          "items": {
            "type": "string",
            "enum": ["Vertex AI", "AWS Bedrock", "Anthropic API", "Azure OpenAI", "OpenAI API", "OpenAI Batch API"]
          },
          "description": "Unterstützte Plattformen"
        }
      }
    },
    "security": {
      "type": "object",
      "properties": {
        "dataResidency": {
          "type": "boolean",
          "description": "Unterstützt Datenstandort-Kontrolle"
        },
        "cmekSupport": {
          "type": "boolean",
          "description": "Customer Managed Encryption Keys"
        },
        "vpcSupport": {
          "type": "boolean",
          "description": "VPC Service Controls"
        },
        "accessTransparency": {
          "type": "boolean",
          "description": "Access Transparency"
        },
        "complianceStandards": {
          "type": "array",
          "items": {"type": "string"},
          "description": "Compliance-Standards (z.B. SOC2, GDPR)"
        }
      }
    },
    "usageTypes": {
      "type": "object",
      "properties": {
        "dynamicSharedQuota": {
          "type": "boolean",
          "description": "Unterstützt dynamisches geteiltes Kontingent"
        },
        "provisionedThroughput": {
          "type": "boolean",
          "description": "Unterstützt bereitgestellten Durchsatz"
        },
        "fixedQuota": {
          "type": "boolean",
          "description": "Unterstützt feste Kontingente"
        }
      }
    },
    "benchmarks": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "benchmarkName": {
            "type": "string",
            "description": "Name des Benchmarks"
          },
          "category": {
            "type": "string",
            "enum": ["Reasoning & Knowledge", "Science", "Mathematics", "Code generation", "Code editing", "Agentic coding", "Factuality", "Visual reasoning", "Image understanding", "Long context", "Multilingual performance"],
            "description": "Benchmark-Kategorie"
          },
          "score": {
            "type": "number",
            "description": "Benchmark-Score (Hauptwert)"
          },
          "alternativeScores": {
            "type": "object",
            "description": "Alternative oder zusätzliche Scores",
            "properties": {
              "multipleAttempts": {
                "type": "number",
                "description": "Score bei mehreren Versuchen"
              },
              "whole": {
                "type": "number",
                "description": "Whole-file Score (für Code-Benchmarks)"
              },
              "diff": {
                "type": "number",
                "description": "Diff-based Score (für Code-Benchmarks)"
              },
              "average": {
                "type": "number",
                "description": "Durchschnittswert"
              },
              "pointwise": {
                "type": "number",
                "description": "Pointwise-Bewertung"
              }
            }
          },
          "metric": {
            "type": "string",
            "description": "Metrik (z.B. 'Pass Rate', 'Accuracy', 'Success Rate')"
          },
          "attemptType": {
            "type": "string",
            "enum": ["single attempt", "multiple attempts", "pass@1", "average", "pointwise"],
            "description": "Art der Bewertung"
          },
          "contextLength": {
            "type": "string",
            "description": "Kontext-Länge für den Test (z.B. '128k', '1M')"
          },
          "toolsUsed": {
            "type": "boolean",
            "description": "Ob Tools/Hilfsmittel verwendet wurden"
          },
          "date": {
            "type": "string",
            "format": "date",
            "description": "Datum der Messung"
          },
          "notes": {
            "type": "string",
            "description": "Zusätzliche Anmerkungen oder Konfiguration"
          }
        },
        "required": ["benchmarkName", "score", "metric"]
      }
    },
    "metadata": {
      "type": "object",
      "properties": {
        "lastUpdated": {
          "type": "string",
          "format": "date-time",
          "description": "Letzte Aktualisierung der Model Card"
        },
        "dataSource": {
          "type": "string",
          "description": "Quelle der Informationen"
        },
        "version": {
          "type": "string",
          "description": "Schema-Version"
        }
      }
    }
  },
  "required": ["basicInfo", "technicalSpecs", "capabilities"]
}
```

## Beispiel: Claude Opus 4

```json
{
  "basicInfo": {
    "modelId": "claude-opus-4-20250514",
    "displayName": "Claude Opus 4",
    "provider": "Anthropic",
    "modelFamily": "Claude",
    "version": "20250514",
    "description": "Das bisher leistungsstärkste Modell von Anthropic und das modernste Codierungsmodell",
    "releaseDate": "2025-05-22",
    "status": "GA",
    "knowledgeCutoff": "November 2024"
  },
  "technicalSpecs": {
    "contextWindow": 200000,
    "maxOutputTokens": 32000,
    "supportedInputTypes": ["text", "image", "document"],
    "supportedOutputTypes": ["text"],
    "inputLimitations": {
      "supportedMimeTypes": {
        "image": ["image/jpeg", "image/png", "image/gif", "image/webp"],
        "document": ["application/pdf", "text/plain"]
      }
    }
  },
  "capabilities": {
    "functionCalling": true,
    "vision": true,
    "pdfSupport": true,
    "audioInput": false,
    "audioOutput": false,
    "imageGeneration": false,
    "codeExecution": false,
    "systemInstructions": true,
    "promptCaching": true,
    "batchProcessing": true,
    "reasoning": true,
    "thinking": true,
    "grounding": false,
    "multilingualSupport": true,
    "embeddingImageInput": false
  },
  "performance": {
    "latency": "Moderately Fast",
    "rateLimits": {
      "queriesPerMinute": 25,
      "inputTokensPerMinute": 60000,
      "outputTokensPerMinute": 6000
    },
    "temperature": {
      "min": 0,
      "max": 1,
      "default": 0.7
    }
  },
  "pricing": {
    "inputCostPer1MTokens": 15.0,
    "outputCostPer1MTokens": 75.0,
    "cachingCosts": {
      "cacheWrites": 18.75,
      "cacheHits": 1.50
    },
    "currency": "USD"
  },
  "availability": {
    "regions": [
      {
        "region": "us-east5",
        "availability": "GA"
      }
    ],
    "dataProcessingRegions": ["Multi-region"],
    "supportedPlatforms": ["Vertex AI", "AWS Bedrock", "Anthropic API"]
  },
  "security": {
    "dataResidency": true,
    "cmekSupport": true,
    "vpcSupport": true,
    "accessTransparency": true,
    "complianceStandards": ["SOC2", "GDPR"]
  },
  "usageTypes": {
    "dynamicSharedQuota": true,
    "provisionedThroughput": true,
    "fixedQuota": true
  },
  "benchmarks": [
    {
      "benchmarkName": "Aider-Polyglot",
      "category": "Code editing",
      "score": 75.2,
      "alternativeScores": {
        "whole": 75.2,
        "diff": 68.5
      },
      "metric": "Pass Rate",
      "attemptType": "single attempt",
      "date": "2025-05-20",
      "notes": "Whole file vs diff-based editing"
    },
    {
      "benchmarkName": "MMMU",
      "category": "Visual reasoning",
      "score": 84.1,
      "metric": "Accuracy",
      "attemptType": "single attempt",
      "date": "2025-05-20"
    }
  ],
  "metadata": {
    "lastUpdated": "2025-06-08T10:11:20Z",
    "dataSource": "Google Cloud Vertex AI Documentation",
    "version": "1.0"
  }
}
```

## Beispiel: Gemini 2.0 Flash

```json
{
  "basicInfo": {
    "modelId": "gemini-2.0-flash-001",
    "displayName": "Gemini 2.0 Flash",
    "provider": "Google",
    "modelFamily": "Gemini",
    "version": "001",
    "description": "Funktionen der nächsten Generation und verbesserte Fähigkeiten, darunter höhere Geschwindigkeit, integrierte Tools und multimodale Generierung",
    "releaseDate": "2025-02-05",
    "status": "GA",
    "knowledgeCutoff": "Juni 2024"
  },
  "technicalSpecs": {
    "contextWindow": 1048576,
    "maxOutputTokens": 8192,
    "supportedInputTypes": ["text", "image", "audio", "video", "document"],
    "supportedOutputTypes": ["text"],
    "inputLimitations": {
      "maxImages": 3000,
      "maxImageSize": "7 MB",
      "maxAudioLength": "8.4 Stunden",
      "maxVideoLength": "45 Minuten",
      "supportedMimeTypes": {
        "image": ["image/png", "image/jpeg", "image/webp"],
        "audio": ["audio/x-aac", "audio/flac", "audio/mp3", "audio/m4a", "audio/mpeg", "audio/opus", "audio/wav"],
        "video": ["video/mp4", "video/webm", "video/quicktime", "video/mpeg"],
        "document": ["application/pdf", "text/plain"]
      }
    }
  },
  "capabilities": {
    "functionCalling": true,
    "vision": true,
    "pdfSupport": true,
    "audioInput": true,
    "audioOutput": false,
    "imageGeneration": false,
    "codeExecution": true,
    "systemInstructions": true,
    "promptCaching": false,
    "batchProcessing": true,
    "reasoning": false,
    "thinking": false,
    "grounding": true,
    "multilingualSupport": true,
    "embeddingImageInput": false
  },
  "performance": {
    "latency": "Fast",
    "rateLimits": {
      "tokensPerMinute": 40000000
    },
    "temperature": {
      "min": 0,
      "max": 2,
      "default": 1.0
    },
    "topP": 0.95,
    "topK": 64
  },
  "pricing": {
    "inputCostPer1MTokens": 1.25,
    "outputCostPer1MTokens": 5.0,
    "currency": "USD"
  },
  "availability": {
    "regions": [
      {
        "region": "global",
        "availability": "GA"
      },
      {
        "region": "us-central1",
        "availability": "GA"
      },
      {
        "region": "europe-west1",
        "availability": "GA"
      }
    ],
    "dataProcessingRegions": ["Multi-region"],
    "supportedPlatforms": ["Vertex AI"]
  },
  "security": {
    "dataResidency": true,
    "cmekSupport": true,
    "vpcSupport": true,
    "accessTransparency": true
  },
  "usageTypes": {
    "dynamicSharedQuota": true,
    "provisionedThroughput": true,
    "fixedQuota": false
  },
  "metadata": {
    "lastUpdated": "2025-06-08T10:11:20Z",
    "dataSource": "Google Cloud Vertex AI Documentation",
    "version": "1.0"
  }
}
```

## Beispiel: OpenAI o1

```json
{
  "basicInfo": {
    "modelId": "o1-preview-2024-09-12",
    "displayName": "o1-preview",
    "provider": "OpenAI",
    "modelFamily": "o1",
    "version": "2024-09-12",
    "description": "Ein neues großes Sprachmodell, das für komplexe Reasoning-Aufgaben trainiert wurde",
    "releaseDate": "2024-09-12",
    "status": "Preview",
    "knowledgeCutoff": "Oktober 2023"
  },
  "technicalSpecs": {
    "contextWindow": 128000,
    "maxOutputTokens": 32768,
    "maxReasoningTokens": 65536,
    "maxCompletionTokens": 32768,
    "supportedInputTypes": ["text"],
    "supportedOutputTypes": ["text"]
  },
  "capabilities": {
    "functionCalling": false,
    "vision": false,
    "pdfSupport": false,
    "audioInput": false,
    "audioOutput": false,
    "imageGeneration": false,
    "codeExecution": false,
    "systemInstructions": false,
    "promptCaching": false,
    "batchProcessing": true,
    "reasoning": true,
    "thinking": true,
    "grounding": false,
    "multilingualSupport": true,
    "embeddingImageInput": false,
    "structuredOutputs": false,
    "webBrowsing": false,
    "codeInterpreter": false,
    "dalleIntegration": false,
    "realTimeAPI": false
  },
  "performance": {
    "latency": "Slow",
    "rateLimits": {
      "queriesPerMinute": 20,
      "reasoningTokensPerMinute": 30000,
      "outputTokensPerMinute": 6000
    },
    "reasoningPerformance": {
      "averageReasoningTime": "10-30 Sekunden",
      "maxReasoningTime": "120 Sekunden",
      "reasoningEfficiency": "High"
    }
  },
  "pricing": {
    "inputCostPer1MTokens": 15.0,
    "outputCostPer1MTokens": 60.0,
    "reasoningCosts": {
      "reasoningTokensPerMillion": 15.0,
      "completionTokensPerMillion": 60.0
    },
    "currency": "USD"
  },
  "availability": {
    "supportedPlatforms": ["OpenAI API", "Azure OpenAI"]
  },
  "benchmarks": [
    {
      "benchmarkName": "AIME 2024",
      "category": "Mathematics",
      "score": 83.3,
      "metric": "Pass Rate",
      "attemptType": "single attempt",
      "date": "2024-09-12"
    },
    {
      "benchmarkName": "GPQA Diamond",
      "category": "Science",
      "score": 78.0,
      "metric": "Pass Rate",
      "attemptType": "single attempt",
      "date": "2024-09-12"
    }
  ],
  "metadata": {
    "lastUpdated": "2025-06-08T10:41:00Z",
    "dataSource": "OpenAI Documentation",
    "version": "1.0"
  }
}
```

## Beispiel: OpenAI GPT-4o

```json
{
  "basicInfo": {
    "modelId": "gpt-4o-2024-08-06",
    "displayName": "GPT-4o",
    "provider": "OpenAI",
    "modelFamily": "GPT",
    "version": "2024-08-06",
    "description": "Das neueste multimodale Flaggschiff-Modell von OpenAI, das günstiger und schneller ist als GPT-4 Turbo",
    "releaseDate": "2024-05-13",
    "status": "GA",
    "knowledgeCutoff": "Oktober 2023"
  },
  "technicalSpecs": {
    "contextWindow": 128000,
    "maxOutputTokens": 16384,
    "supportedInputTypes": ["text", "image", "audio"],
    "supportedOutputTypes": ["text", "audio"],
    "inputLimitations": {
      "maxImages": 50,
      "supportedMimeTypes": {
        "image": ["image/jpeg", "image/png", "image/gif", "image/webp"],
        "audio": ["audio/wav", "audio/mp3", "audio/flac", "audio/m4a", "audio/ogg"]
      }
    }
  },
  "capabilities": {
    "functionCalling": true,
    "vision": true,
    "pdfSupport": true,
    "audioInput": true,
    "audioOutput": true,
    "imageGeneration": false,
    "codeExecution": false,
    "systemInstructions": true,
    "promptCaching": false,
    "batchProcessing": true,
    "reasoning": false,
    "thinking": false,
    "grounding": false,
    "multilingualSupport": true,
    "embeddingImageInput": false,
    "structuredOutputs": true,
    "webBrowsing": true,
    "codeInterpreter": true,
    "dalleIntegration": true,
    "realTimeAPI": true
  },
  "performance": {
    "latency": "Fast",
    "rateLimits": {
      "queriesPerMinute": 10000,
      "tokensPerMinute": 30000000,
      "batchQueueLimit": 100000
    },
    "temperature": {
      "min": 0,
      "max": 2,
      "default": 1.0
    },
    "topP": 1.0
  },
  "pricing": {
    "inputCostPer1MTokens": 2.50,
    "outputCostPer1MTokens": 10.00,
    "batchProcessingCosts": {
      "inputCostPer1MTokens": 1.25,
      "outputCostPer1MTokens": 5.00
    },
    "currency": "USD"
  },
  "availability": {
    "supportedPlatforms": ["OpenAI API", "Azure OpenAI", "OpenAI Batch API"]
  },
  "benchmarks": [
    {
      "benchmarkName": "MMMU",
      "category": "Visual reasoning",
      "score": 69.1,
      "metric": "Accuracy",
      "attemptType": "single attempt",
      "date": "2024-05-13"
    },
    {
      "benchmarkName": "HumanEval",
      "category": "Code generation",
      "score": 90.2,
      "metric": "Pass Rate",
      "attemptType": "pass@1",
      "date": "2024-05-13"
    }
  ],
  "metadata": {
    "lastUpdated": "2025-06-08T10:41:00Z",
    "dataSource": "OpenAI Documentation",
    "version": "1.0"
  }
}
```

## Beispiel: OpenAI o1-mini

```json
{
  "basicInfo": {
    "modelId": "o1-mini-2024-09-12",
    "displayName": "o1-mini",
    "provider": "OpenAI",
    "modelFamily": "o1",
    "version": "2024-09-12",
    "description": "Eine kosteneffiziente Reasoning-Modell, das besonders für Coding-, Mathematik- und Wissenschaftsaufgaben geeignet ist",
    "releaseDate": "2024-09-12",
    "status": "GA",
    "knowledgeCutoff": "Oktober 2023"
  },
  "technicalSpecs": {
    "contextWindow": 128000,
    "maxOutputTokens": 65536,
    "maxReasoningTokens": 65536,
    "maxCompletionTokens": 65536,
    "supportedInputTypes": ["text"],
    "supportedOutputTypes": ["text"]
  },
  "capabilities": {
    "functionCalling": false,
    "vision": false,
    "pdfSupport": false,
    "audioInput": false,
    "audioOutput": false,
    "imageGeneration": false,
    "codeExecution": false,
    "systemInstructions": false,
    "promptCaching": false,
    "batchProcessing": true,
    "reasoning": true,
    "thinking": true,
    "grounding": false,
    "multilingualSupport": true,
    "embeddingImageInput": false,
    "structuredOutputs": false,
    "webBrowsing": false,
    "codeInterpreter": false,
    "dalleIntegration": false,
    "realTimeAPI": false
  },
  "performance": {
    "latency": "Moderately Fast",
    "rateLimits": {
      "queriesPerMinute": 50,
      "reasoningTokensPerMinute": 200000,
      "outputTokensPerMinute": 40000
    },
    "reasoningPerformance": {
      "averageReasoningTime": "5-15 Sekunden",
      "maxReasoningTime": "60 Sekunden",
      "reasoningEfficiency": "Medium"
    }
  },
  "pricing": {
    "inputCostPer1MTokens": 3.0,
    "outputCostPer1MTokens": 12.0,
    "reasoningCosts": {
      "reasoningTokensPerMillion": 3.0,
      "completionTokensPerMillion": 12.0
    },
    "currency": "USD"
  },
  "availability": {
    "supportedPlatforms": ["OpenAI API", "Azure OpenAI"]
  },
  "benchmarks": [
    {
      "benchmarkName": "Codeforces",
      "category": "Code generation",
      "score": 1650,
      "metric": "Elo Rating",
      "attemptType": "single attempt",
      "date": "2024-09-12"
    },
    {
      "benchmarkName": "AIME 2024",
      "category": "Mathematics",
      "score": 70.0,
      "metric": "Pass Rate",
      "attemptType": "single attempt",
      "date": "2024-09-12"
    }
  ],
  "metadata": {
    "lastUpdated": "2025-06-08T10:41:00Z",
    "dataSource": "OpenAI Documentation",
    "version": "1.0"
  }
}
```

## Beispiel: Deprecated Model (z.B. GPT-3.5 Turbo Legacy)

```json
{
  "basicInfo": {
    "modelId": "gpt-3.5-turbo-0613",
    "displayName": "GPT-3.5 Turbo (Legacy)",
    "provider": "OpenAI",
    "modelFamily": "GPT",
    "version": "0613",
    "description": "Legacy-Version von GPT-3.5 Turbo - wird eingestellt",
    "releaseDate": "2023-06-13",
    "status": "Deprecated",
    "knowledgeCutoff": "September 2021",
    "deprecationDate": "2024-07-15",
    "shutdownDate": "2024-09-13"
  },
  "technicalSpecs": {
    "contextWindow": 4096,
    "maxOutputTokens": 4096,
    "supportedInputTypes": ["text"],
    "supportedOutputTypes": ["text"]
  },
  "capabilities": {
    "functionCalling": true,
    "vision": false,
    "pdfSupport": false,
    "audioInput": false,
    "audioOutput": false,
    "imageGeneration": false,
    "codeExecution": false,
    "systemInstructions": false,
    "promptCaching": false,
    "batchProcessing": false,
    "reasoning": false,
    "thinking": false,
    "grounding": false,
    "multilingualSupport": true,
    "embeddingImageInput": false,
    "structuredOutputs": false,
    "webBrowsing": false,
    "codeInterpreter": false,
    "dalleIntegration": false,
    "realTimeAPI": false
  },
  "pricing": {
    "inputCostPer1MTokens": 1.5,
    "outputCostPer1MTokens": 2.0,
    "currency": "USD"
  },
  "availability": {
    "supportedPlatforms": ["OpenAI API"]
  },
  "metadata": {
    "lastUpdated": "2024-07-15T00:00:00Z",
    "dataSource": "OpenAI Documentation",
    "version": "1.0"
  }
}
```

## Anwendungshinweise

### 1. Pflichtfelder
- `basicInfo`: Minimale Identifikationsinformationen
- `technicalSpecs`: Grundlegende technische Spezifikationen
- `capabilities`: Boolean-Flags für unterstützte Features

### 2. Optionale Abschnitte
- `performance`: Leistungskennzahlen und Limits
- `pricing`: Kostenstruktur
- `availability`: Regionale Verfügbarkeit
- `security`: Sicherheitsfeatures
- `benchmarks`: Benchmark-Ergebnisse
- `metadata`: Verwaltungsinformationen

### 3. OpenAI-spezifische Felder
- `maxReasoningTokens`: Für o1/o3 Modelle mit separaten Reasoning-Tokens
- `maxCompletionTokens`: Getrennte Completion-Tokens von Reasoning-Tokens
- `reasoningPerformance`: Leistungsmetriken für Reasoning-Modelle (Zeiten und Effizienz)
- `reasoningCosts`: Separate Kostenstruktur für Reasoning vs. Completion-Tokens
- `batchProcessingCosts`: Reduzierte Kosten für Batch-Verarbeitung (oft 50% Rabatt)
- Neue Capabilities: `structuredOutputs`, `webBrowsing`, `codeInterpreter`, `dalleIntegration`, `realTimeAPI`
- Erweiterte Rate Limits: `reasoningTokensPerMinute`, `batchQueueLimit`

### 4. Erweiterbarkeit
Das Schema ist erweiterbar und kann bei Bedarf um neue Properties ergänzt werden, ohne bestehende Implementierungen zu brechen.

### 5. Lifecycle-Management für Deprecated Modelle
- `status`: "Deprecated" kennzeichnet Modelle, die eingestellt werden
- `deprecationDate`: Datum der offiziellen Deprecation-Ankündigung
- `shutdownDate`: Geplantes Datum der endgültigen Abschaltung
- Beide Datumsfelder verwenden das ISO 8601 Format (YYYY-MM-DD)
- Diese Felder sind nur relevant, wenn `status` auf "Deprecated" gesetzt ist
- Ermöglicht Planungssicherheit für Entwickler und automatisierte Migrationsstrategien

### 6. Standardisierung
- Preise immer in USD pro 1M Tokens
- Datums-/Zeitangaben im ISO 8601 Format
- Boolean-Werte für binäre Capabilities
- Enum-Werte für kategorische Eigenschaften
- Reasoning-Zeiten in verständlichen Einheiten (Sekunden/Minuten)

### 7. Validierung
Das JSON Schema kann zur Validierung von Model Card Instanzen verwendet werden, um Konsistenz und Vollständigkeit sicherzustellen. Bei deprecated Modellen sollte zusätzlich validiert werden:
- Wenn `status` = "Deprecated", sollten `deprecationDate` und `shutdownDate` gesetzt sein
- `shutdownDate` sollte nach `deprecationDate` liegen
- Beide Datumsfelder sollten im korrekten ISO 8601 Format vorliegen

## Benchmark-Kategorien und Metriken

### Kategorien-Übersicht

**Reasoning & Knowledge**
- Tests für logisches Denken und Wissensbewertung
- Beispiele: Humanity's Last Exam
- Metriken: Pass Rate, Accuracy

**Science**
- Naturwissenschaftliche Fragen und Problemlösung
- Beispiele: GPQA diamond
- Metriken: Pass Rate

**Mathematics**
- Mathematische Problemlösung und Berechnungen
- Beispiele: AIME 2024/2025
- Metriken: Pass Rate
- Besonderheiten: Oft single vs. multiple attempts

**Code generation**
- Generierung von Code basierend auf Beschreibungen
- Beispiele: LiveCodeBench v5
- Metriken: Pass Rate, Success Rate

**Code editing**
- Bearbeitung und Verbesserung bestehenden Codes
- Beispiele: Aider-Polyglot
- Metriken: Pass Rate
- Besonderheiten: Whole-file vs. diff-based editing

**Agentic coding**
- Komplexe Software-Engineering-Aufgaben
- Beispiele: SWE-bench verified
- Metriken: Success Rate

**Factuality**
- Korrektheit und Faktentreue der Antworten
- Beispiele: SimpleQA
- Metriken: Accuracy

**Visual reasoning**
- Bildverständnis und visuelle Logik
- Beispiele: MMMU
- Metriken: Accuracy
- Besonderheiten: single vs. multiple attempts

**Image understanding**
- Grundlegendes Bildverständnis
- Beispiele: Vibe-Eval (Reka)
- Metriken: Accuracy

**Long context**
- Verarbeitung langer Texte und Kontexte
- Beispiele: MRCR
- Metriken: Accuracy
- Besonderheiten: Verschiedene Kontext-Längen (128k, 1M), average vs. pointwise

**Multilingual performance**
- Mehrsprachige Fähigkeiten
- Beispiele: Global MMLU (Lite)
- Metriken: Accuracy

### Bewertungsarten (attemptType)

- **single attempt**: Ein Versuch pro Aufgabe
- **multiple attempts**: Mehrere Versuche erlaubt, bester Wert zählt
- **pass@1**: Erfolgsrate beim ersten Versuch
- **average**: Durchschnittswert über mehrere Tests
- **pointwise**: Punkt-für-Punkt-Bewertung

### Alternative Scores

Viele Benchmarks bieten zusätzliche Bewertungsmethoden:
- **whole**: Vollständige Datei-Bearbeitung
- **diff**: Nur geänderte Teile bewertet
- **multipleAttempts**: Score bei mehreren Versuchen
- **average**: Durchschnittswert
- **pointwise**: Detaillierte Punkt-Bewertung