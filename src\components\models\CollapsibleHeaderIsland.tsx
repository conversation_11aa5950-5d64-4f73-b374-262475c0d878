import { TranslationProvider, type Translations } from "../../contexts/TranslationContext";
import { CollapsibleHeader } from "./CollapsibleHeader";
import { useEffect, useState } from "react";

export interface CollapsibleHeaderIslandProps {
  titleKey?: string;
  descriptionKey?: string;
  totalModels?: number;
  totalProviders?: number;
  totalBenchmarks?: number;
  title?: string; // fallback for legacy usage
  description?: string; // fallback for legacy usage
  isCollapsedByDefault?: boolean;
  children: React.ReactNode;
}

export default function CollapsibleHeaderIsland(props: CollapsibleHeaderIslandProps) {
  const [initialTranslations, setInitialTranslations] = useState<Translations>({});
  const [initialLang, setInitialLang] = useState("de");

  useEffect(() => {
    // Get initial translations from global variables
    if (typeof window !== "undefined") {
      const globalTranslations = (window as any).__TRANSLATIONS__ || (window as any).translations || {};
      const globalLang = (window as any).__CURRENT_LANG__ || (window as any).currentLang || "de";
      
      setInitialTranslations(globalTranslations);
      setInitialLang(globalLang);
    }
  }, []);

  return (
    <TranslationProvider
      initialTranslations={initialTranslations}
      initialLang={initialLang}
    >
      <CollapsibleHeader {...props} />
    </TranslationProvider>
  );
}