{"basicInfo": {"modelId": "gpt-4.1-mini-2025-04-14", "displayName": "GPT-4.1 mini", "provider": "OpenAI", "modelFamily": "GPT", "version": "2025-04-14", "description": "Cost-effective version of GPT-4.1 with reduced capabilities for everyday tasks. Balances performance and affordability for high-volume usage.", "releaseDate": "2025-04-14", "status": "GA", "knowledgeCutoff": "Juni 2024"}, "technicalSpecs": {"contextWindow": 1047576, "maxOutputTokens": 32768, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": false, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": true, "codeInterpreter": true, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": false}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 500, "tokensPerMinute": 150000, "batchQueueLimit": 1000000}, "temperature": {"min": 0, "max": 2, "default": 1.0}}, "pricing": {"inputCostPer1MTokens": 0.5, "outputCostPer1MTokens": 2.0, "cachingCosts": {"cacheHits": 0.125}, "batchProcessingCosts": {"inputCostPer1MTokens": 0.25, "outputCostPer1MTokens": 1.0}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "OpenAI Batch API"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 87.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Massive Multitask Language Understanding"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 78.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multilingual Q&A across 14 languages"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 65.0, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Graduate-level reasoning in science"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 49.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "American Invitational Mathematics Examination"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 73.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Visual mathematical tasks"}, {"benchmarkName": "CharXiv", "category": "Visual reasoning", "score": 56.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Questions about charts from scientific papers"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 72.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multimodal understanding and reasoning"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 23.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Real software engineering tasks"}, {"benchmarkName": "Internal API instruction following (hard)", "category": "Instruction following", "score": 45.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Hard subset of instruction following eval"}, {"benchmarkName": "MultiChallenge", "category": "Instruction following", "score": 35.8, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multi-turn instruction following"}, {"benchmarkName": "IFEval", "category": "Instruction following", "score": 84.1, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Instruction following with verifiable instructions"}, {"benchmarkName": "Multi-IF", "category": "Instruction following", "score": 67.0, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multi-instruction following benchmark"}, {"benchmarkName": "Graphwalks BFS <128k accuracy", "category": "Long context", "score": 61.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Multi-round co-reference resolution in langen Kontexten"}, {"benchmarkName": "ComplexFuncBench", "category": "Function calling", "score": 49.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Complex function calling benchmark"}, {"benchmarkName": "TAU-bench Airline", "category": "Agentic coding", "score": 36.0, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Agentic tool use in airline scenarios"}, {"benchmarkName": "TAU-bench Retail", "category": "Agentic coding", "score": 55.8, "metric": "Success Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Agentic tool use in retail scenarios"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 34.7, "alternativeScores": {"diff": 31.6}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-14", "notes": "Aider polyglot benchmark - whole: 34.7%, diff: 31.6%"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 47.1, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 3.2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "GPT-4.1 mini score estimated based on positioning relative to other GPT models"}], "metadata": {"lastUpdated": "2025-06-19T12:55:00Z", "dataSource": "OpenAI Platform Documentation, Model Specifications based on GPT Family patterns", "version": "1.0"}}