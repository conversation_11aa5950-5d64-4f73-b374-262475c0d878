import React, { useState, useMemo } from 'react';
import type { ModelCard } from '@/types/model-cards';
import benchmarkDescriptions from '@/data/benchmarks/benchmark-descriptions.json';

// Relevante Benchmarks aus model-recommendations.ts
const USE_CASE_BENCHMARK_MAPPING: { [key: string]: string[] } = {
  'code-generation': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
  'code-review': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
  'debugging': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
  'documentation': ['LiveCodeBench v2025', 'MMLU', 'IFEval', 'MultiChallenge'],
  'refactoring': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
  'testing': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'],
  'api-integration': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'ComplexFuncBench', 'TAU-bench Retail', 'TAU-bench Airline', 'Terminal-bench'],
  'data-analysis': ['MATH', 'MMMU', 'MathVista', 'LiveCodeBench v2025'],
  'devops-automation': ['SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench', 'TAU-bench Retail', 'TAU-bench Airline'],
  'learning-support': ['MMLU', 'IFEval', 'MMLU']
};

const CRITICAL_CODING_BENCHMARKS = [
  'SWE-bench Verified', 'LiveCodeBench v2025', 'Aider-Polyglot', 'WebDev-Arena', 'Terminal-bench'
];

interface QualityCheckPageProps {
  modelCards: ModelCard[];
}

interface BenchmarkData {
  benchmarkName: string;
  category: string;
  modelValues: { [modelId: string]: { score: number; metric: string; category: string } };
  relevantUseCases: string[];
  isCritical: boolean;
}

export const QualityCheckPage: React.FC<QualityCheckPageProps> = ({ modelCards }) => {
  const [selectedUseCase, setSelectedUseCase] = useState<string>('all');
  const [selectedBenchmark, setSelectedBenchmark] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'coverage' | 'avgScore'>('coverage');
  const [showOnlyWithData, setShowOnlyWithData] = useState(true);

  // Extrahiere alle Benchmarks und deren Daten
  const benchmarkData = useMemo(() => {
    const benchmarks = new Map<string, BenchmarkData>();

    // Sammle alle Benchmarks aus den Model Cards
    modelCards.forEach(card => {
      if (card.benchmarks) {
        card.benchmarks.forEach(benchmark => {
          if (!benchmarks.has(benchmark.benchmarkName)) {
            // Finde relevante Use Cases für diesen Benchmark
            const relevantUseCases: string[] = [];
            Object.entries(USE_CASE_BENCHMARK_MAPPING).forEach(([useCase, benchmarkList]) => {
              if (benchmarkList.some(b => 
                benchmark.benchmarkName.toLowerCase().includes(b.toLowerCase()) ||
                benchmark.category.toLowerCase().includes(b.toLowerCase())
              )) {
                relevantUseCases.push(useCase);
              }
            });

            benchmarks.set(benchmark.benchmarkName, {
              benchmarkName: benchmark.benchmarkName,
              category: benchmark.category,
              modelValues: {},
              relevantUseCases,
              isCritical: CRITICAL_CODING_BENCHMARKS.some(cb =>
                benchmark.benchmarkName.toLowerCase().includes(cb.toLowerCase())
              )
            });
          }

          const benchmarkData = benchmarks.get(benchmark.benchmarkName);
          if (!benchmarkData) return;
          benchmarkData.modelValues[card.basicInfo.modelId] = {
            score: benchmark.score,
            metric: benchmark.metric,
            category: benchmark.category
          };
        });
      }
    });

    return Array.from(benchmarks.values());
  }, [modelCards]);

  // Filtere Benchmarks basierend auf ausgewähltem Use Case
  const filteredBenchmarks = useMemo(() => {
    let filtered = benchmarkData;

    if (selectedUseCase !== 'all') {
      filtered = filtered.filter(b => b.relevantUseCases.includes(selectedUseCase));
    }

    if (selectedBenchmark !== 'all') {
      filtered = filtered.filter(b => b.benchmarkName === selectedBenchmark);
    }

    if (showOnlyWithData) {
      filtered = filtered.filter(b => Object.keys(b.modelValues).length > 0);
    }

    // Sortierung
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.benchmarkName.localeCompare(b.benchmarkName);
        case 'coverage':
          return Object.keys(b.modelValues).length - Object.keys(a.modelValues).length;
        case 'avgScore': {
          const avgA = Object.values(a.modelValues).reduce((sum, v) => sum + v.score, 0) / Object.keys(a.modelValues).length || 0;
          const avgB = Object.values(b.modelValues).reduce((sum, v) => sum + v.score, 0) / Object.keys(b.modelValues).length || 0;
          return avgB - avgA;
        }
        default:
          return 0;
      }
    });

    return filtered;
  }, [benchmarkData, selectedUseCase, selectedBenchmark, sortBy, showOnlyWithData]);

  // Extrahiere Use Cases für Filter
  const useCases = Object.keys(USE_CASE_BENCHMARK_MAPPING);

  // Extrahiere alle Benchmark-Namen für Filter
  const allBenchmarkNames = useMemo(() => {
    return Array.from(new Set(benchmarkData.map(b => b.benchmarkName))).sort();
  }, [benchmarkData]);

  // Hilfsfunktion für Score-Farben
  const getScoreColor = (score: number, metric: string, benchmarkName?: string): string => {
    // Spezielle Behandlung für Aider-Polyglot-Wellformated (hohe Scores, feinere Abstufung)
    if (benchmarkName && benchmarkName.toLowerCase().includes('aider-polyglot-wellformated')) {
      if (score >= 98) return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/30';     // Excellent (98%+)
      if (score >= 96) return 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/30';      // Good (96-98%)
      if (score >= 94) return 'text-yellow-600 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/30';  // Fair (94-96%)
      return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/30';                         // Poor (<94%)
    }
    
    // Spezielle Behandlung für Arena Scores (WebDev-Arena)
    if (metric === 'Arena Score') {
      if (score >= 1450) return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/30';   // Excellent (95pts equivalent)
      if (score >= 1400) return 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/30';    // Good (85pts equivalent)
      if (score >= 1350) return 'text-yellow-600 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/30'; // Fair (70pts equivalent)
      return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/30';                         // Poor (<70pts equivalent)
    }
    
    // Spezielle Behandlung für ELO-Ratings
    if (metric === 'ELO Rating' || metric === 'Elo Rating' || metric === 'Rating') {
      if (score >= 2400) return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/30';
      if (score >= 1800) return 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/30';
      if (score >= 1200) return 'text-yellow-600 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/30';
      return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/30';
    }

    // Standard-Prozent-Scores
    if (score >= 80) return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/30';
    if (score >= 60) return 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/30';
    if (score >= 40) return 'text-yellow-600 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/30';
    return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/30';
  };

  // Formatiere Score-Anzeige
  const formatScore = (score: number, metric: string): string => {
    if (metric === 'ELO Rating' || metric === 'Elo Rating' || metric === 'Rating' || metric ==='Arena Score') {
      return score.toFixed(0);
    }
    return score.toFixed(1) + '%';
  };

  // Hilfsfunktion um Benchmark-Beschreibung zu finden
  const getBenchmarkDescription = (benchmarkName: string) => {
    // Direkte Suche nach exaktem Namen
    if (benchmarkName in benchmarkDescriptions.benchmarks) {
      return benchmarkDescriptions.benchmarks[benchmarkName as keyof typeof benchmarkDescriptions.benchmarks];
    }
    
    // Fallback: Suche nach partieller Übereinstimmung
    const entries = Object.entries(benchmarkDescriptions.benchmarks);
    for (const [key, desc] of entries) {
      if (key.toLowerCase().includes(benchmarkName.toLowerCase()) ||
          benchmarkName.toLowerCase().includes(key.toLowerCase())) {
        return desc;
      }
    }
    
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Filter-Bereich */}
      <div className="bg-card rounded-lg border p-6">
        <h2 className="text-xl font-semibold text-foreground mb-4">Filter & Sortierung</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Use Case Filter */}
          <div>
            <label htmlFor="usecase-select" className="block text-sm font-medium text-muted-foreground mb-2">Use Case</label>
            <select
              id="usecase-select"
              value={selectedUseCase}
              onChange={(e) => setSelectedUseCase(e.target.value)}
              className="w-full p-2 border border-input rounded-md focus:ring-2 focus:ring-ring focus:border-ring bg-background text-foreground"
            >
              <option value="all">Alle Use Cases</option>
              {useCases.map(useCase => (
                <option key={useCase} value={useCase}>
                  {useCase.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </option>
              ))}
            </select>
          </div>

          {/* Benchmark Filter */}
          <div>
            <label htmlFor="benchmark-select" className="block text-sm font-medium text-muted-foreground mb-2">Benchmark</label>
            <select
              id="benchmark-select"
              value={selectedBenchmark}
              onChange={(e) => setSelectedBenchmark(e.target.value)}
              className="w-full p-2 border border-input rounded-md focus:ring-2 focus:ring-ring focus:border-ring bg-background text-foreground"
            >
              <option value="all">Alle Benchmarks</option>
              {allBenchmarkNames.map(name => (
                <option key={name} value={name}>{name}</option>
              ))}
            </select>
          </div>

          {/* Sortierung */}
          <div>
            <label htmlFor="sort-select" className="block text-sm font-medium text-muted-foreground mb-2">Sortierung</label>
            <select
              id="sort-select"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'name' | 'coverage' | 'avgScore')}
              className="w-full p-2 border border-input rounded-md focus:ring-2 focus:ring-ring focus:border-ring bg-background text-foreground"
            >
              <option value="coverage">Nach Abdeckung</option>
              <option value="avgScore">Nach Durchschnittsscore</option>
              <option value="name">Nach Name</option>
            </select>
          </div>

          {/* Nur mit Daten anzeigen */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="showOnlyWithData"
              checked={showOnlyWithData}
              onChange={(e) => setShowOnlyWithData(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-ring border-input rounded"
            />
            <label htmlFor="showOnlyWithData" className="ml-2 block text-sm text-muted-foreground">
              Nur Benchmarks mit Daten
            </label>
          </div>
        </div>
      </div>

      {/* Benchmark-Grid */}
      <div className="space-y-4">
        {filteredBenchmarks.map(benchmark => {
          const description = getBenchmarkDescription(benchmark.benchmarkName);
          
          return (
            <div key={benchmark.benchmarkName} className="bg-card rounded-lg border overflow-hidden">
              {/* Benchmark Header */}
              <div className="bg-muted px-6 py-4 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-foreground flex items-center gap-2 mb-2">
                      {benchmark.benchmarkName}
                      {benchmark.isCritical && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
                          Critical
                        </span>
                      )}
                      {description?.difficulty && (
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          description.difficulty === 'Extrem Hoch' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                          description.difficulty === 'Sehr Hoch' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400' :
                          description.difficulty === 'Hoch' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                          description.difficulty === 'Mittel bis Hoch' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' :
                          'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                        }`}>
                          {description.difficulty}
                        </span>
                      )}
                    </h3>
                    
                    {/* Benchmark-Beschreibung */}
                    {description && (
                      <div className="space-y-2 mb-2">
                        <p className="text-sm text-muted-foreground">
                          <span className="font-medium">Beschreibung:</span> {description.shortDescription}
                        </p>
                        {description.fullName && description.fullName !== benchmark.benchmarkName && (
                          <p className="text-xs text-muted-foreground">
                            <span className="font-medium">Vollständiger Name:</span> {description.fullName}
                          </p>
                        )}
                        <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
                          {description.metric && (
                            <span><span className="font-medium">Metrik:</span> {description.metric}</span>
                          )}
                          {description.scoreRange && (
                            <span><span className="font-medium">Score-Bereich:</span> {description.scoreRange}</span>
                          )}
                          {description.languages && description.languages.length > 0 && (
                            <span><span className="font-medium">Sprachen:</span> {description.languages.join(', ')}</span>
                          )}
                        </div>
                        {description.website && (
                          <div className="text-xs">
                            <a
                              href={description.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline"
                            >
                              🔗 Website
                            </a>
                            {description.paperUrl && (
                              <>
                                {' | '}
                                <a
                                  href={description.paperUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline"
                                >
                                  📄 Paper
                                </a>
                              </>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                    
                    <p className="text-sm text-muted-foreground">
                      Kategorie: {benchmark.category} |
                      Relevante Use Cases: {benchmark.relevantUseCases.join(', ') || 'Keine spezifischen'}
                    </p>
                  </div>
                  <div className="text-right ml-4">
                    <p className="text-sm text-muted-foreground">Abdeckung</p>
                    <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                      {Object.keys(benchmark.modelValues).length}/{modelCards.length}
                    </p>
                  </div>
                </div>
              </div>

            {/* Model Values Grid */}
            <div className="p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
                {modelCards.map(card => {
                  const value = benchmark.modelValues[card.basicInfo.modelId];
                  
                  return (
                    <div
                      key={card.basicInfo.modelId}
                      className={`p-3 rounded-lg border ${
                        value
                          ? `${getScoreColor(value.score, value.metric, benchmark.benchmarkName)} border-current`
                          : 'bg-muted border-border'
                      }`}
                    >
                      <div className="text-xs font-medium text-foreground mb-1 truncate" title={card.basicInfo.displayName}>
                        {card.basicInfo.displayName}
                      </div>
                      {value ? (
                        <div>
                          <div className="text-lg font-bold">
                            {formatScore(value.score, value.metric)}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {value.metric}
                          </div>
                        </div>
                      ) : (
                        <div className="text-sm text-muted-foreground">
                          Keine Daten
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          );
        })}
      </div>

      {filteredBenchmarks.length === 0 && (
        <div className="bg-card rounded-lg border p-8 text-center">
          <p className="text-muted-foreground">Keine Benchmarks gefunden, die den aktuellen Filterkriterien entsprechen.</p>
        </div>
      )}
    </div>
  );
};

export default QualityCheckPage;