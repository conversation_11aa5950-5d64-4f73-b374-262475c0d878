import type { ModelCard } from "@/types/model-cards";
import type {
  UseCase,
  ModelRecommendation,
  UseCaseRecommendations,
} from "@/types/model-recommendations";
import { STANDARD_USE_CASES } from "@/types/model-recommendations";

// Gewichtungen für verschiedene Faktoren bei der Modell-Bewertung
const SCORING_WEIGHTS = {
  benchmark: 0.6, // Benchmark-Performance (erhöht - wichtigster Faktor)
  capabilities: 0.15, // Erforderliche Capabilities
  cost: 0.1, // Kosten-Effizienz (reduziert - war zu dominant)
  latency: 0.1, // Latenz/Geschwindigkeit (erhöht - wichtig für UX)
  availability: 0.05, // Verfügbarkeit und Stabilität (reduziert - weniger kritisch)
};

// Benchmark-Mapping für Use Cases - erweitert mit wichtigsten Coding-Benchmarks
const USE_CASE_BENCHMARK_MAPPING: { [key: string]: string[] } = {
  "code-generation": [
    "SWE-bench Verified",
    "LiveCodeBench v2025",
    "Aider-Polyglot",
    "WebDev-Arena",
    "Terminal-bench",
  ],
  "code-review": [
    "SWE-bench Verified",
    "LiveCodeBench v2025",
    "Aider-Polyglot",
    "WebDev-Arena",
    "Terminal-bench",
  ],
  debugging: [
    "SWE-bench Verified",
    "LiveCodeBench v2025",
    "Aider-Polyglot",
    "WebDev-Arena",
    "Terminal-bench",
  ],
  documentation: [
    "LiveCodeBench v2025",
    "MMLU",
    "IFEval",
    "MultiChallenge",
    "Graphwalks BFS <128k",
  ],
  refactoring: [
    "SWE-bench Verified",
    "LiveCodeBench v2025",
    "Aider-Polyglot",
    "WebDev-Arena",
    "Terminal-bench",
  ],
  testing: [
    "SWE-bench Verified",
    "LiveCodeBench v2025",
    "Aider-Polyglot",
    "WebDev-Arena",
    "Terminal-bench",
  ],
  "api-integration": [
    "SWE-bench Verified",
    "LiveCodeBench v2025",
    "Aider-Polyglot",
    "WebDev-Arena",
    "ComplexFuncBench",
    "TAU-bench Retail",
    "TAU-bench Airline",
    "Terminal-bench",
  ],
  "data-analysis": ["MATH", "MMMU", "MathVista", "LiveCodeBench v2025"],
  "devops-automation": [
    "SWE-bench Verified",
    "LiveCodeBench v2025",
    "Aider-Polyglot",
    "WebDev-Arena",
    "Terminal-bench",
    "TAU-bench Retail",
    "TAU-bench Airline",
  ],
  "learning-support": ["MMLU", "IFEval", "MMLU"],
};

// Kosten-Kategorien basierend auf Input-Kosten pro 1M Tokens
const getCostCategory = (
  inputCost: number
): "budget" | "standard" | "premium" => {
  if (inputCost <= 1) return "budget";
  if (inputCost <= 5) return "standard";
  return "premium";
};

// Latenz-Score basierend auf Latenz-Kategorie
const getLatencyScore = (latency: string): number => {
  switch (latency) {
    case "Fastest":
      return 100;
    case "Fast":
      return 85;
    case "Moderately Fast":
      return 70;
    case "Slow":
      return 50;
    case "Slowest":
      return 30;
    default:
      return 70;
  }
};

// Berechnet Capability-Score für einen Use Case
const calculateCapabilityScore = (
  modelCard: ModelCard,
  useCase: UseCase
): number => {
  const requiredCaps = useCase.requiredCapabilities;
  let score = 0;
  let totalWeight = 0;

  requiredCaps.forEach((cap) => {
    totalWeight += 1;
    if (modelCard.capabilities[cap as keyof typeof modelCard.capabilities]) {
      score += 1;
    }
  });

  return totalWeight > 0 ? (score / totalWeight) * 100 : 50;
};

// Berechnet Benchmark-Score für einen Use Case mit Coding-Fokus
const calculateBenchmarkScore = (
  modelCard: ModelCard,
  useCase: UseCase
): number => {
  const relevantBenchmarks = USE_CASE_BENCHMARK_MAPPING[useCase.id] || [];

  if (!modelCard.benchmarks || modelCard.benchmarks.length === 0) {
    return 0;
  }

  let totalScore = 0;
  let benchmarkCount = 0;
  let _codingBenchmarkBonus = 0;

  // Definiere wichtigste Coding-Benchmarks für zusätzliche Gewichtung
  const CRITICAL_CODING_BENCHMARKS = [
    "SWE-bench Verified",
    "LiveCodeBench v2025",
    "Aider-Polyglot",
    "WebDev-Arena",
    "Terminal-bench",
  ];

  modelCard.benchmarks.forEach((benchmark) => {
    const isRelevant = relevantBenchmarks.some(
      (rb) =>
        benchmark.benchmarkName.toLowerCase().includes(rb.toLowerCase()) ||
        benchmark.category.toLowerCase().includes(rb.toLowerCase())
    );

    if (isRelevant) {
      // Normalisiere Benchmark-Scores (angenommen: 0-100 für die meisten)
      let normalizedScore = benchmark.score;

      // Spezielle Behandlung für Arena Scores (WebDev-Arena)
      if (benchmark.metric === "Arena Score") {
        // Arena Score Normalisierung: 1300 = 60, 1350 = 70, 1400 = 85, 1450+ = 95
        if (benchmark.score >= 1450) normalizedScore = 95;
        else if (benchmark.score >= 1400)
          normalizedScore = 85 + ((benchmark.score - 1400) / 50) * 10;
        else if (benchmark.score >= 1350)
          normalizedScore = 70 + ((benchmark.score - 1350) / 50) * 15;
        else if (benchmark.score >= 1300)
          normalizedScore = 60 + ((benchmark.score - 1300) / 50) * 10;
        else normalizedScore = Math.max(30, (benchmark.score / 1300) * 60);
      }
      // Spezielle Behandlung für ELO-Ratings und Ratings
      else if (
        benchmark.metric === "ELO Rating" ||
        benchmark.metric === "Elo Rating" ||
        benchmark.metric === "Rating"
      ) {
        // Realistischere ELO-Normalisierung: 1200 = 30, 1800 = 70, 2400+ = 90
        if (benchmark.score >= 2400) normalizedScore = 90;
        else if (benchmark.score >= 1800)
          normalizedScore = 70 + ((benchmark.score - 1800) / 600) * 20;
        else if (benchmark.score >= 1200)
          normalizedScore = 30 + ((benchmark.score - 1200) / 600) * 40;
        else normalizedScore = Math.max(10, (benchmark.score / 1200) * 30);
      }

      // Bonus für kritische Coding-Benchmarks bei Coding-Use-Cases
      const isCodingUseCase = [
        "code-generation",
        "code-review",
        "debugging",
        "refactoring",
        "testing",
        "api-integration",
        "devops-automation",
      ].includes(useCase.id);
      const isCriticalCodingBenchmark = CRITICAL_CODING_BENCHMARKS.some((cb) =>
        benchmark.benchmarkName.toLowerCase().includes(cb.toLowerCase())
      );

      if (isCodingUseCase && isCriticalCodingBenchmark) {
        // Gewichte kritische Coding-Benchmarks 1.5x höher
        totalScore += normalizedScore * 1.5;
        _codingBenchmarkBonus += normalizedScore * 0.5;
      } else {
        totalScore += normalizedScore;
      }
      benchmarkCount++;
    }
  });

  if (benchmarkCount > 0) {
    const baseScore = totalScore / benchmarkCount;
    return Math.min(100, baseScore);
  }

  return 0;
};

// Berechnet Kosten-Score (inversely proportional zu Kosten) - weniger extremer Unterschied
const calculateCostScore = (modelCard: ModelCard): number => {
  const inputCost = modelCard.pricing.inputCostPer1MTokens;
  // Moderatere Skalierung für fairere Bewertung
  // Budget models (0-1$): 85-95 points
  // Standard models (1-5$): 70-85 points
  // Premium models (5$+): 50-70 points
  if (inputCost <= 1) return 85 + (1 - inputCost) * 10;
  if (inputCost <= 5) return 70 + ((5 - inputCost) / 4) * 15;
  return Math.max(50, 70 - (inputCost - 5) * 2);
};

// Berechnet Context Window Score
const calculateContextScore = (modelCard: ModelCard): number => {
  const contextWindow = modelCard.technicalSpecs.contextWindow || 0;
  // Berücksichtigt Kontext-Fenster als Qualitätsfaktor
  if (contextWindow >= 500000) return 120; // 500k+ = Outstanding
  if (contextWindow >= 200000) return 100; // 200k+ = Excellent
  if (contextWindow >= 128000) return 90; // 128k+ = Very Good
  if (contextWindow >= 65536) return 80; // 64k+ = Good
  if (contextWindow >= 32768) return 70; // 32k+ = Acceptable
  if (contextWindow >= 16384) return 60; // 16k+ = Limited
  return 40; // < 16k = Poor
};

// Hauptfunktion zur Bewertung eines Modells für einen Use Case
export const calculateModelScore = (
  modelCard: ModelCard,
  useCase: UseCase
): number => {
  const benchmarkScore = calculateBenchmarkScore(modelCard, useCase);
  const capabilityScore = calculateCapabilityScore(modelCard, useCase);
  const costScore = calculateCostScore(modelCard);
  const latencyScore = getLatencyScore(modelCard.performance.latency);
  const contextScore = calculateContextScore(modelCard);
  const availabilityScore =
    modelCard.basicInfo.status === "GA"
      ? 100
      : modelCard.basicInfo.status === "Preview"
      ? 80
      : 60;

  // Penalisiere Modelle mit sehr schlechten Benchmarks (z.B. SimpleQA = 30.1%)
  let benchmarkPenalty = 0;
  if (modelCard.benchmarks) {
    const poorPerformanceBenchmarks = modelCard.benchmarks.filter(
      (b) =>
        b.score < 40 &&
        (b.category.includes("Factuality") || b.category.includes("Knowledge"))
    );
    benchmarkPenalty = poorPerformanceBenchmarks.length * 5; // -5 Punkte pro schlechten Benchmark
  }

  // Belohne multimodale Capabilities für entsprechende Use Cases
  let multimodalBonus = 0;
  if (
    (useCase.id === "data-analysis" || useCase.id === "documentation") &&
    modelCard.capabilities.vision
  ) {
    multimodalBonus = 5;
  }

  // Spezielle Coding-Anpassungen basierend auf Use Case
  const isCodingUseCase = [
    "code-generation",
    "code-review",
    "debugging",
    "refactoring",
    "testing",
    "api-integration",
    "devops-automation",
  ].includes(useCase.id);
  let codingAdjustment = 0;

  if (isCodingUseCase) {
    // Erhöhe Benchmark-Gewichtung für Coding-Use-Cases
    codingAdjustment = benchmarkScore * 0.1; // +10% Benchmark-Gewichtung

    // Context Window ist besonders wichtig für Coding
    const contextBonus = contextScore > 90 ? 3 : contextScore > 80 ? 2 : 0;
    codingAdjustment += contextBonus;
  }

  const totalScore =
    benchmarkScore * SCORING_WEIGHTS.benchmark +
    capabilityScore * SCORING_WEIGHTS.capabilities +
    costScore * SCORING_WEIGHTS.cost +
    latencyScore * SCORING_WEIGHTS.latency +
    availabilityScore * SCORING_WEIGHTS.availability +
    multimodalBonus +
    codingAdjustment -
    benchmarkPenalty;

  return Math.round(Math.max(0, totalScore));
};

// Generiert Empfehlungen für einen spezifischen Use Case
export const generateUseCaseRecommendations = (
  modelCards: ModelCard[],
  useCase: UseCase
): UseCaseRecommendations => {
  const modelRecommendations: ModelRecommendation[] = modelCards.map((card) => {
    const score = calculateModelScore(card, useCase);
    const benchmarkScore = calculateBenchmarkScore(card, useCase);
    const capabilityScore = calculateCapabilityScore(card, useCase);
    const costCategory = getCostCategory(card.pricing.inputCostPer1MTokens);

    // Generiere Stärken basierend auf Scores
    const strengths: string[] = [];
    if (benchmarkScore > 80) strengths.push("Exzellente Benchmark-Performance");
    if (benchmarkScore > 60)
      strengths.push("Gute Performance in relevanten Tests");
    if (capabilityScore === 100)
      strengths.push("Unterstützt alle erforderlichen Features");
    if (costCategory === "budget") strengths.push("Sehr kostengünstig");
    if (
      card.performance.latency === "Fastest" ||
      card.performance.latency === "Fast"
    ) {
      strengths.push("Schnelle Antwortzeiten");
    }

    // Generiere Limitierungen
    const limitations: string[] = [];
    if (benchmarkScore < 40)
      limitations.push("Begrenzte Performance in relevanten Benchmarks");
    if (capabilityScore < 80)
      limitations.push("Fehlende wichtige Capabilities");
    if (costCategory === "premium") limitations.push("Höhere Kosten");
    if (
      card.performance.latency === "Slow" ||
      card.performance.latency === "Slowest"
    ) {
      limitations.push("Langsamere Antwortzeiten");
    }

    // Bestimme Eignung
    let suitability: "excellent" | "good" | "acceptable" | "limited";
    if (score >= 80) suitability = "excellent";
    else if (score >= 65) suitability = "good";
    else if (score >= 50) suitability = "acceptable";
    else suitability = "limited";

    // Kostenwirksamkeit - korrekte Logik (high = kostengünstig, low = teuer)
    let costEffectiveness: "high" | "medium" | "low";
    if (costCategory === "budget" && score > 65) {
      costEffectiveness = "high"; // Budget + good performance = very cost effective
    } else if (costCategory === "standard" && score > 70) {
      costEffectiveness = "medium"; // Standard price + good performance = medium cost effectiveness
    } else if (costCategory === "premium") {
      costEffectiveness = "low"; // Premium models are expensive regardless of performance
    } else if (costCategory === "budget") {
      costEffectiveness = "medium"; // Budget models are generally cost effective
    } else {
      costEffectiveness = "medium"; // Standard models with lower performance
    }

    return {
      modelId: card.basicInfo.modelId,
      useCase: useCase.id,
      score,
      reasoning: generateRecommendationReasoning(
        card,
        useCase,
        score,
        benchmarkScore,
        capabilityScore
      ),
      strengths,
      limitations,
      costEffectiveness,
      suitability,
    };
  });

  // Sortiere nach Score
  modelRecommendations.sort((a, b) => b.score - a.score);

  // Teile in Kategorien
  const recommendedModels = modelRecommendations.filter((r) => r.score >= 65);
  const alternativeModels = modelRecommendations.filter(
    (r) => r.score >= 50 && r.score < 65
  );
  const notRecommendedModels = modelRecommendations.filter((r) => r.score < 50);

  return {
    useCase,
    recommendedModels,
    alternativeModels,
    notRecommendedModels,
  };
};

// Generiert Reasoning-Text für eine Empfehlung
const generateRecommendationReasoning = (
  card: ModelCard,
  useCase: UseCase,
  totalScore: number,
  benchmarkScore: number,
  capabilityScore: number
): string => {
  const costCategory = getCostCategory(card.pricing.inputCostPer1MTokens);

  let reasoning = `${card.basicInfo.displayName} erreicht ${totalScore}/100 Punkte für ${useCase.name}. `;

  if (benchmarkScore > 70) {
    reasoning += `Zeigt starke Performance in relevanten Benchmarks (${benchmarkScore.toFixed(
      1
    )}/100). `;
  } else if (benchmarkScore > 40) {
    reasoning += `Solide Benchmark-Performance (${benchmarkScore.toFixed(
      1
    )}/100). `;
  } else {
    reasoning += `Begrenzte Benchmark-Daten verfügbar. `;
  }

  if (capabilityScore === 100) {
    reasoning += `Unterstützt alle erforderlichen Capabilities. `;
  } else if (capabilityScore > 70) {
    reasoning += `Unterstützt die meisten wichtigen Features. `;
  }

  reasoning += `Kostenkategorie: ${costCategory}. `;

  if (
    card.performance.latency === "Fastest" ||
    card.performance.latency === "Fast"
  ) {
    reasoning += `Bietet schnelle Antwortzeiten.`;
  }

  return reasoning;
};

// Holt alle Use Case Empfehlungen
export const getAllUseCaseRecommendations = (
  modelCards: ModelCard[]
): UseCaseRecommendations[] => {
  return STANDARD_USE_CASES.map((useCase) =>
    generateUseCaseRecommendations(modelCards, useCase)
  );
};

// Findet beste Modelle für mehrere Use Cases
export const findBestModelsForUseCases = (
  modelCards: ModelCard[],
  useCaseIds: string[]
): {
  modelId: string;
  averageScore: number;
  useCaseScores: { [useCaseId: string]: number };
}[] => {
  const relevantUseCases = STANDARD_USE_CASES.filter((uc) =>
    useCaseIds.includes(uc.id)
  );

  const modelScores = modelCards.map((card) => {
    const useCaseScores: { [useCaseId: string]: number } = {};
    let totalScore = 0;

    relevantUseCases.forEach((useCase) => {
      const score = calculateModelScore(card, useCase);
      useCaseScores[useCase.id] = score;
      totalScore += score;
    });

    return {
      modelId: card.basicInfo.modelId,
      averageScore: totalScore / relevantUseCases.length,
      useCaseScores,
    };
  });

  return modelScores.sort((a, b) => b.averageScore - a.averageScore);
};

// Exportiere Use Cases für UI
export { STANDARD_USE_CASES };
