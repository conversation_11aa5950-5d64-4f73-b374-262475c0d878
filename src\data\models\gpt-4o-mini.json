{"basicInfo": {"modelId": "gpt-4o-mini-2024-07-18", "displayName": "GPT-4o mini", "provider": "OpenAI", "modelFamily": "GPT", "version": "2024-07-18", "description": "Fast, affordable small model for focused tasks", "releaseDate": "2024-07-18", "status": "GA", "knowledgeCutoff": "Oktober 2023"}, "technicalSpecs": {"contextWindow": 128000, "maxOutputTokens": 16384, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": true, "reasoning": false, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": true, "codeInterpreter": true, "dalleIntegration": true, "realTimeAPI": false, "liteLLM-provisioning": false}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 500, "tokensPerMinute": 200000, "batchQueueLimit": 2000000}, "temperature": {"min": 0, "max": 2, "default": 1.0}, "topP": 1.0}, "pricing": {"inputCostPer1MTokens": 0.15, "outputCostPer1MTokens": 0.6, "cachingCosts": {"cacheHits": 0.075}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "Azure OpenAI", "OpenAI Batch API"]}, "benchmarks": [{"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 82.0, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 40.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "DROP", "category": "Reasoning & Knowledge", "score": 79.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "MGSM", "category": "Mathematics", "score": 87.0, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 70.2, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "MMLU", "category": "Multilingual performance", "score": 59.4, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 56.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-07-18"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 3.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2024-12-21", "notes": "Aider-Polyglot benchmark with whole edit format"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 100.0, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2024-12-21", "notes": "Percentage of well-formed responses in Aider-Polyglot benchmark"}, {"benchmarkName": "Graphwalks BFS <128k accuracy", "category": "Long context", "score": 28.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Multi-round co-reference resolution in langen Kontexten"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1100.0, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #10"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 18.5, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 2.0, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "GPT-4o mini score estimated based on positioning relative to GPT-4o"}], "metadata": {"lastUpdated": "2025-06-08T16:17:31Z", "dataSource": "OpenAI Platform Documentation", "version": "1.0"}}