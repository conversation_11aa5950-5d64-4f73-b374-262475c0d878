

# Model-Card-Basis-Template

```
<PERSON><PERSON><PERSON> aus vorhandenen Modell-Cards-Infos ein allgemeingültiges JSON inkl. Beispiel, womit ein Model beschrieben werden kann:

- @https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-5-pro?hl=de
- @https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-5-flash?hl=de
- @https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-0-flash?hl=de
- @https://cloud.google.com/vertex-ai/generative-ai/docs/models/imagen/4-0-generate-preview-05-20?hl=de
- @https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/claude/opus-4?hl=de
- @https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/claude/sonnet-4?hl=de
- @https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/claude/sonnet-3-7?hl=de
- @https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/claude/sonnet-3-5-v2?hl=de
- @https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/llama/llama3-2?hl=de
- @https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/llama/llama4-scout?hl=de
- @https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/mistral/mistral-large?hl=de
- @https://docs.anthropic.com/en/docs/about-claude/models/overview#model-comparison-table

Lege das Ergebnis unter \docs\feature-model-cards-templates als MD-Datei ab!
```

## Verify

```
Erweitere das JSON-Schema, um auch die OpenAI spezifischen infos korrekt abzubilden @/src/data/models/model-card-json-schema.md 

- @https://platform.openai.com/docs/models/o4-mini
- @https://platform.openai.com/docs/models/o3
- @https://platform.openai.com/docs/models/gpt-4.1
- @https://platform.openai.com/docs/models/o3-mini
- @https://platform.openai.com/docs/models/gpt-4o

```



# Benchmark-Daten
Attach: https://storage.googleapis.com/gweb-uniblog-publish-prod/original_images/gemini_benchmarks_cropped_light2x_1PPmDuP.gif

```
Erweitere das Template @/docs/feature-model-cards-templates/model-card-json-schema.md und das Beispiel @/docs/feature-model-cards-templates/gemini-2.5-pro-example.json auf Basis der Daten!
```

## Claude 4-Opus

```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/claude/opus-4?hl=de 

unter \src\data\models. 

Nutze die 
- Benchmark-Daten : @https://www.anthropic.com/news/claude-4 
- Offizielle Informationen: @https://docs.anthropic.com/en/docs/about-claude/models/overview#model-comparison-table 
```

## Claude 4-Sonnet

```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/claude/sonnet-4?hl=de 

unter \src\data\models. 

Nutze die 
- Benchmark-Daten : @https://www.anthropic.com/news/claude-4 
- Offizielle Informationen: @https://docs.anthropic.com/en/docs/about-claude/models/overview#model-comparison-table 
```

## Claude 3.7-Sonnet
```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/claude/sonnet-3-7?hl=de

unter \src\data\models. 

Nutze die 
- Benchmark-Daten : @https://www.anthropic.com/news/claude-4 
- Offizielle Informationen: @https://docs.anthropic.com/en/docs/about-claude/models/overview#model-comparison-table 
```

## Claude 3.5-Sonnet
```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/claude/sonnet-3-5-v2?hl=de

unter \src\data\models. 

Nutze die 
- Benchmark-Daten : @https://www.anthropic.com/news/claude-3-5-sonnet 
```

## OpenAI GPT-4.1
```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://platform.openai.com/docs/models/gpt-4.1

unter \src\data\models. 

Nutze die 
- Benchmark-Daten : @https://www.anthropic.com/news/claude-4 
```

## OpenAI O4-Mini
```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://platform.openai.com/docs/models/o4-mini

unter \src\data\models. 

Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md .
```

```
Aktualisiere die Benchmarks von @/src/data/models/o4-mini.json  und @/src/data/models/o3.json 
auf Basis von @https://www.datacamp.com/blog/o4-mini
```

## OpenAI gpt-4o-mini
```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://platform.openai.com/docs/models/gpt-4o-mini

unter \src\data\models. 

<details>
html-code von der website
</details>

Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md .
```

## OpenAI gpt-4.1.nano
```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://platform.openai.com/docs/models/gpt-4.1-nano

unter \src\data\models. 

<details>
html-code von der website
</details>

Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md .
```

## Qwen 2.5 - Code 32b

```
Lege die Modell-Card-Daten für das Qwen2.5-Coder-32B-Instruct auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://qwenlm.github.io/blog/qwen2.5-coder-family/

unter \src\data\models. 

Nutze ebenfalls:
- @https://lambda.ai/inference-models/qwen25-coder-32b-instruct 


Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md .
```

## Deepseek-r1
- siehe https://the-decoder.de/wp-content/uploads/2025/01/DeepSeek-R1-Benchmarkresults-770x635.png.webp 

```
Lege die Modell-Card-Daten für das Deepseek-R1 auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://api-docs.deepseek.com/news/news250120

unter \src\data\models. 

Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md .
```


## Gemini 2.5 Pro
Attach: https://storage.googleapis.com/gweb-uniblog-publish-prod/original_images/gemini_benchmarks_cropped_light2x_1PPmDuP.gif


```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-5-pro?hl=de

unter \src\data\models. 

Nutze die 
- Benchmark-Daten : @https://www.anthropic.com/news/claude-4 
- Offizielle Informationen: @https://docs.anthropic.com/en/docs/about-claude/models/overview#model-comparison-table 
```

## Model-IDs
```
Erstelle unter \src\data\models eine Liste alle Model-IDs als MD-Datei, die dann für das Mapping genutzt werden können! @/src/data/models/model-mappings.json 
auf Basis von @/src/data/models.json !
```


## OpenAI o3
```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://platform.openai.com/docs/models/o3

unter \src\data\models. 

Nutze die 
- Benchmark-Daten : @https://www.anthropic.com/news/claude-4 

Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md 

```

## OpenAI o3-mini
```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://platform.openai.com/docs/models/o3-mini

unter \src\data\models. 

Nutze die 
- Benchmark-Daten : @https://www.anthropic.com/news/claude-4 

Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md 

```

## OpenAI gpt-4o
```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://platform.openai.com/docs/models/gpt-4o

unter \src\data\models. 

Nutze die 
- Benchmark-Daten : @https://www.anthropic.com/news/claude-4 

Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md 

```

## Mistral Large

```
Lege die Modell-Card-Daten auf Basis des Templates @/src/data/models/model-card-json-schema.md an für 
@https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/mistral/mistral-large?hl=de

unter \src\data\models. 

Aktualisiere danach die Mapping-Datei @/src/data/models/model-mappings.json   auf Basis der vorhandenen @/src/data/models/model-ids-reference.md 
 ```

# Einbindung der Daten im Generierungsprozess

Erweitere die Package/generate:data Script Aufruf so, dass die Daten aus @/src/data/models ebenfalls genutzt werden. Die Daten aus den Models werden dabei nur ergänzend als model-cards eingehängt und können später optional als Quelle genutzt werden!

