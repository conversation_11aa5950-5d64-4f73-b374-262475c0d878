{"basicInfo": {"modelId": "grok-3", "displayName": "Grok 3", "provider": "xAI", "modelFamily": "Grok", "version": "beta", "description": "Das bisher leistungsstärkste Modell von xAI, das bei Unternehmensanwendungen wie Datenextraktion, Programmierung und Textzusammenfassung glänzt. Verfügt über tiefes Fachwissen in Finanzen, Gesundheitswesen, Recht und Wissenschaft.", "releaseDate": "2024-12-01", "status": "Beta", "knowledgeCutoff": "November 2024"}, "technicalSpecs": {"contextWindow": 131072, "maxOutputTokens": 32768, "architecture": "Transformer", "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"], "inputLimitations": {"maxImageSize": "10 MB", "supportedMimeTypes": {"image": ["image/jpeg", "image/jpg", "image/png"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": false, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": true, "batchProcessing": false, "reasoning": true, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": false}, "performance": {"latency": "Moderately Fast", "rateLimits": {"queriesPerMinute": 50, "inputTokensPerMinute": 100000, "outputTokensPerMinute": 10000}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 3.0, "outputCostPer1MTokens": 15.0, "cachingCosts": {"cacheWrites": 3.0, "cacheHits": 0.75}, "currency": "USD"}, "availability": {"regions": [{"region": "global", "availability": "Beta"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["xAI API"]}, "security": {"dataResidency": false, "cmekSupport": false, "vpcSupport": false, "accessTransparency": false, "complianceStandards": []}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 93.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-30", "notes": "High School Mathematik-Wettbewerb 2025 - Grok 3 [Beta] bei vellum.ai"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 84.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-06", "notes": "Graduate-level naturwissenschaftliche Fragen bei vellum.ai"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 66.7, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-10", "notes": "Grok-3-Mini (High) - Holistische und kontaminationsfreie Code-Generierung"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1142.79, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "early-grok-3 - Real-time web development coding competition. Rank #15"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 17.5, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-17", "notes": "grok-3 - Terminal-basierte Coding-Aufgaben. 17.5% ± 4.2%"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 53.3, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-10", "notes": "Grok 3 Beta - Mehrsprachige Code-Bearbeitung mit verschiedenen Editing-Modi"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 99.6, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-10", "notes": "Grok 3 Beta - Prozentsatz wohlgeformter Antworten in Polyglot-Tests"}], "metadata": {"lastUpdated": "2025-06-10T16:44:00Z", "dataSource": "xAI Documentation, vals.ai AIME Benchmark, vellum.ai LLM Leaderboard, LiveCodeBench Leaderboard, WebDev-Arena Leaderboard, Terminal-Bench Leaderboard, Aider Polyg<PERSON>", "version": "1.4"}}