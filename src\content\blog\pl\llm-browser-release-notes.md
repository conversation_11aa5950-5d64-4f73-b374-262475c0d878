---
title: "Notatki wydania"
excerpt: "Dziennik zmian opisuje najnowsze zmiany krótko i zwięźle dla LLM Browser"
category: "release-notes"
tags: ["launch", "model-comparison", "benchmarks", "astro", "react", "performance"]
publishDate: "2025-06-15T09:00:00Z"
lastUpdated: "2025-06-15T09:00:00Z"
author:
  name: "Zespół Rozwoju LLM Browser"
  role: "Product & Engineering"
readingTime: 3
featured: false
relatedModelIds: ["o3", "claude-opus-4", "gemini-2.5-pro", "deepseek-v3", "deepseek-r1"]
releaseVersion: "0.5"

# pola specyficzne dla i18n
lang: "pl"
translationKey: "llm-browser-release-notes"
availableLanguages: ["de", "en", "pl"]

metaDescription: "Krótkie zwięzłe notatki wydania"
metaKeywords: ["LLM Browser", "Modele AI", "Benchmarki", "Porównanie modeli", "Analiza wydajności"]
featuredImage: "/images/blog/2025-06-1st-release.png"
changelog:
  - type: "added"
    description: "Opis notatki wydania dla 0.0.3 z 15.06.2025"
    impact: "minor"
    technicalDetails: "Krótki opis zmian"

---

## Mapa drogowa

- [Minor] Dalsze dostrojenie systemu rekomendacji dotyczące używanych benchmarków

## Wydania

### Wydanie 21 czerwca 2025 (v0.5)
- Implementacja funkcjonalności I18N do celów demonstracyjnych
  - Wersja początkowa (początkowo po stronie serwera) autorstwa Paweł Kamiński
  - Zrewidowana wersja do pracy z funkcjami I18N po stronie klienta ze względu na ograniczenia GitLab Pages
- Aktualizacja kart modeli Gemini i danych cenowych z teraz finalnymi modelami ze znacznymi obniżkami cen
  ![](/images/blog/2025-06-21-gemini-prices.png)

### Wydanie 19 czerwca 2025 (v0.4.1)

- Karty modeli
  - GPT 4.1-Mini włączony do porównania wraz z wartościami benchmarków
  - 4.1*, gpt-4o wsparcie PDF, wsparcie wizji, osadzanie obrazów naprawione (false->true)
  
### Wydanie 15 czerwca 2025 (v0.4)

- Optymalizacje UI
  - [Minor] Motyw ciemny/jasny zaimplementowany, który można aktywować przez pasek narzędzi
  - [Minor] Link bezpośredni GitLab w górnej nawigacji
  - [Minor] Benchmarki Aider włączone ponownie bezpośrednio w górnej nawigacji drugorzędnej
- Baza kodu
  - [Minor] Refaktoryzacja komponentów, tak aby powstała obecna logika domenowa była również lepiej reprezentowana technicznie
  - [Minor] Wprowadzenie konfiguracji lintera
  - [Minor] Przebudowa na konfigurację Type-Safety (TypeScript)
  - [Minor] Początkowa konfiguracja Vitetest
- Funkcje
  - [Minor] Początek notatek wydania
  - [Minor] Funkcja bloga teraz z funkcjami filtrowania i użyciem filtra kategorii (było tylko zasugerowane, ale nie zaprogramowane)
- Karty modeli
  - [Minor] Wprowadzenie karty modelu z https://mistral.ai/news/magistral (Nowy model rozumowania od Mistral)

### Wydanie 11 czerwca 2025 (v0.3)

- Funkcje
  - [Major] Budowa przypadków użycia AI i odpowiednich obliczonych rekomendacji modeli
    - Ważone benchmarki + możliwości + koszty + wydajność + dostępność jako podstawowe podejście
  - [Major] Budowa systemu bloga, aby móc szczegółowo omawiać rozwój i rekomendacje
    - Treści są po prostu utrzymywane jako pliki MD z podejściem frontmatter  
  - [Minor] Aktualizacja danych benchmarków kart modeli dla Claude 4, OpenAI o4, o4-mini 

### Wydanie 6 czerwca 2025 (v0.2)

- Funkcje
  - [Major] Wprowadzenie kart modeli, aby móc dostarczać informacje niezależnie obok danych proxy LLM i niezależnie od danych benchmarków Polyglot
  - [Major] Iteracyjna budowa danych kart modeli do użytku
- Dev | DevOps
  - [Minor] Publikowanie GitLab
  - [Major] Kompletna refaktoryzacja z aplikacji NEXT na framework Astro, tj. generowane strony internetowe ze wsparciem JS, aby móc być publikowane przez proste strony GitLab

### Wydanie 16 maja 2025 (v.0.1)

- Wersja początkowa
  - [Major] Pierwsza wersja demo dla _NEXT25 z wyświetlaniem danych modeli proxy LLM oraz benchmarku Aider Polyglot jako punkt wyjścia. Głównym celem było zbudowanie działającego demo z kaskadą promptów

## Dalszy rozwój
Jeśli masz życzenia dotyczące dalszego rozwoju, po prostu daj nam znać!

**Przydatne linki:**

- **[Repozytorium GitLab](https://gitlab.com/iteratec/llm-browser)** - Kod źródłowy