import * as React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../../ui/card";
import { Clock, Zap, AlertCircle, TrendingUp } from "lucide-react";
import type { EnrichedModelData } from "../types";

interface ModelPerformanceProps {
  model: EnrichedModelData;
}

export function ModelPerformance({ model }: ModelPerformanceProps) {
  const modelCard = model.modelCard;

  // Helper function to format numbers
  const formatNumber = (value: number | undefined | null) => {
    if (value == null) return "N/A";
    return value.toLocaleString();
  };

  // Get latency color and icon
  const getLatencyConfig = (latency: string) => {
    switch (latency) {
      case 'Fastest':
        return { color: 'text-green-600 dark:text-green-400', icon: Zap, bgColor: 'bg-green-100 dark:bg-green-900/30' };
      case 'Fast':
        return { color: 'text-blue-600 dark:text-blue-400', icon: TrendingUp, bgColor: 'bg-blue-100 dark:bg-blue-900/30' };
      case 'Moderately Fast':
        return { color: 'text-yellow-600 dark:text-yellow-400', icon: Clock, bgColor: 'bg-yellow-100 dark:bg-yellow-900/30' };
      case 'Slow':
        return { color: 'text-red-600 dark:text-red-400', icon: AlertCircle, bgColor: 'bg-red-100 dark:bg-red-900/30' };
      default:
        return { color: 'text-muted-foreground', icon: Clock, bgColor: 'bg-muted' };
    }
  };

  const latencyConfig = modelCard?.performance.latency 
    ? getLatencyConfig(modelCard.performance.latency)
    : null;

  return (
    <div className="space-y-6">
      {/* Latency */}
      {modelCard?.performance.latency && (
        <Card>
          <CardHeader>
            <CardTitle>Latenz</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`flex items-center gap-3 p-4 rounded-lg ${latencyConfig?.bgColor}`}>
              {latencyConfig?.icon && (
                <latencyConfig.icon className={`w-6 h-6 ${latencyConfig.color}`} />
              )}
              <div>
                <span className={`text-xl font-bold ${latencyConfig?.color}`}>
                  {modelCard.performance.latency}
                </span>
                <p className="text-sm text-gray-600">
                  Relative Geschwindigkeitskategorie
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Rate Limits */}
      {modelCard?.performance.rateLimits && (
        <Card>
          <CardHeader>
            <CardTitle>Rate Limits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {modelCard.performance.rateLimits.queriesPerMinute && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Queries per Minute</h3>
                  <p className="text-lg">{formatNumber(modelCard.performance.rateLimits.queriesPerMinute)}</p>
                </div>
              )}
              {modelCard.performance.rateLimits.tokensPerMinute && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Tokens per Minute</h3>
                  <p className="text-lg">{formatNumber(modelCard.performance.rateLimits.tokensPerMinute)}</p>
                </div>
              )}
              {modelCard.performance.rateLimits.inputTokensPerMinute && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Input Tokens per Minute</h3>
                  <p className="text-lg">{formatNumber(modelCard.performance.rateLimits.inputTokensPerMinute)}</p>
                </div>
              )}
              {modelCard.performance.rateLimits.outputTokensPerMinute && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Output Tokens per Minute</h3>
                  <p className="text-lg">{formatNumber(modelCard.performance.rateLimits.outputTokensPerMinute)}</p>
                </div>
              )}
              {modelCard.performance.rateLimits.reasoningTokensPerMinute && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Reasoning Tokens per Minute</h3>
                  <p className="text-lg">{formatNumber(modelCard.performance.rateLimits.reasoningTokensPerMinute)}</p>
                </div>
              )}
              {modelCard.performance.rateLimits.batchQueueLimit && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Batch Queue Limit</h3>
                  <p className="text-lg">{formatNumber(modelCard.performance.rateLimits.batchQueueLimit)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Temperature and Sampling Parameters */}
      {modelCard?.performance && (
        <Card>
          <CardHeader>
            <CardTitle>Sampling-Parameter</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-700">Temperature</h3>
                <div className="mt-2">
                  <p className="text-sm text-muted-foreground">
                    Min: {modelCard.performance.temperature.min} | 
                    Max: {modelCard.performance.temperature.max} | 
                    Default: {modelCard.performance.temperature.default}
                  </p>
                  <div className="mt-2 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.min(100, Math.max(0, (modelCard.performance.temperature.default / modelCard.performance.temperature.max) * 100))}%`
                      }}
                    />
                  </div>
                </div>
              </div>
              {modelCard.performance.topP && (
                <div>
                  <h3 className="font-semibold text-gray-700">Top-P</h3>
                  <p className="text-lg">{modelCard.performance.topP}</p>
                </div>
              )}
              {modelCard.performance.topK && (
                <div>
                  <h3 className="font-semibold text-gray-700">Top-K</h3>
                  <p className="text-lg">{modelCard.performance.topK}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reasoning Performance (for o1/o3 models) */}
      {modelCard?.performance?.reasoningPerformance && (
        <Card>
          <CardHeader>
            <CardTitle>Reasoning Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {modelCard.performance.reasoningPerformance.averageReasoningTime && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Durchschnittliche Reasoning-Zeit</h3>
                  <p className="text-lg">{modelCard.performance.reasoningPerformance.averageReasoningTime}</p>
                </div>
              )}
              {modelCard.performance.reasoningPerformance.maxReasoningTime && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Maximale Reasoning-Zeit</h3>
                  <p className="text-lg">{modelCard.performance.reasoningPerformance.maxReasoningTime}</p>
                </div>
              )}
              {modelCard.performance.reasoningPerformance.reasoningEfficiency && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Reasoning-Effizienz</h3>
                  <span className={`inline-block px-3 py-1 rounded-full text-sm ${
                    modelCard.performance.reasoningPerformance.reasoningEfficiency === 'High'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : modelCard.performance.reasoningPerformance.reasoningEfficiency === 'Medium'
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                      : 'bg-muted text-muted-foreground'
                  }`}>
                    {modelCard.performance.reasoningPerformance.reasoningEfficiency}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Legacy API Rate Limits */}
      {(model.tpm || model.rpm) && (
        <Card>
          <CardHeader>
            <CardTitle>API Rate Limits (Legacy)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              {model.tpm && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Tokens per Minute</h3>
                  <p className="text-lg">{formatNumber(model.tpm)}</p>
                </div>
              )}
              {model.rpm && (
                <div>
                  <h3 className="font-semibold text-muted-foreground">Requests per Minute</h3>
                  <p className="text-lg">{formatNumber(model.rpm)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Notes */}
      {!modelCard?.performance && (
        <Card>
          <CardHeader>
            <CardTitle>Performance Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 p-4 bg-muted rounded-lg">
              <AlertCircle className="w-5 h-5 text-muted-foreground" />
              <p className="text-muted-foreground">
                Detaillierte Performance-Daten sind nur für Modelle mit vollständigen Model Cards verfügbar.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}