import type { ModelInfo, ApiResponse, ModelData } from "@/types/api";

// Configuration for API access
interface ApiConfig {
  baseUrl: string;
  apiKey: string;
  useMockData: boolean;
}

// Default configuration - can be overridden via environment variables
const defaultConfig: ApiConfig = {
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || process.env.API_BASE_URL || "https://api.iteragpt.iteratec.de",
  apiKey: process.env.NEXT_PUBLIC_API_KEY || process.env.API_KEY || "",
  useMockData: process.env.USE_MOCK_DATA === "true" || false // Use real API by default
};

// Helper function to create auth headers
const getAuthHeaders = (config: ApiConfig) => {
  return {
    "Content-Type": "application/json",
    "Authorization": `Bearer ${config.apiKey}`
  };
};

// Generic fetch function with error handling for build-time
async function fetchWithAuth<T>(url: string, config: ApiConfig, options: RequestInit = {}): Promise<ApiResponse<T>> {
  try {
    const headers = {
      ...getAuthHeaders(config),
      ...options.headers
    };

    console.log(`[BUILD] Fetching from: ${url}`);
    
    const response = await fetch(url, {
      ...options,
      headers
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`[BUILD] Successfully fetched data from: ${url}`);
    
    return {
      data,
      status: response.status,
      message: "Success"
    };
  } catch (error) {
    console.error(`[BUILD] API fetch error for ${url}:`, error);
    return {
      data: [] as unknown as T,
      status: 500,
      message: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

// Fetch model information from /model/info endpoint for build-time
export async function fetchModelInfo(config: ApiConfig = defaultConfig): Promise<ApiResponse<ModelInfo[]>> {
  // Use mock data for development/testing
  if (config.useMockData) {
    console.log("[BUILD] Using mock data for model info");
    return {
      data: await getMockModelInfo(),
      status: 200,
      message: "Success (Mock Data)"
    };
  }
  
  try {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const response = await fetchWithAuth<any>(`${config.baseUrl}/model/info`, config);
    
    // Check if the response is successful
    if (response.status !== 200) {
      console.error("[BUILD] Failed to fetch model info:", response.message);
      return response as ApiResponse<ModelInfo[]>;
    }
    
    // Check if the data is an array
    if (!Array.isArray(response.data)) {
      console.warn("[BUILD] Model info data is not an array, trying to extract it");
      
      // Try to extract the array from common API response formats
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      let modelData: any[] = [];
      
      if (response.data && typeof response.data === 'object') {
        // Check for common patterns in API responses
        if (Array.isArray(response.data.data)) {
          modelData = response.data.data;
        } else if (Array.isArray(response.data.models)) {
          modelData = response.data.models;
        } else if (Array.isArray(response.data.results)) {
          modelData = response.data.results;
        } else if (response.data.items && Array.isArray(response.data.items)) {
          modelData = response.data.items;
        }
      }
      
      if (modelData.length > 0) {
        // Transform the data to ensure each model has an ID
        const transformedData: ModelInfo[] = modelData.map(item => {
          // Extract model_info if it exists (new API format)
          const modelInfo = item.model_info || item;
          
          // Get model name from the new API format
          const modelName = item.model_name || modelInfo.key || modelInfo.name || "Unknown Model";
          
          // Create a ModelInfo object with proper ID and map ALL support properties
          return {
            id: modelInfo.id || modelName || `model-${Math.random().toString(36).substring(2, 9)}`,
            name: modelName,
            provider: modelInfo.litellm_provider || "Unknown",
            description: modelInfo.description || "",
            capabilities: modelInfo.capabilities || [],
            modelGroup: modelInfo.model_group || "Unknown",
            isAvailable: modelInfo.is_available !== false,
            confidentiality: modelInfo.confidentiality || "external",
            mode: modelInfo.mode || "chat",
            maxTokens: modelInfo.max_tokens || modelInfo.max_output_tokens,
            maxInputTokens: modelInfo.max_input_tokens,
            maxOutputTokens: modelInfo.max_output_tokens,
            inputCostPerToken: modelInfo.input_cost_per_token,
            outputCostPerToken: modelInfo.output_cost_per_token,
            
            // Support properties from /info endpoint (snake_case from API)
            supportsFunctionCalling: modelInfo.supports_function_calling ?? false,
            supportsParallelFunctionCalling: modelInfo.supports_parallel_function_calling ?? false,
            supportsResponseSchema: modelInfo.supports_response_schema ?? false,
            supportsVision: modelInfo.supports_vision ?? false,
            supportsPromptCaching: modelInfo.supports_prompt_caching ?? false,
            supportsSystemMessages: modelInfo.supports_system_messages ?? false,
            supportsToolChoice: modelInfo.supports_tool_choice ?? false,
            supportsWebSearch: modelInfo.supports_web_search ?? false,
            supportsNativeStreaming: modelInfo.supports_native_streaming ?? false,
            supportsPdfInput: modelInfo.supports_pdf_input ?? false,
            supportsAudioInput: modelInfo.supports_audio_input ?? false,
            supportsAudioOutput: modelInfo.supports_audio_output ?? false,
            supportsAssistantPrefill: modelInfo.supports_assistant_prefill ?? false,
            supportsEmbeddingImageInput: modelInfo.supports_embedding_image_input ?? false,
            supportsReasoning: modelInfo.supports_reasoning ?? false,
            
            key: modelName,
            contextWindow: modelInfo.context_window || modelInfo.max_input_tokens || modelInfo.max_tokens
          };
        });
        
        console.log(`[BUILD] Successfully transformed ${transformedData.length} models from API response`);
        return {
          data: transformedData,
          status: 200,
          message: "Success"
        };
      }
    }
    
    // If we reach here, the data is already an array or we couldn't extract it
    const modelData = Array.isArray(response.data) ? response.data : [];
    console.log(`[BUILD] Using direct array data with ${modelData.length} models`);
    
    return {
      data: modelData,
      status: 200,
      message: "Success"
    };
  } catch (error) {
    console.error("[BUILD] Error fetching model info:", error);
    return {
      data: [],
      status: 500,
      message: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

// Combined data fetching for build-time generation
export async function fetchCombinedModelData(config: ApiConfig = defaultConfig): Promise<ApiResponse<ModelData[]>> {
  try {
    console.log("[BUILD] Starting combined model data fetch");
    
    // Fetch only model info data
    const modelInfoResponse = await fetchModelInfo(config);

    // Check if model info fetch failed
    if (modelInfoResponse.status !== 200) {
      console.error("[BUILD] Failed to fetch model info:", modelInfoResponse.message);
      return {
        data: [],
        status: modelInfoResponse.status,
        message: `Fehler beim Abrufen der Modellinformationen: ${modelInfoResponse.message}`
      };
    }

    // Ensure modelInfoResponse.data is an array
    const modelInfoData = Array.isArray(modelInfoResponse.data)
      ? modelInfoResponse.data
      : config.useMockData ? await getMockModelInfo() : [];

    // If we don't have model info data, return an error
    if (modelInfoData.length === 0) {
      console.error("[BUILD] No model info data available");
      return {
        data: [],
        status: 404,
        message: "Keine Modellinformationen verfügbar"
      };
    }

    // Process model data: ensure all models have an ID and name/key are the same
    const processedModelData = modelInfoData.map((modelInfo: ModelInfo) => {
      if (!modelInfo || !modelInfo.id) {
        console.warn("[BUILD] Model info missing ID:", modelInfo);
        // Generate an ID based on the name
        const modelName = modelInfo.name || modelInfo.key || `model-${Math.random().toString(36).substring(2, 9)}`;
        return {
          ...modelInfo,
          id: modelName,
          name: modelName,
          key: modelName
        };
      }
      
      // Ensure name and key are the same
      const modelName = modelInfo.name || modelInfo.key || modelInfo.id;
      return {
        ...modelInfo,
        name: modelName,
        key: modelName
      };
    });

    // Transform to ModelData format
    const transformedData: ModelData[] = processedModelData.map((modelInfo: ModelInfo) => {
      return {
        // Basic model information
        id: modelInfo.id,
        name: modelInfo.name,
        key: modelInfo.key,
        provider: modelInfo.provider,
        description: modelInfo.description,
        capabilities: modelInfo.capabilities,
        modelGroup: modelInfo.modelGroup,
        isAvailable: modelInfo.isAvailable,
        confidentiality: modelInfo.confidentiality,
        mode: modelInfo.mode,
        
        // Cost information from /info endpoint
        inputCostPerToken: modelInfo.inputCostPerToken,
        outputCostPerToken: modelInfo.outputCostPerToken,
        
        // Context/token limits from /info endpoint
        contextWindow: modelInfo.contextWindow || modelInfo.maxInputTokens || modelInfo.maxTokens,
        maxTokens: modelInfo.maxTokens || modelInfo.maxOutputTokens,
        maxInputTokens: modelInfo.maxInputTokens,
        maxOutputTokens: modelInfo.maxOutputTokens,
        
        // Support properties from /info endpoint (use snake_case versions as primary)
        supportsFunctionCalling: modelInfo.supportsFunctionCalling,
        supportsParallelFunctionCalling: modelInfo.supportsParallelFunctionCalling,
        supportsResponseSchema: modelInfo.supportsResponseSchema,
        supportsVision: modelInfo.supportsVision,
        supportsPromptCaching: modelInfo.supportsPromptCaching,
        supportsSystemMessages: modelInfo.supportsSystemMessages,
        supportsToolChoice: modelInfo.supportsToolChoice,
        supportsWebSearch: modelInfo.supportsWebSearch,
        supportsNativeStreaming: modelInfo.supportsNativeStreaming,
        supportsPdfInput: modelInfo.supportsPdfInput,
        supportsAudioInput: modelInfo.supportsAudioInput,
        supportsAudioOutput: modelInfo.supportsAudioOutput,
        supportsAssistantPrefill: modelInfo.supportsAssistantPrefill,
        supportsEmbeddingImageInput: modelInfo.supportsEmbeddingImageInput,
        supportsReasoning: modelInfo.supportsReasoning,
      };
    });

    console.log(`[BUILD] Successfully processed ${transformedData.length} unique models`);

    return {
      data: transformedData,
      status: 200,
      message: "Success"
    };
  } catch (error) {
    console.error("[BUILD] Error in fetchCombinedModelData:", error);
    
    // Use mock data as a fallback in case of error
    if (config.useMockData) {
      console.log("[BUILD] Using mock data as fallback");
      const mockData = await getMockModelInfo();
      return {
        data: mockData as ModelData[],
        status: 200,
        message: "Verwende Mock-Daten aufgrund eines Fehlers"
      };
    }
    
    return {
      data: [],
      status: 500,
      message: error instanceof Error ? error.message : "Unbekannter Fehler beim Abrufen der Modelldaten"
    };
  }
}

// Mock data function - comprehensive demo data
async function getMockModelInfo(): Promise<ModelInfo[]> {
  console.log("[BUILD] Loading mock model data");
  return [
    {
      id: "anthropic/claude-3.5-sonnet",
      name: "anthropic/claude-3.5-sonnet",
      provider: "anthropic",
      description: "Claude 3.5 Sonnet - Advanced reasoning model",
      capabilities: ["text", "vision", "function_calling"],
      modelGroup: "claude-3.5",
      isAvailable: true,
      confidentiality: "confidential",
      mode: "chat",
      maxTokens: 8192,
      maxInputTokens: 200000,
      maxOutputTokens: 8192,
      contextWindow: 200000,
      inputCostPerToken: 0.003,
      outputCostPerToken: 0.015,
      supportsFunctionCalling: true,
      supportsVision: true,
      supportsSystemMessages: true,
      supportsPdfInput: false,
      supportsAudioInput: false,
      supportsAudioOutput: false,
      supportsPromptCaching: true,
      supportsReasoning: true,
      key: "anthropic/claude-3.5-sonnet"
    },
    {
      id: "anthropic/claude-3.7-sonnet",
      name: "anthropic/claude-3.7-sonnet",
      provider: "anthropic",
      description: "Claude 3.7 Sonnet - Latest reasoning model",
      capabilities: ["text", "vision", "function_calling"],
      modelGroup: "claude-3.7",
      isAvailable: true,
      confidentiality: "confidential",
      mode: "chat",
      maxTokens: 8192,
      maxInputTokens: 200000,
      maxOutputTokens: 8192,
      contextWindow: 200000,
      inputCostPerToken: 0.003,
      outputCostPerToken: 0.015,
      supportsFunctionCalling: true,
      supportsVision: true,
      supportsSystemMessages: true,
      supportsPdfInput: false,
      supportsAudioInput: false,
      supportsAudioOutput: false,
      supportsPromptCaching: true,
      supportsReasoning: true,
      key: "anthropic/claude-3.7-sonnet"
    },
    {
      id: "aws/claude-3.5-sonnet",
      name: "aws/claude-3.5-sonnet",
      provider: "bedrock",
      description: "Claude 3.5 Sonnet via AWS Bedrock",
      capabilities: ["text", "vision"],
      modelGroup: "claude-3.5",
      isAvailable: true,
      confidentiality: "internal",
      mode: "chat",
      maxTokens: 4096,
      maxInputTokens: 200000,
      maxOutputTokens: 4096,
      contextWindow: 200000,
      inputCostPerToken: 0.003,
      outputCostPerToken: 0.015,
      supportsFunctionCalling: false,
      supportsVision: true,
      supportsSystemMessages: true,
      supportsPdfInput: false,
      supportsAudioInput: false,
      supportsAudioOutput: false,
      supportsPromptCaching: false,
      supportsReasoning: false,
      key: "aws/claude-3.5-sonnet"
    },
    {
      id: "azure/DeepSeek-R1",
      name: "azure/DeepSeek-R1",
      provider: "azure_ai",
      description: "DeepSeek R1 reasoning model",
      capabilities: ["text", "reasoning"],
      modelGroup: "deepseek-r1",
      isAvailable: true,
      confidentiality: "public",
      mode: "chat",
      maxTokens: 8192,
      maxInputTokens: 128000,
      maxOutputTokens: 8192,
      contextWindow: 128000,
      inputCostPerToken: 0.00135,
      outputCostPerToken: 0.0054,
      supportsFunctionCalling: false,
      supportsVision: false,
      supportsSystemMessages: true,
      supportsPdfInput: false,
      supportsAudioInput: false,
      supportsAudioOutput: false,
      supportsPromptCaching: false,
      supportsReasoning: true,
      key: "azure/DeepSeek-R1"
    },
    {
      id: "azure/gpt-4.1",
      name: "azure/gpt-4.1",
      provider: "azure",
      description: "GPT-4.1 via Azure OpenAI",
      capabilities: ["text", "vision", "function_calling"],
      modelGroup: "gpt-4.1",
      isAvailable: true,
      confidentiality: "internal",
      mode: "chat",
      maxTokens: 32768,
      maxInputTokens: 1047576,
      maxOutputTokens: 32768,
      contextWindow: 1047576,
      inputCostPerToken: 0.002,
      outputCostPerToken: 0.008,
      supportsFunctionCalling: true,
      supportsVision: true,
      supportsSystemMessages: true,
      supportsPdfInput: false,
      supportsAudioInput: false,
      supportsAudioOutput: false,
      supportsPromptCaching: false,
      supportsReasoning: false,
      key: "azure/gpt-4.1"
    },
    {
      id: "azure/gpt-4.1-nano",
      name: "azure/gpt-4.1-nano",
      provider: "azure",
      description: "GPT-4.1 Nano - Lightweight version",
      capabilities: ["text", "function_calling"],
      modelGroup: "gpt-4.1",
      isAvailable: true,
      confidentiality: "internal",
      mode: "chat",
      maxTokens: 32768,
      maxInputTokens: 1047576,
      maxOutputTokens: 32768,
      contextWindow: 1047576,
      inputCostPerToken: 0.0001,
      outputCostPerToken: 0.0004,
      supportsFunctionCalling: true,
      supportsVision: false,
      supportsSystemMessages: true,
      supportsPdfInput: false,
      supportsAudioInput: false,
      supportsAudioOutput: false,
      supportsPromptCaching: false,
      supportsReasoning: false,
      key: "azure/gpt-4.1-nano"
    },
    {
      id: "azure/gpt-4o",
      name: "azure/gpt-4o",
      provider: "azure",
      description: "GPT-4o via Azure OpenAI",
      capabilities: ["text", "vision", "function_calling"],
      modelGroup: "gpt-4o",
      isAvailable: true,
      confidentiality: "internal",
      mode: "chat",
      maxTokens: 16384,
      maxInputTokens: 128000,
      maxOutputTokens: 16384,
      contextWindow: 128000,
      inputCostPerToken: 0.0025,
      outputCostPerToken: 0.01,
      supportsFunctionCalling: true,
      supportsVision: true,
      supportsSystemMessages: true,
      supportsPdfInput: false,
      supportsAudioInput: false,
      supportsAudioOutput: false,
      supportsPromptCaching: false,
      supportsReasoning: false,
      key: "azure/gpt-4o"
    },
    {
      id: "azure/gpt-4o-mini",
      name: "azure/gpt-4o-mini",
      provider: "azure",
      description: "GPT-4o Mini - Cost-effective version",
      capabilities: ["text", "vision", "function_calling"],
      modelGroup: "gpt-4o",
      isAvailable: true,
      confidentiality: "internal",
      mode: "chat",
      maxTokens: 16384,
      maxInputTokens: 128000,
      maxOutputTokens: 16384,
      contextWindow: 128000,
      inputCostPerToken: 0.000017,
      outputCostPerToken: 0.000066,
      supportsFunctionCalling: true,
      supportsVision: true,
      supportsSystemMessages: true,
      supportsPdfInput: false,
      supportsAudioInput: false,
      supportsAudioOutput: false,
      supportsPromptCaching: false,
      supportsReasoning: false,
      key: "azure/gpt-4o-mini"
    },
    {
      id: "azure/o1",
      name: "azure/o1",
      provider: "azure",
      description: "OpenAI o1 reasoning model",
      capabilities: ["text", "reasoning"],
      modelGroup: "o1",
      isAvailable: true,
      confidentiality: "public",
      mode: "chat",
      maxTokens: 100000,
      maxInputTokens: 200000,
      maxOutputTokens: 100000,
      contextWindow: 200000,
      inputCostPerToken: 0.0165,
      outputCostPerToken: 0.066,
      supportsFunctionCalling: false,
      supportsVision: false,
      supportsSystemMessages: false,
      supportsPdfInput: false,
      supportsAudioInput: false,
      supportsAudioOutput: false,
      supportsPromptCaching: false,
      supportsReasoning: true,
      key: "azure/o1"
    }
  ];
}

// Export configuration type for use in build scripts
export type { ApiConfig };