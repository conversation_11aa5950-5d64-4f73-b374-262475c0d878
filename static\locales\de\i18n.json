{"site": {"title": "<PERSON><PERSON>", "description": "LLM Browser - Vergleiche und bewerte Sprachmodelle"}, "nav": {"models": "Models", "blog": "Blog", "recommendations": "Empfehlungen", "benchmarks": "Benchmarks", "api_usage": "API-Nutzung@iteratec"}, "footer": {"copyright": "© 2025 LLM Browser - Vise-Coding", "built_with": "Erstellt mit Astro, React, TypeScript & Tailwind CSS"}, "language_selector": {"label": "Sprache wählen", "de": "De<PERSON>ch", "en": "<PERSON><PERSON><PERSON>", "pl": "Polnisch"}, "language": "<PERSON><PERSON><PERSON>", "redirect": "Weiterleitung zur Modell-Übersicht...", "meta_description": "LLM Browser - Vergleiche und bewerte Sprachmodelle", "vise_coding": "Vise-Coding", "loading": "Laden...", "models": {"header": "LL<PERSON> Modell-<PERSON><PERSON><PERSON>", "description": "Entdecke {{count}} KI-Modelle mit detaillierten Informationen zu Preisen, Fähigkeiten und Benchmark-Ergebnissen. Ziel ist es, die Auswahl geeigneter Modelle für die tägliche Arbeit interaktiver, einfacher und transparenter zu machen. Andernfalls müssen die Informationen aus vielen Quellen zusammengesucht werden. Basierend auf Live-Daten von iteraGPT (LiteLLM) und AI-generierten Model Cards aus verschiedenen Quellen.", "filters": {"all": "Alle", "security": "Sicherheit", "mode": "Modus", "confidential": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "internal": "Intern", "open": "Open", "public": "<PERSON><PERSON><PERSON><PERSON>", "chat": "Cha<PERSON>", "completion": "Completion", "embedding": "Embedding", "image_generation": "Bildgenerierung", "search_placeholder": "Suche nach Name, Anbieter oder Modellgruppe...", "of": "von", "models": "<PERSON><PERSON>"}, "stats": {"total_models": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "benchmarks": "Benchmarks", "average_score": "Durchschnittswert", "top_performer": "Top-Performer", "filtered_count": "{{filtered}} von {{total}} <PERSON>len"}, "comparison": {"title": "Modellvergleich ({{count}} Modelle)", "reset_selection": "Auswahl zurücksetzen", "property": "Eigenschaft", "provider": "<PERSON><PERSON><PERSON>", "litellm_availability": "LiteLLM Verfügbarkeit", "available": "Verfügbar", "model_card_only": "Nur Model Card", "context_window": "Kontext-Fenster", "max_output_tokens": "<PERSON> Output Tokens", "input_cost": "Input Kosten (pro 1M Tokens)", "output_cost": "Output Kosten (pro 1M Tokens)", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "capabilities": "Fähigkeiten", "supported_platforms": "Unterstützte Plattformen", "metric": "<PERSON><PERSON>", "range": "<PERSON><PERSON><PERSON>", "no_details_available": "<PERSON><PERSON> detail<PERSON>ten Informationen verfügbar.", "other_benchmarks": "Weitere Benchmarks", "at": "bei", "and": "und", "well_formed_code": "well formed code", "category": "<PERSON><PERSON><PERSON>", "difficulty": "Schwierigkeit", "variants": "<PERSON><PERSON><PERSON>", "not_available": "N/V", "aider_polyglot_short": "Aider-Polyglot (o)", "website": "Website", "paper": "Publikation", "aider_benchmark": {"title": "Aider's polyglot benchmark", "description": "<PERSON>t die Fähigkeit von Modellen, Code in verschiedenen Programmiersprachen zu bearbeiten und zu verbessern.", "metric": "Pass Rate (2nd attempt)", "range": "0-100%", "fallback_description": "Aider's coding benchmark - misst die Fähigkeit von Modellen, Code zu bearbeiten und zu verbessern."}}, "capabilities": {"vision": "Vision", "pdf_input": "PDF Input", "audio_input": "Audio Input", "audio_output": "Audio Output", "embedding_image": "Embedding Image", "function_calling": "Function Calling", "prompt_caching": "Prompt Caching", "reasoning": "Reasoning", "system_messages": "System Messages"}, "table": {"pagination": {"showing": "Zeige {{start}} bis {{end}} von {{total}} Modellen", "previous": "Zurück", "next": "<PERSON><PERSON>"}, "headers": {"select": "Auswählen", "security": "Sicherheit", "model_card": "Model Card", "litellm_status": "LiteLLM/Status", "name": "Name", "provider": "<PERSON><PERSON><PERSON>", "mode": "Modus", "context": "Kontext", "max_output": "Max Output", "input_cost_per_million": "Input Kosten/1M", "output_cost_per_million": "Output Kosten/1M", "polyglot_score": "Polyglot Score", "support": "Support", "details": "Details", "show_filters": "Filter anzeigen", "fullscreen": "Vollbild"}, "tooltips": {"confidential": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "internal": "Intern", "public": "<PERSON><PERSON><PERSON><PERSON>", "model_card_available": "Model Card verfügbar", "deprecated": "<PERSON><PERSON><PERSON>", "shutdown_date": "Abschaltung: {{date}}", "litellm_available": "LiteLLM verfügbar", "model_card_only": "Nur Model Card", "chat": "Cha<PERSON>", "embedding": "Embedding", "image": "Bild", "vision_processing": "Vision/Bildverarbeitung", "pdf_input": "PDF-Eingabe", "audio_input": "Audio-Eingabe", "audio_output": "Audio-Ausgabe", "embedding_image_input": "Embedding-Bild-Eingabe", "function_calling": "Funktionsaufrufe", "prompt_caching": "Prompt-Caching", "reasoning": "Reasoning", "system_messages": "System-Nachrichten", "capability_vision": "Vision/Bildverarbeitung", "capability_pdf_input": "PDF-Eingabe", "capability_audio_input": "Audio-Eingabe", "capability_audio_output": "Audio-Ausgabe", "capability_embedding_image_input": "Embedding-Bild-Eingabe", "capability_function_calling": "Funktionsaufrufe", "capability_prompt_caching": "Prompt-Caching", "capability_reasoning": "Reasoning", "capability_system_messages": "System-Nachrichten"}, "empty_state": "<PERSON><PERSON> gefunden", "select_model": "Modell {{name}} auswählen", "details_button": "Details"}, "detail_dialog": {"tabs": {"overview": "Überblick", "technical": "Technik", "capabilities": "Fähigkeiten", "performance": "Performance", "pricing": "<PERSON><PERSON>", "benchmarks": "Benchmarks", "availability": "Verfügbarkeit", "recommendations": "Empfehlungen"}, "basic_info": {"title": "Grundlegende Informationen", "provider": "<PERSON><PERSON><PERSON>", "model_group": "Modellgruppe", "availability": "Verfügbarkeit", "available": "Verfügbar", "unavailable": "Nicht verfügbar", "status": "Status", "release_date": "Veröffentlichungsdatum", "description": "Beschreibung", "technical_id": "Technische Identifikation", "model_id": "Model ID", "model_card_id": "Model Card ID", "internal_key": "<PERSON><PERSON>hlü<PERSON>"}, "capabilities": {"core_title": "Grundlegende Fähigkeiten", "modality_title": "Modalitäten", "technical_title": "Technische Fähigkeiten", "additional_title": "Zusätzliche Features", "openai_params_title": "Unterstützte OpenAI Parameter", "vision": "Vision", "pdf_input": "PDF-Eingabe", "audio_input": "Audio-Eingabe", "audio_output": "Audio-Ausgabe", "function_calling": "Funktionsaufrufe", "prompt_caching": "Prompt-Caching", "reasoning": "Reasoning", "system_messages": "System-Nachrichten", "parallel_function_calling": "<PERSON><PERSON><PERSON>aufru<PERSON>", "response_schema": "Response Schema", "tool_choice": "Tool Choice", "native_streaming": "Native Streaming", "assistant_prefill": "Assistant Prefill", "embedding_image_input": "Embedding-Bild-Eingabe", "multilingual_support": "Mehrsprachiger Support", "image_generation": "Bildgenerierung", "litellm_provisioning": "LiteLLM Provisioning", "recommended": "<PERSON><PERSON><PERSON><PERSON>"}, "technical_specs": {"title": "Technische Spezifikationen", "token_limits": "Token-Limits", "context_window": "Kontext-Fenster", "max_output_tokens": "<PERSON> Output Tokens", "max_input_tokens": "Max Input Tokens", "max_reasoning_tokens": "<PERSON>s", "architecture_params": "Architektur & Parameter", "architecture": "Architektur", "parameter_count": "Parameter Count", "supported_modalities": "Unterstützte Modalitäten", "input_types": "Input-Typen", "output_types": "Output-Typen", "input_limitations": "Input-Limitierungen", "max_images": "<PERSON>. Bilder", "max_image_size": "Max. Bildgröße", "max_audio_length": "<PERSON><PERSON>", "max_video_length": "<PERSON><PERSON>", "supported_mime_types": "Unterstützte MIME-Typen", "legacy_api_data": "Legacy API Daten", "modalities": "Modalitäten", "output_modalities": "Output-Modalitäten", "vector_size": "Vector Size"}, "pricing": {"title": "<PERSON><PERSON>", "standard_pricing": "Standard-Preise", "input_cost_per_1m": "Input Kosten (pro 1M Tokens)", "output_cost_per_1m": "Output Kosten (pro 1M Tokens)", "currency": "Währung", "caching_costs": "Caching-Kosten", "cache_hits": "<PERSON><PERSON> (pro 1M Tokens)", "cache_writes": "<PERSON><PERSON> (pro 1M Tokens)", "cache_hits_description": "Deutlich günstiger für wiederholte Inhalte", "cache_writes_description": "Einmalige Kosten für Cache-Erstellung", "reasoning_costs": "Reasoning-<PERSON><PERSON>", "reasoning_tokens": "Reasoning Tokens (pro 1M)", "completion_tokens": "Completion Tokens (pro 1M)", "reasoning_description": "Interne Denkprozesse", "completion_description": "Finale Antwort-Tokens", "reasoning_note": "Bei Reasoning-Modellen werden sowohl Reasoning- als auch Completion-Tokens berechnet. Die tatsächlichen Kosten können variieren je nach Komplexität der Anfrage.", "batch_processing": "Batch-Verarbeitung (50% Rabatt)", "batch_input": "Input (pro 1M Tokens)", "batch_output": "Output (pro 1M Tokens)", "standard_label": "Standard", "legacy_pricing": "Legacy API-Preise (pro 1K Tokens)", "alternative_pricing": "Alternative Preismodelle", "per_character_input": "Pro Zeichen (Input)", "per_character_output": "Pro Zeichen (Output)", "per_query": "Pro Anfrage", "per_second_input": "Pro Sekunde (Input)", "per_second_output": "Pro Sekunde (Output)", "per_image": "Pro Bild", "audio_pricing": "Audio-Preise", "audio_input_token": "Audio Input (pro Token)", "audio_output_token": "Audio Output (pro Token)", "cache_pricing_api": "<PERSON><PERSON><PERSON><PERSON><PERSON> (API)", "cache_creation": "<PERSON><PERSON> (pro Token)", "cache_read": "<PERSON><PERSON> (pro Token)", "no_pricing_info": "Keine Preisinformationen verfügbar. Bitte prüfen Sie die Anbieter-Dokumentation für aktuelle Preise."}, "benchmarks": {"title": "Benchmarks", "aider_polyglot": "Aider-<PERSON>y<PERSON><PERSON>", "polyglot_score": "Polyglot Score", "tests_passed": "Tests bestanden", "benchmark_details": "Benchmark Details", "test_cases": "Test Cases", "pass_rate_2": "Pass Rate 2", "pass_rate_1": "Pass Rate 1", "well_formed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "total_cost": "Gesamtkosten", "edit_format": "Edit Format", "command_used": "Verwendeter Befehl", "detailed_stats": "Detaillierte Statistiken", "model_card_benchmarks": "Model Card Benchmarks", "other_benchmarks": "Weitere Benchmarks", "no_benchmark_data": "<PERSON><PERSON>-<PERSON><PERSON>", "no_benchmark_description": "<PERSON><PERSON><PERSON> dieses Modell sind derzeit keine Benchmark-Ergebnisse verfügbar.", "benchmark_info": "Benchmark-Information", "aider_description": "Testet Code-Editing-Fähigkeiten über verschiedene Programmiersprachen hinweg. Der Score gibt den Prozentsatz erfolgreich bearbeiteter Aufgaben an.", "model_card_description": "Umfassende Bewertungen in verschiedenen Kategorien wie Reasoning, Mathematik, Code-Generierung und mehr. Die Scores sind je nach Benchmark-Typ unterschiedlich skaliert.", "benchmark_note": "Benchmark-Ergebnisse können je nach Testbedingungen, Version und Konfiguration variieren. Sie bieten eine Orientierung, aber die tatsächliche Leistung kann je nach Anwendungsfall abweichen."}, "availability": {"title": "Verfügbarkeit", "no_availability_data": "<PERSON>ine Verfügbarkeitsdaten", "no_availability_description": "Verfügbarkeitsinformationen sind nur für Modelle mit vollständigen Model Cards verfügbar.", "supported_platforms": "Unterstützte Plattformen", "platform_specific_ids": "Plattform-spezifische IDs", "regional_availability": "Regionale Verfügbarkeit", "global_available": "Weltweit verfügbar", "data_processing_regions": "Datenverarbeitungsregionen", "data_processing_description": "Diese Regionen werden für die ML-Verarbeitung und Datenbehandlung verwendet.", "security_features": "Sicherheitsfeatures", "data_residency": "Data Residency", "cmek_support": "CMEK Support", "vpc_support": "VPC Support", "access_transparency": "Access Transparency", "available": "Verfügbar", "not_available": "Nicht verfügbar", "compliance_standards": "Compliance Standards", "usage_types": "Nutzungstypen", "dynamic_shared_quota": "Dynamic Shared Quota", "provisioned_throughput": "Provisioned Throughput", "fixed_quota": "Fixed Quota", "additional_info": "Zusätzliche Informationen", "availability_note": "Die Verfügbarkeit kann sich je nach Region, Plattform und Zeit ändern. Bitte prüfen Sie die aktuelle Verfügbarkeit bei Ihrem gewählten Anbieter.", "data_source": "<PERSON><PERSON><PERSON><PERSON>", "last_updated": "Letzte Aktualisierung"}}}, "blog": {"title": "LLM Blog", "description": "Aktuelle Insights zu AI-Modellen, Release Notes und Benchmark-Analysen. Bleiben Sie auf dem Laufenden über die neuesten Entwicklungen in der LLM-Landschaft.", "sectionModelAnalysis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sectionModelAnalysisDesc": "Detaillierte Reviews der neuesten AI-Modelle", "sectionReleaseNotes": "Release Notes", "sectionReleaseNotesDesc": "Neueste Updates und Änderungen an Modellen", "sectionBenchmarkAnalysis": "Benchmark-<PERSON><PERSON><PERSON>", "sectionBenchmarkAnalysisDesc": "Tiefgehende Auswertungen von Performance-Tests", "sectionIndustryNews": "Branchen-News", "sectionIndustryNewsDesc": "Wichtige Entwicklungen im AI-Markt", "stats": {"articles": "Blog-Artikel", "categories": "<PERSON><PERSON><PERSON>", "featured": "Featured Posts", "tags": "Tags"}, "featured_articles": "Featured Art<PERSON>l", "loading_articles": "Lade Artikel...", "articles": "Artikel", "found": "gefunden", "clear_all_filters": "Alle Filter zurücksetzen", "no_articles_found": "<PERSON><PERSON> gefunden", "try_different_search": "Versuchen Sie andere Suchbegriffe oder Filter.", "no_articles_available": "Es sind noch keine Blog-Artikel verfügbar.", "reset_filters": "<PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste", "back_to_blog": "Zurück zum Blog", "available_in": "<PERSON><PERSON><PERSON>üg<PERSON> in:", "available_languages": "Verfügbare S<PERSON>chen:", "reading_time": "<PERSON><PERSON>", "related_information": "Verwandte Informationen", "related_models": "Verwandte Modelle", "related_benchmarks": "Verwandte Benchmarks", "similar_articles": "Ähnliche Artikel"}, "recommendations": {"title": "Empfehlungen für Unternehmen", "description": "Intelligentes Empfehlungssystem für {{totalModels}} LLM-<PERSON><PERSON> von {{totalProviders}} Anbietern. Basierend auf Standard-Anwendungsfällen in Unternehmen werden die optimalen Modelle für verschiedene Szenarien empfohlen. Berücksichtigt werden Benchmark-Performance, Capabilities, Kosten und weitere Faktoren für fundierte Entscheidungen.", "availableModels": "Verfügbare Modelle", "providers": "<PERSON><PERSON><PERSON>", "gaStatus": "GA Status", "avgInputCost": "⌀ Input Kosten", "avgOutputCost": "⌀ Output Kosten", "perMillion": "pro 1M Tokens", "pageTitle": "Model-Empfehlungen für Unternehmen", "pageDescription": "Intelligente Empfehlungen für die Auswahl des optimalen LLM-Modells basierend auf Standard-Anwendungsfällen in Unternehmen. Berücksichtigt Performance, Kosten, Capabilities und weitere Faktoren.", "topUseCases": "Top Use Cases:", "benchmarksUsed": "Verwendete Benchmarks:", "requiredCapabilities": "Benötigte Capabilities:", "category": "Kategorie:", "priority": {"high": "Hoch", "medium": "<PERSON><PERSON><PERSON>", "low": "<PERSON><PERSON><PERSON>", "label": "Priorität"}, "topRecommendations": "Top Empfehlungen:", "costEffectiveness": {"high": "Kostengü<PERSON>ig", "medium": "Standard", "low": "<PERSON><PERSON>"}, "qualityCheck": "Quality-Check", "stats": {"useCases": "Use Cases", "models": "<PERSON><PERSON>", "recommendations": "Empfehlungen", "excellent": "Exzellent", "avgPerUseCase": "⌀ pro Use Case"}, "tabs": {"overview": "Übersicht", "topModels": "Top Modelle", "highPriority": "Kritische Use Cases", "allUseCases": "Alle Use Cases"}, "sections": {"bestOverallModels": "Beste Gesamtmodelle", "bestOverallDescription": "Modelle mit der besten durchschnittlichen Performance über alle Use Cases hinweg.", "criticalUseCases": "Kritische Use Cases", "criticalDescription": "Empfehlungen für geschäftskritische Anwendungsfälle mit hoher Priorität.", "allUseCasesTitle": "Alle Use Cases", "allUseCasesDescription": "Detaillierte Empfehlungen für jeden Standard-Anwendungsfall."}}, "benchmark": {"title": "Benchmark Ergebnisse", "description": "Detaillierte Analyse der Polyglot-Benchmark-Ergebnisse für {{totalBenchmarks}} Benchmark-Tests.", "testedModels": "Getestete Modelle", "averageScore": "Durchschnittsscore", "highestScore": "Höchster Score", "testCases": "Testfälle", "about": "Über den Polyglot Benchmark:", "aboutText1": "Dieser Benchmark basiert auf Exercism-Coding-Übungen und testet die Fähigkeit von Sprachmodellen, komplexe Programmierprobleme in 6 verschiedenen Sprachen zu lösen:", "languages": "C++, Go, Java, JavaScript, Python und Rust", "aboutText2": "Der Benchmark umfasst die {{hardest}} schwierigsten Übungen aus insgesamt {{total}} verfügbaren Exercism-Problemen und wurde entwickelt, um deutlich herausfordernder zu sein als frühere Benchmarks. Die Scores basieren auf der Anzahl erfolgreich gelöster Coding-Probleme und bieten eine präzise Bewertung der Code-Editing-Fähigkeiten moderner LLMs."}, "qc": {"title": "Benchmark-Vergleich für LLM-Modelle", "header": "Benchmark-Vergleich", "description": "Detaillierte Benchmark-Analyse für {{modelCount}} LLM-Modelle. Vergleiche die Performance verschiedener Modelle in {{benchmarkCount}} verschiedenen Benchmarks. Diese Übersicht ermöglicht einen direkten Vergleich der tatsächlichen Benchmark-Werte aus den Model Cards.", "availableBenchmarks": "Verfügbare Benchmarks", "avgBenchmarksPerModel": "⌀ Benchmarks/Modell", "mostCommonBenchmark": "Häufigster Benchmark", "modelsWithBenchmarks": "Modelle mit Benchmarks", "topBenchmarks": "Top 5 Benchmarks (nach Verfügbarkeit)", "models": "<PERSON><PERSON>"}, "components": {"collapsible_header": {"show_info": "Info anzeigen", "hide_info": "Info ausblenden"}}, "calculation_methodology": {"title": "Berechnungsmethodik", "dialog_title": "Berechnungsmethodik der Empfehlungen", "dialog_description": "Detaillierte Erklärung des intelligenten Empfehlungssystems und der verwendeten Algorithmen", "scoring_overview": "Scoring-Algorithmus Übersicht", "scoring_description": "Unser intelligentes Empfehlungssystem bewertet jedes Modell für jeden Use Case anhand eines 100-Punkte-Systems. Der Gesamtscore setzt sich aus fünf gewichteten Faktoren zusammen:", "benchmark_performance": "Benchmark Performance", "required_capabilities": "Erforderliche Capabilities", "cost_efficiency": "Kosten-Effizienz", "latency_speed": "Latenz/Geschwindigkeit", "availability": "Verfügbarkeit", "benchmark_calculation": "Benchmark-Performance (45% Gewichtung)", "benchmark_description": "Benchmark-Scores werden intelligent normalisiert und Use-Case-spezifisch gewichtet:", "critical_coding_benchmarks": "Kritische Coding-Benchmarks (1.5x Gewichtung):", "score_normalization": "Score-Normalisierung:", "arena_scores": "Arena Scores: 1300=60pts, 1350=70pts, 1400=85pts, 1450+=95pts", "elo_ratings": "ELO Ratings: 1200=30pts, 1800=70pts, 2400+=90pts", "standard_benchmarks": "Standard Benchmarks: Direkte 0-100% Übernahme", "other_factors": "Weitere Bewertungsfaktoren", "capabilities_score": "Capabilities (25%)", "capabilities_description": "Proporional: (erfüllte Capabilities / benötigte Capabilities) × 100", "cost_score": "Kosten-Score (10%)", "budget_range": "Budget (0-1$): 85-95 Punkte", "standard_range": "Standard (1-5$): 70-85 Punkte", "premium_range": "Premium (5$+): 50-70 Punkte", "latency_score": "Latenz-Score (15%)", "fastest": "Fastest: 100pts", "fast": "Fast: 85pts", "moderately_fast": "Moderately Fast: 70pts", "slow_slowest": "Slow/Slowest: 50/30pts", "context_window_score": "Context Window Score", "availability_score": "Verfügbarkeit-Score (5%)", "ga_score": "GA: 100pts", "preview_score": "Preview: 80pts", "other_score": "Andere: 60pts", "quality_factors": "Qualitätsfaktoren & Coding-Optimierungen (2025)", "benchmark_penalties": "Benchmark-Penalties", "penalty_description": "-5 Punkte pro Benchmark mit <40% in Factuality/Knowledge Kategorien", "multimodal_bonus": "Multimodal-Bonus", "multimodal_description": "+5 Punkte für Vision-Capabilities bei Data-Analysis/Documentation Use Cases", "coding_adjustments": "Spezielle Coding-Anpassungen", "coding_use_cases": "Coding Use Cases: +10% zusätzliche Benchmark-Gewichtung", "context_bonus": "Context Bonus: +3 <PERSON><PERSON> für >500k/200k, +2 <PERSON><PERSON> für >128k Context Window", "affected_use_cases": "Betroffene Use Cases: code-generation, code-review, debugging, refactoring, testing, api-integration, devops-automation", "final_calculation": "Finale Score-<PERSON><PERSON><PERSON><PERSON>ng", "total_score_formula": "Total Score = (Benchmark × 45%) + (Capabilities × 25%) + (Cost × 10%) + (Latency × 15%) + (Availability × 5%) + Multimodal-Bonus + Coding-Anpassungen - Benchmark-Penalties", "use_case_mappings": "Use Case Benchmark-Mappings", "mappings_description": "Jeder Use Case verwendet spezifische Benchmarks zur Bewertung. Die wichtigsten Code-Benchmarks werden priorisiert:", "code_generation": "Code-generierung/-review/-debugging/-refactoring/-testing:", "api_integration": "API-Integration:", "devops_automation": "DevOps-Automatisierung:", "data_analysis": "Datenanalyse:", "learning_documentation": "Lernen/Dokumentation:", "rating_scale": "Bewertungsskala & Kategorisierung", "suitability_categories": "Suitability-<PERSON><PERSON><PERSON>:", "excellent": "Ausgezeichnet", "good": "Gut", "acceptable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "limited": "Begrenzt", "recommendation_categories": "Empfehlungskategorien:", "recommended": "<PERSON><PERSON><PERSON><PERSON>", "alternative": "Alternative", "not_recommended": "<PERSON>cht empfohlen", "cost_effectiveness": "Kostenwirksamkeit:", "high_cost_eff": "Hoch: Budget + Score >65", "medium_cost_eff": "Mittel: Standard + Score >70", "low_cost_eff": "Niedrig: Premium-Modelle", "disclaimer_title": "<PERSON><PERSON><PERSON><PERSON>", "disclaimer_text": "Diese Empfehlungen basieren auf algorithmischen Berechnungen und dienen als Orientierungshilfe. Für produktive Anwendungen sollten immer eigene Tests und Evaluationen durchgeführt werden. Preise und Verfügbarkeiten können sich ändern. Stand der Daten:"}, "use_case_dashboard": {"title": "Use Case Empfehlungen Dashboard", "description": "Übersicht über die besten Modell-Empfehlungen für Standard-Anwendungsfälle in Unternehmen. Basiert auf Benchmark-Performance, Capabilities, Kosten und weiteren Faktoren.", "use_cases": "Use Cases", "available_models": "Verfügbare Modelle", "total_recommendations": "Gesamte Empfehlungen", "avg_recommendations": "⌀ Empfehlungen/Use Case", "search_placeholder": "Use Cases durchsuchen...", "select_category": "<PERSON><PERSON><PERSON> w<PERSON>", "all_categories": "Alle Kategorien", "sort_by_name": "Nach Name", "sort_by_recommended": "<PERSON><PERSON> Empfehlung<PERSON>", "recommended_count": "em<PERSON><PERSON>len", "cost_effective": "Kostengü<PERSON>ig", "standard_cost": "Standard", "expensive": "<PERSON><PERSON>", "no_suitable_models": "<PERSON><PERSON> gee<PERSON>eten Modelle gefunden", "additional_models": "weitere Modelle verfügbar", "no_use_cases_found": "<PERSON>ine Use Cases gefunden", "try_different_search": "Versuchen Sie andere Suchbegriffe oder Filter."}}