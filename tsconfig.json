{"extends": "astro/tsconfigs/base", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "jsx": "react-jsx", "jsxImportSource": "react", "allowJs": true, "checkJs": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "types": ["astro/client", "vite/client"]}}