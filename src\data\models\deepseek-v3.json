{"basicInfo": {"modelId": "deepseek-v3@20241226", "displayName": "DeepSeek-V3", "provider": "DeepSeek", "modelFamily": "DeepSeek", "version": "V3", "description": "Ein starkes Mixture-of-Experts (MoE) Sprachmodell mit 671B Gesamtparametern und 37B aktivierten Parametern pro Token. Nutzt Multi-head Latent Attention (MLA) und DeepSeekMoE-Architekturen für effiziente Inferenz und kosteneffektives Training.", "releaseDate": "2024-12-26", "status": "GA", "knowledgeCutoff": "Unbekannt"}, "technicalSpecs": {"contextWindow": 128000, "maxOutputTokens": 8192, "architecture": "MoE Transformer", "parameterCount": "671B (37B aktiviert)", "supportedInputTypes": ["text", "image", "audio", "video", "document"], "supportedOutputTypes": ["text"], "inputLimitations": {"supportedMimeTypes": {"image": ["image/jpeg", "image/png", "image/gif", "image/webp"], "audio": ["audio/wav", "audio/mp3", "audio/flac", "audio/m4a"], "video": ["video/mp4", "video/webm", "video/quicktime"], "document": ["application/pdf", "text/plain"]}}}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": true, "audioOutput": false, "imageGeneration": false, "codeExecution": true, "systemInstructions": true, "promptCaching": false, "batchProcessing": true, "reasoning": true, "thinking": false, "grounding": false, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": true, "webBrowsing": false, "codeInterpreter": true, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": false}, "performance": {"latency": "Fast", "temperature": {"min": 0, "max": 2, "default": 1.0}}, "pricing": {"inputCostPer1MTokens": 0.14, "outputCostPer1MTokens": 0.28, "cachingCosts": {"cacheWrites": 0.14, "cacheHits": 0.014}, "currency": "USD"}, "availability": {"supportedPlatforms": ["HuggingFace", "DeepSeek API"]}, "benchmarks": [{"benchmarkName": "MMLU", "category": "Reasoning & Knowledge", "score": 88.5, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Chat-Version des Modells"}, {"benchmarkName": "MATH", "category": "Mathematics", "score": 90.2, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Chat-Version des Modells"}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 39.2, "metric": "Pass@1", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Chat-Version des Modells"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 42.0, "metric": "Resolved", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Chat-Version des Modells"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 49.6, "metric": "Accuracy", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Chat-Version des Modells"}, {"benchmarkName": "DROP", "category": "Reasoning & Knowledge", "score": 89.0, "metric": "F1", "attemptType": "single attempt", "date": "2024-12-26", "notes": "Base-Version des Modells"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 64.8, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-30", "notes": "DeepSeek V3 0324 Version - Vellum AI Leaderboard"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 27.2, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-05-30", "notes": "454 problems selected, Rank 21/22 - LiveCodeBench Leaderboard"}, {"benchmarkName": "WebDev-Arena", "category": "Code generation", "score": 959.75, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-05-30", "notes": "Real-time AI coding competition, Rank #29 - LMArena WebDev Arena"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 5.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-15", "notes": "Rank 15/15 using Terminus framework - Terminal-Bench Leaderboard"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 59.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-05-30", "notes": "DeepSeek V3 0324 Version - Vellum AI Leaderboard"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 42.9, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "Qwen2.5-VL-32B benchmark data - Vellum AI Leaderboard"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 4.55, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-10", "notes": "DeepSeek-V3 nicht im verfügbaren Leaderboard gelistet - Scale AI Leaderboard"}], "metadata": {"lastUpdated": "2025-06-10T11:33:00Z", "dataSource": "HuggingFace DeepSeek-V3 Model Card, DeepSeek-V3 Technical Report, Vals.ai AIME & MMMU Benchmarks, SWE-bench Leaderboard, Terminal-Bench Leaderboard, WebDev-Arena Leaderboard, LiveCodeBench Leaderboard, Vellum AI Leaderboard", "version": "1.1"}}