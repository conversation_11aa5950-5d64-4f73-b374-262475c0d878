# LiteLLM API - Model Group Info

## Endpoint: `/model_group/info`

This endpoint provides information about model groups in the LiteLLM API.

## Request

```http
GET /model_group/info
```

### Parameters

| Name | Type | Required | Description |
|------|------|----------|-------------|
| model_group | string | Yes | The name of the model group to retrieve information for |

## Response

### Success Response

```json
{
  "model_group": "string",
  "models": [
    {
      "model_name": "string",
      "litellm_params": {
        "model": "string",
        "api_key": "string",
        "additional_params": {}
      },
      "model_info": {
        "id": "string",
        "mode": "string",
        "cost_per_token": 0,
        "context_window": 0
      }
    }
  ],
  "deployment_id": "string",
  "deployment_name": "string",
  "description": "string",
  "model_group_id": "string"
}
```

### Error Response

```json
{
  "detail": "Error message"
}
```

## Example

### Request

```http
GET /model_group/info?model_group=gpt-4-group
```

### Response

```json
{
  "model_group": "gpt-4-group",
  "models": [
    {
      "model_name": "gpt-4",
      "litellm_params": {
        "model": "gpt-4",
        "api_key": "sk-***",
        "additional_params": {
          "temperature": 0.7
        }
      },
      "model_info": {
        "id": "gpt-4-0613",
        "mode": "chat",
        "cost_per_token": 0.00003,
        "context_window": 8192
      }
    },
    {
      "model_name": "gpt-4-turbo",
      "litellm_params": {
        "model": "gpt-4-turbo",
        "api_key": "sk-***",
        "additional_params": {
          "temperature": 0.7
        }
      },
      "model_info": {
        "id": "gpt-4-1106-preview",
        "mode": "chat",
        "cost_per_token": 0.00001,
        "context_window": 128000
      }
    }
  ],
  "deployment_id": "gpt-4-group-001",
  "deployment_name": "GPT-4 Models",
  "description": "Group containing GPT-4 models for high-complexity tasks",
  "model_group_id": "mg_123456"
}
```

## Usage Notes

- This endpoint is useful for retrieving detailed information about model groups configured in your LiteLLM deployment
- The response includes all models within the specified group, along with their parameters and performance characteristics
- You can use this information to make informed decisions about which model to use for specific tasks

## Related Endpoints

- `/model_group/create` - Create a new model group
- `/model_group/update` - Update an existing model group
- `/model_group/delete` - Delete a model group

## Documentation

For more information, visit the [LiteLLM API documentation](https://litellm-api.up.railway.app/).