import js from '@eslint/js';
import tseslint from 'typescript-eslint';
import eslintPluginAstro from 'eslint-plugin-astro';
import react from 'eslint-plugin-react';
import reactHooks from 'eslint-plugin-react-hooks';
import jsxA11y from 'eslint-plugin-jsx-a11y';

export default [
  // Ignore patterns - must be first
  {
    ignores: [
      'dist/',
      'public/',
      'build/',
      'node_modules/',
      '.astro/',
      'coverage/',
      'test-results/',
      '*.min.js',
      '*.bundle.js',
      'scripts/generate-*.js',
      'src/data/*.json',
      '.tmp/',
      'temp/',
      '.vscode/',
      '.idea/',
      '.DS_Store',
      'Thumbs.db',
      '**/*.md',
    ],
  },

  // Base JavaScript configuration
  js.configs.recommended,
  
  // TypeScript configuration (without type-aware rules)
  ...tseslint.configs.recommended,
  
  // Astro configuration
  ...eslintPluginAstro.configs.recommended,
  
  // Global settings
  {
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  },

  // React and JSX configuration for .tsx files
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      react,
      'react-hooks': reactHooks,
      'jsx-a11y': jsxA11y,
    },
    rules: {
      // React rules
      ...react.configs.recommended.rules,
      ...react.configs['jsx-runtime'].rules,
      ...reactHooks.configs.recommended.rules,
      ...jsxA11y.configs.recommended.rules,
      
      // General code quality rules
      'no-console': 'warn',
      'no-debugger': 'error',
      'no-unused-vars': 'off', // Handled by TypeScript
      'prefer-const': 'error',
      'no-var': 'error',
      'no-useless-escape': 'error',
      'no-case-declarations': 'error',
      
      // React specific rules
      'react/prop-types': 'off', // Using TypeScript for prop validation
      'react/react-in-jsx-scope': 'off', // Not needed with new JSX transform
      'react/jsx-uses-react': 'off', // Not needed with new JSX transform
      'react/jsx-no-target-blank': 'error',
      'react/jsx-key': 'error',
      'react/no-array-index-key': 'warn',
      'react/no-unescaped-entities': 'error',
      
      // Accessibility rules (relaxed for development)
      'jsx-a11y/alt-text': 'warn',
      'jsx-a11y/anchor-has-content': 'warn',
      'jsx-a11y/anchor-is-valid': 'warn',
      'jsx-a11y/click-events-have-key-events': 'off',
      'jsx-a11y/no-static-element-interactions': 'off',
    },
  },

  // TypeScript specific rules
  {
    files: ['**/*.{ts,tsx}'],
    rules: {
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
        },
      ],
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-non-null-assertion': 'warn',
      '@typescript-eslint/consistent-type-imports': [
        'warn',
        {
          prefer: 'type-imports',
          disallowTypeAnnotations: false,
        },
      ],
    },
  },

  // Astro specific rules
  {
    files: ['**/*.astro'],
    rules: {
      // Astro components can have unused props (they might be used in the template)
      '@typescript-eslint/no-unused-vars': 'off',
      'react/jsx-key': 'off', // Not applicable in Astro files
      '@typescript-eslint/no-explicit-any': 'off', // More lenient for Astro files
    },
  },

  // Configuration files
  {
    files: ['**/*.config.{js,mjs,ts}', '**/astro.config.mjs', 'next.config.ts'],
    rules: {
      'no-console': 'off',
      '@typescript-eslint/no-var-requires': 'off',
    },
  },

  // Scripts directory - less strict rules
  {
    files: ['scripts/**/*.{js,ts}'],
    rules: {
      'no-console': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
    },
  },

  // Services and utilities - allow console for debugging
  {
    files: ['src/services/**/*.{ts,tsx}', 'src/utils/**/*.{ts,tsx}'],
    rules: {
      'no-console': 'off', // Allow console in services for debugging
    },
  },
];
