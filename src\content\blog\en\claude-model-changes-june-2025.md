---
title: "Model Updates at iteratec from June 12, 2025: Claude 4 and OpenAI o4 Generation"
excerpt: "Starting June 12, 2025, iteratec is implementing comprehensive model updates: Claude 3.5 models will be replaced by Claude 4 Sonnet/Opus, OpenAI o1/o3-mini will be superseded by the new o3/o4-mini generation. All new models are available in iteraGPT and via API."
category: "release-notes"
tags: ["claude", "openai", "model-updates", "iteragpt", "api", "non-eu-models", "claude-4", "o4", "o4-mini"]
publishDate: "2025-06-11T15:30:00Z"
lastUpdated: "2025-06-11T15:30:00Z"
author:
  name: "iteratec AI Team"
  role: "Platform Engineering"
readingTime: 4
featured: true
relatedModelIds: ["claude-sonnet-4", "claude-opus-4", "o4-mini-2025-04-16", "o3-2025-04-16"]
releaseVersion: "2025.06.12"

# i18n-specific fields
lang: "en"
translationKey: "claude-model-changes-june-2025"
availableLanguages: ["de", "en", "pl"]
changelog:
  - type: "removed"
    description: "All Claude 3.5 models will be removed from the iteratec portfolio"
    impact: "major"
    technicalDetails: "Affects Claude 3.7 Sonnet, Claude 3.7 Sonnet v2 and Claude 3.5 Haiku - both in iteraGPT and via API"
  - type: "removed"
    description: "OpenAI o1 and o3-mini models will be replaced by newer generations"
    impact: "major"
    technicalDetails: "o1 will be completely removed, o3-mini will be replaced by o4-mini - better performance at same costs"
  - type: "added"
    description: "Claude 4 Sonnet available as EU model"
    impact: "major"
    technicalDetails: "Provided as open model without EU data protection restrictions, available in iteraGPT and via API"
  - type: "added"
    description: "Claude 4 Opus available as Non-EU model (USA only directly via Anthropic)"
    impact: "major"
    technicalDetails: "Provided as open model without EU data protection restrictions, available in iteraGPT and via API"
  - type: "added"
    description: "OpenAI o4-mini available as successor to o3-mini"
    impact: "major"
    technicalDetails: "Improved reasoning performance at same costs, enhanced vision capabilities and web browsing support"
  - type: "added"
    description: "OpenAI o3 available as new premium reasoning model"
    impact: "major"
    technicalDetails: "Replaces o1 with significantly improved performance in mathematics, coding and scientific reasoning"
metaDescription: "Starting June 12, 2025, iteratec implements comprehensive model updates: Claude 4 Sonnet/Opus replace Claude 3.5, OpenAI o4/o4-mini supersede o1/o3-mini. Available in iteraGPT and via API."
metaKeywords: ["Claude 4", "OpenAI o4", "iteratec", "Non-EU Models", "AI Platform", "Enterprise AI", "Reasoning Models"]
featuredImage: "/images/blog/2025-06-anthropic-claude.png"
---

On **June 12, 2025**, iteratec is implementing comprehensive changes to the AI model portfolio. This strategic realignment affects both Anthropic Claude and OpenAI models and brings significantly more powerful reasoning capabilities as well as extended availability options for our enterprise customers.

## Key Changes Overview

### ❌ Removal of Legacy Models

**Claude 3.5 models** will be completely removed from the iteratec portfolio:
**OpenAI models** of the previous generation will be replaced (**o1** will be completely removed, **o3-mini** will be replaced by **o4-mini**)

These changes affect both the iteraGPT user interface and direct API access.

### ✅ Next Generation New Models

| Model | Availability | Performance | Successor of |
|-------|--------------|-------------|--------------|
| **Claude 4 Sonnet** | Internal: GCP (EU-Hosting)<br/>Open: Anthropic (US-Hosting) | Significantly improved reasoning capabilities<br/>72.7% SWE-bench<br/>76.3% AIME 2024 | Claude 3.5/3.7 Sonnet |
| **Claude 4 Opus** | Open: Anthropic (US-Hosting) | Very good performance for complex tasks<br/>Premium performance in all areas | Claude 3.5 Opus |
| **o4-mini** | Internal: Azure (EU-Hosting)<br/>Open: OpenAI (US-Hosting) | 93.4% AIME 2024<br/>68.1% SWE-bench<br/>Vision support, web browsing<br/>$1.10/$4.40 per 1M tokens | o3-mini |
| **o3** | Internal: Azure (EU-Hosting)<br/>Open: OpenAI (US-Hosting) | 91.6% AIME 2024<br/>69.1% SWE-bench<br/>Very strong reasoning with 2M reasoning tokens<br/>Also strong in mathematics, scientific reasoning | o1 (complete replacement) |

**Important Improvements:**
- **Claude 4**: 65% fewer shortcuts in task solving, persistent memory, MCP integration
- **o4-mini**: New vision capabilities (79.7% MMMU, 84.3% MathVista), same costs as predecessor

## Claude 4 Specifics Compared to Claude 3.7 Sonnet

**Claude 4** (especially Sonnet 4 and Opus 4) brings numerous improvements and new features compared to **Claude 3.7 Sonnet** that affect both performance and practical applicability.

### Improved Thinking and Problem-Solving Capabilities

- **More careful and step-by-step thinking:** Claude 4 models avoid shortcuts or tricks in task solving significantly better – the probability for this is 65% lower than with Sonnet 3.7[1][4][7].
- **More complex tasks:** Sonnet 4 can handle significantly more complex instructions and tasks, especially in coding and reasoning[4][5][7].
- **Extended thinking steps:** Both models (Sonnet 4 and Opus 4) are able to pause and consider multiple steps before responding – particularly valuable for multi-step processes or structured content[1][5].

### Performance Improvement in Coding

- **Benchmark results:** Sonnet 4 achieves 72.7% accuracy in SWE-bench, while Sonnet 3.7 was at 62.3%[2][7]. This is a significant jump and is particularly noticeable in software development, debugging and automation.
- **Better code navigation and problem solving:** According to partners like GitHub and Sourcegraph, Sonnet 4 shows significantly improved ability for code navigation and execution of complex instructions[7].

### New and Improved Features

- **Persistent memory:** Claude 4 (especially Opus 4) can create persistent memory files when developers allow access to local files. This way the model can store and reuse important details across multiple sessions[1][4][5].
- **Tool integration:** Claude 4 can interact with APIs and file systems via the Model Context Protocol (MCP) and use external tools as part of a workflow[1][7].
- **Files API and prompt caching:** New API functions enable uploading and referencing documents across multiple sessions as well as significantly longer prompt caching (up to one hour)[7].
- **Code execution tool:** Python code can be executed in a sandboxed environment, including data analysis and visualization in one step[7].

### Improved Context Processing and Memory

- **Better context understanding:** Sonnet 4 builds on the solid language processing of Sonnet 3.7 but offers improved contextual memory and can better track longer, complex tasks[8].
- **Thinking summaries:** For particularly long thinking processes, summaries are created to condense the chain of thought steps – but this is only necessary in about 5% of cases[7].

### Security and Reliability

- **Reduced error susceptibility:** The models are less susceptible to classic errors like overlooking details in puzzles or incorrectly counting characters, which previously had to be compensated by explicit system prompts[3].
- **Stricter security standards:** With the introduction of Claude 4 models, stricter security standards were also activated[7].

### Summary of Key Differences

| Feature                      | Claude 3.7 Sonnet           | Claude 4 (Sonnet/Opus)           |
|------------------------------|-----------------------------|---------------------------------|
| Care in tasks                | Good                        | Significantly improved, 65% fewer shortcuts[1][4][7] |
| Coding performance (SWE-bench)| 62.3%                      | 72.7%[2][7]                     |
| Context memory               | Solid                       | Extended, persistent memory[1][4][5][8] |
| Tool integration             | Limited                     | MCP, Files API, code execution[1][7] |
| Prompt caching               | 5 minutes                   | Up to 1 hour[7]                 |
| Application scope            | General purpose, coding     | Complex, multi-step tasks, agent workflows, coding[6][7] |

**Conclusion:**  
Claude 4 (Sonnet 4 and Opus 4) is a noticeable upgrade over Claude 3.7 Sonnet in almost all areas: It thinks more carefully, solves more complex tasks, is significantly stronger in coding and brings new features for developers and agent workflows[1][4][5][6][7].

[1] https://www.ultralytics.com/de/blog/anthropics-claude-4-features-whats-new-and-improved
[2] https://www.datacamp.com/de/blog/claude-3-7-sonnet
[3] https://simonwillison.net/2025/May/25/claude-4-system-prompt/
[4] https://www.computerbase.de/news/apps/anthropic-claude-sonnet-4-und-claude-opus-4-freigegeben.92829/
[5] https://t3n.de/news/claude-4-neue-sonnet-opus-modell-anthropic-1689276/
[6] https://www.cometapi.com/de/claude-opus-4-vs-claude-sonnet-4-comparison/
[7] https://the-decoder.de/anthropic-stellt-claude-4-modelle-vor-und-aktiviert-strenge-sicherheitsstandards/
[8] https://www.edenai.co/post/claude-sonnet-3-7-vs-claude-sonnet-4
[9] https://kinews24.de/claude-3-7-sonnet/
[10] https://www.datacamp.com/de/blog/claude-4

## Detailed Performance Improvements at Benchmark Level

The new models offer significant improvements over their predecessors:

### Claude 4 Benchmark Comparison
- **SWE-bench Verified**: Claude 4 Sonnet achieves 72.7% (vs. 62.3% for Claude 3.7 Sonnet)
- **AIME 2024 (Mathematics)**: Claude 4 Sonnet achieves 76.3% (vs. 54.8% for Claude 3.7 Sonnet)
- **LiveCodeBench v2025**: Claude 4 Sonnet achieves 70.9% (vs. 63.8% for Claude 3.7 Sonnet)
- **Terminal-bench**: Claude 4 Sonnet achieves 35.5% (vs. 35.2% for Claude 3.7 Sonnet)
- **MMLU (Multilingual)**: Claude 4 Sonnet achieves 86.5% (vs. 85.9% for Claude 3.7 Sonnet)

### OpenAI o4 Generation Benchmark Comparison
- **AIME 2024**: o4-mini achieves 93.4% (vs. 86.5% for o3-mini), o3 achieves 91.6% (vs. 74.3% for o1)
- **SWE-bench Verified**: o4-mini achieves 68.1% (vs. 48.0% for o3-mini), o3 achieves 69.1% (vs. 48.9% for o1)
- **GPQA Diamond**: o4-mini achieves 81.4% (vs. 75.0% for o3-mini), o3 achieves 83.3% (vs. 78.0% for o1)
- **LiveCodeBench v2025**: o4-mini achieves 68.3% (vs. 69.5% for o3-mini), o3 achieves 75.9%
- **Vision capabilities**: o4-mini new with MMMU 79.7%, MathVista 84.3% (o3-mini had no vision)

## Comparison: OpenAI o1 vs. o3

OpenAI o3 is the direct evolution of o1 and brings significant improvements in almost all relevant areas. The key differences and features can be summarized as follows:

**Performance and Benchmarks**

- **Visual and logical thinking:**  
  o3 surpasses o1 in demanding benchmarks such as MMMU College-level Visual Problem-Solving (82.9% vs. 77.6%), MathVista Visual Math Reasoning (86.8% vs. 71.8%) and CharXiv-Reasoning Scientific Figure Reasoning (78.6% vs. o1)[1][2].

- **Mathematical and scientific tasks:**  
  o3 achieves 91.6% accuracy in AIME 2024 (o1: 74.3%) and 83.3% in GPQA Diamond (PhD-level science questions) (o1: 78%)[1][2][4].

- **Coding and software development:**  
  o3 reaches 69.1% accuracy in SWE-Bench Verified Software Engineering Benchmark (o1: 48.9%) and an ELO value of 2706 in Competitive Programming (o1: 1891)[1][2]. o3 is also significantly ahead in code editing benchmarks[5].

**Practical Differences and Features**

| Feature                      | OpenAI o1                  | OpenAI o3                      |
|------------------------------|----------------------------|--------------------------------|
| Speed                        | Solid, but slower          | Significantly faster (e.g. o3-mini: 24% faster than o1-mini)[6][4] |
| Accuracy & Reliability       | Good for simple tasks      | Higher accuracy, even for complex problems (e.g. 88% in ARC-AGI Benchmark vs. 32% for o1)[4] |
| Depth of understanding       | Superficial checks         | Recognizes even subtle errors, e.g. in code reviews[5] |
| Adaptability                 | Limited                    | Versatile, suitable for complex and various tasks[4] |
| Generative capabilities      | Limited                    | High-quality content, complex problem solving[4] |
| Security                     | High standard              | New security mechanisms, deliberative alignment[6] |
| Tool compatibility           | Limited                    | Broad support for tools and platforms[4] |

**Summary:**
- **o3** is significantly superior to its predecessor **o1** in all benchmarks, especially in logical thinking, coding and mathematical-scientific tasks[1][2][4][5].
- **o3** also recognizes more subtle errors and problems in practice, such as in code reviews or complex tasks, while o1 tends to be limited to superficial checks[5].
- **o3** is faster, more versatile and offers more advanced security and adaptation mechanisms[4][6].

**Conclusion:**  
OpenAI o3 is the clear choice for advanced, complex and professional applications, while o1 remains a solid option for simpler, cost-effective tasks.

[1] https://www.datacamp.com/blog/o3-openai
[2] https://www.datacamp.com/de/blog/o3-openai
[3] https://zapier.com/blog/openai-o1/
[4] https://www.hixx.ai/de/blog/awesome-ai-tools/open-ai-03
[5] https://www.codeant.ai/blogs/o1-vs-o3-mini-ai-code-review-comparison
[6] https://openai.com/index/openai-o3-mini/
[7] https://www.byteplus.com/en/topic/516725
[8] https://www.helicone.ai/blog/openai-o3
[9] https://community.openai.com/t/o1-model-is-better-than-o3/1240304
[10] https://www.reddit.com/r/OpenAI/comments/1iemnvi/openai_o3mini/?tl=de