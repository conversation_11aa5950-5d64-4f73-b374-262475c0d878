# i18n Implementation Guide - LLM Browser Project

## Overview

This project uses a **unified client-side only i18n architecture** optimized for GitLab Pages static deployment:

1. **Static Fallback Texts**: Astro pages use German fallback texts with `data-translate` attributes
2. **Client-side Translation Loading**: All translation switching happens client-side via JavaScript
3. **React Islands**: Use TranslationProvider with global translation fallback pattern
4. **No Server-side Translation Loading**: Eliminates `fs.readFileSync()` for GitLab Pages compatibility

This architecture ensures proper language switching on static hosting while maintaining SEO-friendly fallback content.

## Supported Languages

- **German (de)**: Default language
- **English (en)**: Full translation support
- **Polish (pl)**: Full translation support

## File Structure

```
public/locales/
├── de/i18n.json          # German translations (default)
├── en/i18n.json          # English translations
└── pl/i18n.json          # Polish translations

src/contexts/
└── TranslationContext.tsx # React translation context

src/components/
├── LanguageSelectorIsland.tsx    # Language selector wrapper
└── ui/language-selector.tsx      # Language selector component

src/layouts/
└── Layout.astro          # Main layout with server-side i18n setup
```

## Translation File Structure

Each `i18n.json` file follows this structure:

```json
{
  "site": {
    "title": "LLM Browser",
    "description": "Browse and compare LLM models"
  },
  "nav": {
    "models": "Models",
    "blog": "Blog",
    "recommendations": "Recommendations",
    "benchmarks": "All Benchmarks",
    "api_usage": "API Usage@iteratec"
  },
  "footer": {
    "copyright": "© 2025 LLM Browser",
    "built_with": "Built with Astro, React, TypeScript and Tailwind CSS"
  },
  "language_selector": {
    "label": "Language",
    "de": "Deutsch",
    "en": "English", 
    "pl": "Polski"
  },
  "models": {
    "header": "LLM Models",
    "description": "Browse and compare available models",
    "filters": { /* filter options */ },
    "stats": { /* statistics labels */ },
    "comparison": { /* comparison table labels */ },
    "capabilities": { /* capability labels */ }
  },
  "blog": { /* blog-specific translations */ },
  "recommendations": { /* recommendations page translations */ },
  "benchmark": { /* benchmark page translations */ },
  "qc": { /* quality control labels */ },
  "components": {
    "collapsible_header": {
      "show_info": "Show Info",
      "hide_info": "Hide Info"
    }
  }
}
```

## Client-side Only i18n (Astro Pages)

### Setup in Layout.astro

The main layout handles:
- Server-side German translation loading (default)
- Global variable setup for client-side access
- Client-side language detection and switching
- Automatic `data-translate` attribute processing

### Usage in .astro Files

**✅ CORRECT - Client-side Only Pattern:**
```astro
---
// NO server-side translation loading!
// NO fs.readFileSync() calls!
// Client-side only approach for GitLab Pages compatibility
---

<!-- Use data-translate attributes with German fallback texts -->
<h1 data-translate="models.header">LLM Modell-Browser</h1>
<p data-translate="models.description">
  Entdecke KI-Modelle mit detaillierten Informationen zu Preisen, Fähigkeiten und Benchmark-Ergebnissen.
</p>

<!-- Dynamic content handled by React Islands -->
<ModelStatsIsland client:load />
```

**❌ INCORRECT - Server-side Pattern (GitLab Pages incompatible):**
```astro
---
// DON'T DO THIS - Breaks GitLab Pages deployment
import fs from "fs";
import path from "path";

const cookieLang = Astro.cookies?.get("preferredLanguage")?.value;
const translationsPath = path.join(process.cwd(), "public", "locales", currentLang, "i18n.json");
const translations = JSON.parse(fs.readFileSync(translationsPath, "utf-8"));
---

<h1>{translations.models?.header}</h1> <!-- ❌ Server-side translation -->
```

### Required Approach for All Pages

```astro
---
// Clean approach - no translation imports needed
// Layout.astro handles all translation setup
---

<Layout title="Page Title" description="German fallback description">
  <main>
    <!-- All text uses data-translate with German fallbacks -->
    <h1 data-translate="page.title">Deutsche Überschrift</h1>
    <p data-translate="page.description">Deutsche Beschreibung</p>
  </main>
</Layout>
```

## Client-side i18n (React Components)

### Critical Pattern: TranslationProvider with Global Fallback

**⚠️ REQUIRED**: All React Islands must use this pattern to ensure proper language switching:

```tsx
// MyComponentIsland.tsx
import { TranslationProvider, useTranslation, t, type Translations } from "@/contexts/TranslationContext";
import { useState, useEffect } from "react";

// Global window interface
declare global {
  interface Window {
    translations?: any;
    currentLang?: string;
    __TRANSLATIONS__?: any;
    __CURRENT_LANG__?: string;
  }
}

function MyComponentContent(props: MyComponentProps) {
  const { translations } = useTranslation();
  
  // Critical fallback pattern
  const getTranslation = (key: string, replacements?: Record<string, string | number>) => {
    // Try context translations first
    let result = t(translations, key, replacements);
    
    // Fallback to global window translations
    if (result === key && typeof window !== 'undefined' && window.translations) {
      result = t(window.translations, key, replacements);
    }
    
    return result;
  };
  
  return (
    <div>
      <h1>{getTranslation('models.header')}</h1>
      <p>{getTranslation('models.description')}</p>
    </div>
  );
}

// Main export with TranslationProvider wrapper
export default function MyComponentIsland(props: MyComponentProps) {
  const [initialTranslations, setInitialTranslations] = useState<Translations>({});
  const [initialLang, setInitialLang] = useState("de");

  useEffect(() => {
    // Get initial translations from global variables
    if (typeof window !== "undefined") {
      const globalTranslations = (window as any).__TRANSLATIONS__ || (window as any).translations || {};
      const globalLang = (window as any).__CURRENT_LANG__ || (window as any).currentLang || "de";
      
      setInitialTranslations(globalTranslations);
      setInitialLang(globalLang);
    }
  }, []);

  return (
    <TranslationProvider
      initialTranslations={initialTranslations}
      initialLang={initialLang}
    >
      <MyComponentContent {...props} />
    </TranslationProvider>
  );
}
```

Usage in Astro:
```astro
<MyComponentIsland client:load />
```

## Language Detection Priority

1. **Client-side Cookie**: `preferredLanguage` cookie value (localStorage backup)
2. **Default**: German (`de`) - server-side fallback
3. **No Browser Language Detection**: Removed for static deployment compatibility

## Language Switching Mechanism

- **Client-side Only**: No page reload required
- Language changes update global variables and trigger React component re-renders
- New language stored in `preferredLanguage` cookie and localStorage
- Automatic `data-translate` attribute updates via client-side script
- React Islands receive updated translations via global variables

## Translation Key Patterns

### Nested Keys
```json
{
  "models": {
    "comparison": {
      "title": "Model Comparison"
    }
  }
}
```

Access: `t('models.comparison.title')` or `translations.models?.comparison?.title`

### Dynamic Content
```json
{
  "recommendations": {
    "model_count": "We have {count} models available"
  }
}
```

Usage: `t('recommendations.model_count', { count: 42 })` or `translations.recommendations?.model_count?.replace("{count}", "42")`

## Best Practices

### For Astro Pages
1. **NO server-side translation loading** - use `data-translate` attributes only
2. **German fallback texts**: Always provide German text as fallback content
3. **Static-friendly**: No `fs.readFileSync()`, `Astro.cookies`, or server-side language detection
4. **SEO-optimized**: Fallback texts ensure content is indexable

### For React Components
1. **Always use TranslationProvider wrapper pattern** for Islands
2. **Implement global fallback logic** in `getTranslation()` helper
3. **Load initial translations from global variables** in useEffect
4. **Handle translation loading race conditions** with fallback pattern

### Translation Files
1. Keep all three language files in sync (`de`, `en`, `pl`)
2. Use consistent key naming (snake_case with dots for nesting)
3. Group related translations in logical sections
4. Provide meaningful German fallback text for all keys

### GitLab Pages Compatibility
1. **Client-side only**: No server-side translation loading
2. **Static assets**: Translation files in `public/locales/` (copied to build output)
3. **No dynamic imports**: All translations loaded via fetch on client-side
4. **Fallback content**: German text ensures content displays before JS loads

## Common Patterns

### Page Headers (Client-side Only)
```astro
<!-- ✅ CORRECT: data-translate with German fallback -->
<h1 data-translate="models.header">LLM Modell-Browser</h1>
<p class="text-muted-foreground" data-translate="models.description">
  Entdecke KI-Modelle mit detaillierten Informationen zu Preisen, Fähigkeiten und Benchmark-Ergebnissen.
</p>

<!-- ❌ INCORRECT: Server-side translation -->
<h1>{translations.models?.header || "Models"}</h1>
```

### Navigation Links (Handled by Layout.astro)
```astro
<!-- Layout.astro automatically handles navigation translation -->
<!-- No additional code needed in individual pages -->
```

### Dynamic Content (React Islands Only)
```astro
<!-- ✅ CORRECT: Use React Island for dynamic content -->
<ModelStatsIsland client:load models={models} />

<!-- ❌ INCORRECT: Server-side dynamic content -->
{items.map(item => (
  <div>
    <p>{translations.models?.stats?.total || "Total"}: {item.count}</p>
  </div>
))}
```

### React Component Translation (with Fallback)
```tsx
function ModelCard({ model }) {
  const { translations } = useTranslation();
  
  // Critical fallback pattern
  const getTranslation = (key: string) => {
    let result = t(translations, key);
    if (result === key && typeof window !== 'undefined' && window.translations) {
      result = t(window.translations, key);
    }
    return result;
  };
  
  return (
    <div>
      <h3>{model.name}</h3>
      <p>{getTranslation('models.stats.context_window')}: {model.contextWindow}</p>
      <button>{getTranslation('models.comparison.reset_selection')}</button>
    </div>
  );
}
```
### Critical Fallback Pattern for React Components

**⚠️ IMPORTANT**: React components must use a fallback pattern to handle translation loading race conditions.

#### The Problem
React components using direct `t(translations, 'key')` calls may show translation keys instead of values because:
- TranslationContext might be empty during initial render
- Global window translations are always available as fallback

#### The Solution
Always implement this fallback pattern in React components:

```tsx
// Add global window interface declarations
declare global {
  interface Window {
    translations: any;
    currentLang: string;
  }
}

function MyComponent() {
  const { translations } = useTranslation();
  
  // Helper function with fallback logic
  const getTranslation = (key: string, params?: Record<string, any>) => {
    // Try context translations first
    let result = t(translations, key);
    
    // Fallback to global window translations
    if (result === key && typeof window !== 'undefined' && window.translations) {
      result = t(window.translations, key);
    }
    
    // Handle parameter replacement
    if (params && result !== key) {
      Object.entries(params).forEach(([param, value]) => {
        result = result.replace(`{${param}}`, String(value));
      });
    }
    
    return result;
  };
  
  return (
    <div>
      <h1>{getTranslation('models.header')}</h1>
      <p>{getTranslation('models.filtered_count', { count: 42 })}</p>
    </div>
  );
}
```

### Translation Keys Showing Instead of Values

**Symptom**: React components display translation keys (e.g., "models.header") instead of translated text.

**Root Cause**: TranslationContext is empty during initial render due to translation loading race condition.

**Solution**: Implement the Critical Fallback Pattern (see above section).

**Quick Fix**:
1. Add global window interface declarations
2. Create `getTranslation` helper function with fallback logic
3. Replace all `t(translations, 'key')` calls with `getTranslation('key')`

**Example Fix**:
```tsx
// Before (shows translation keys)
const text = t(translations, 'models.header');

// After (shows translated text)
const getTranslation = (key: string) => {
  let result = t(translations, key);
  if (result === key && typeof window !== 'undefined' && window.translations) {
    result = t(window.translations, key);
  }
  return result;
};
const text = getTranslation('models.header');
```
#### Why This Works
1. **Context First**: Tries TranslationContext translations (preferred)
2. **Global Fallback**: Falls back to window.translations if context is empty
3. **Key Fallback**: Returns the key itself if no translation found
4. **Parameter Support**: Handles dynamic content replacement

#### When to Use
- **Always** in React components that display translated text
- **Especially** in island components that hydrate on the client
- **Required** for components showing translation keys instead of values

## Troubleshooting

### Common Issues

1. **Missing translations**: Check console for "Translation key not found" warnings
2. **404 errors**: Ensure translation files exist in `public/locales/{lang}/i18n.json`
3. **React hydration errors**: Wrap island components with TranslationProvider
4. **Language not switching**: Check cookie storage and page reload mechanism
5. **Language switching only works on some pages**: Check prerendering configuration (see Critical Issue below)

### GitLab Pages Static Deployment

**✅ SOLUTION**: Client-side only i18n works perfectly with static deployment.

#### The Architecture
- **Static HTML Generation**: All pages use `prerender: true` (default)
- **German Fallback Content**: SEO-friendly content visible before JS loads
- **Client-side Language Switching**: JavaScript handles all translation switching
- **No Server Dependencies**: No cookies, no server-side language detection

#### Implementation Requirements
**ALL pages must follow this pattern:**

```astro
---
// ✅ CORRECT: No server-side translation code
// Clean Astro page with static content
---

<Layout title="Page Title" description="German description">
  <main>
    <!-- German fallback with data-translate attributes -->
    <h1 data-translate="page.title">Deutsche Überschrift</h1>
    <p data-translate="page.description">Deutsche Beschreibung</p>
    
    <!-- React Islands for dynamic content -->
    <MyComponentIsland client:load />
  </main>
</Layout>
```

#### Pages Successfully Migrated
- `src/pages/index.astro` ✅
- `src/pages/models/index.astro` ✅
- `src/pages/blog/index.astro` ✅
- All other pages following this pattern ✅

#### Migration Checklist
- [ ] Remove all `fs.readFileSync()` calls
- [ ] Remove all `Astro.cookies` usage
- [ ] Replace server-side translations with `data-translate` attributes
- [ ] Use German fallback texts
- [ ] Wrap React components with TranslationProvider pattern
- [ ] Implement global fallback logic in React components

### Debug Information

The system logs extensive debug information:
- Server-side language detection
- Translation file loading
- Client-side translation initialization
- Translation key access attempts

### Common Migration Issues

#### Translation Keys Showing Instead of Values
**Symptom**: React components display "models.header" instead of translated text.
**Solution**: Implement the TranslationProvider wrapper pattern with global fallback.

#### Language Not Switching
**Symptom**: Language selector doesn't change page content.
**Solution**: Ensure React Islands use the correct TranslationProvider pattern.

#### Missing Translations on Page Load
**Symptom**: Content shows briefly in wrong language before switching.
**Solution**: Use German fallback texts in `data-translate` elements.

#### Build Errors with fs/path Imports
**Symptom**: "Cannot resolve module" errors during build.
**Solution**: Remove all `fs`, `path`, and server-side translation imports.

```astro
---
// ❌ REMOVE: Server-side imports
import fs from "fs";
import path from "path";

// ✅ CORRECT: Clean Astro page
// No imports needed for client-side i18n
---
```

## Adding New Languages

1. Create new translation file: `public/locales/{lang}/i18n.json`
2. Copy structure from existing file (e.g., `en/i18n.json`)
3. Translate all values
4. Add language to supported list in Layout.astro language detection
5. Add language option to LanguageSelector component

## Adding New Translation Keys

1. Add key to all three language files (`de`, `en`, `pl`)
2. Use consistent key naming and nesting
3. Test in both Astro pages and React components
4. Provide meaningful fallback values