import type { BenchmarkData, ModelData } from "@/types/api";

// Hilfsfunktion zur Normalisierung von Modellnamen für besseren Vergleich
export function normalizeModelIdentifier(identifier: string): string {
  return identifier
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')           // Leerzeichen zu Bindestrichen
    .replace(/[^\w-]/g, '')         // Nur Buchstaben, Zahlen und Bindestriche
    .replace(/-+/g, '-')            // Mehrfache Bindestriche zu einem
    .replace(/^-|-$/g, '');         // Führende/nachgestellte Bindestriche entfernen
}

// Hilfsfunktion zur Prüfung, ob ein Benchmark-Eintrag bereits existiert
function findExistingBenchmark(benchmarks: BenchmarkData[], newItem: BenchmarkData): BenchmarkData | null {
  const newModelId = normalizeModelIdentifier(newItem.modelid);
  const newModelName = normalizeModelIdentifier(newItem.model);
  
  return benchmarks.find(existing => {
    const existingModelId = normalizeModelIdentifier(existing.modelid);
    const existingModelName = normalizeModelIdentifier(existing.model);
    
    // Prüfe sowohl modelid als auch model name
    const modelIdMatch = existingModelId === newModelId;
    const modelNameMatch = existingModelName === newModelName;
    
    return modelIdMatch || modelNameMatch;
  }) || null;
}

// Hilfsfunktion um zu entscheiden, ob ein existierender Benchmark-Eintrag ersetzt werden soll
function shouldReplaceExistingBenchmark(existing: BenchmarkData, newItem: BenchmarkData): boolean {
  // Priorität 1: Vergleiche Datum (neueres Datum gewinnt)
  if (existing.details?.date && newItem.details?.date) {
    const existingDate = new Date(existing.details.date);
    const newDate = new Date(newItem.details.date);
    
    if (newDate > existingDate) {
      return true; // Neueres Datum, ersetze
    } else if (newDate < existingDate) {
      return false; // Älteres Datum, behalte existing
    }
    // Gleiches Datum, prüfe weitere Kriterien
  }
  
  // Priorität 2: Vergleiche Anzahl Testfälle (mehr Testfälle = besser)
  const existingTestCases = existing.details?.test_cases || 0;
  const newTestCases = newItem.details?.test_cases || 0;
  
  if (newTestCases > existingTestCases) {
    return true; // Mehr Testfälle, ersetze
  } else if (newTestCases < existingTestCases) {
    return false; // Weniger Testfälle, behalte existing
  }
  
  // Priorität 3: Vergleiche Pass-Rate (höhere Pass-Rate = besser)
  const existingPassRate = existing.pass_rate_2 || 0;
  const newPassRate = newItem.pass_rate_2 || 0;
  
  if (newPassRate > existingPassRate) {
    return true; // Höhere Pass-Rate, ersetze
  } else if (newPassRate < existingPassRate) {
    return false; // Niedrigere Pass-Rate, behalte existing
  }
  
  // Priorität 4: Vergleiche Vollständigkeit der Daten (mehr Details = besser)
  const existingDetailsCount = Object.keys(existing.details || {}).length;
  const newDetailsCount = Object.keys(newItem.details || {}).length;
  
  if (newDetailsCount > existingDetailsCount) {
    return true; // Mehr Details, ersetze
  }
  
  // Standard: Behalte existing (erstes gefundenes)
  return false;
}

// Hilfsfunktion zur Deduplizierung von Benchmark-Daten basierend auf modelid UND model name
export function deduplicateBenchmarkData(data: BenchmarkData[]): BenchmarkData[] {
  const uniqueBenchmarks: BenchmarkData[] = [];
  
  data.forEach(item => {
    const existing = findExistingBenchmark(uniqueBenchmarks, item);
    
    if (!existing) {
      // Kein Duplikat gefunden, füge hinzu
      uniqueBenchmarks.push(item);
      console.log(`[BUILD] Added new benchmark: "${item.model}" (modelid: ${item.modelid})`);
    } else {
      // Duplikat gefunden - vergleiche Daten um zu entscheiden, welches behalten werden soll
      const shouldReplace = shouldReplaceExistingBenchmark(existing, item);
      
      if (shouldReplace) {
        // Ersetze das existierende Element
        const index = uniqueBenchmarks.indexOf(existing);
        uniqueBenchmarks[index] = item;
        console.log(`[BUILD] Replacing duplicate benchmark: "${existing.model}" (${existing.modelid}) -> "${item.model}" (${item.modelid})`);
      } else {
        console.log(`[BUILD] Keeping existing benchmark: "${existing.model}" (${existing.modelid}), skipping "${item.model}" (${item.modelid})`);
      }
    }
  });
  
  console.log(`[BUILD] After deduplication: ${uniqueBenchmarks.length} unique benchmark entries (removed ${data.length - uniqueBenchmarks.length} duplicates)`);
  
  return uniqueBenchmarks;
}

// Hilfsfunktion zur Deduplizierung von Model-Daten basierend auf ID und Name
export function deduplicateModelData(data: ModelData[]): ModelData[] {
  const uniqueModels: ModelData[] = [];
  const seenIds = new Set<string>();
  const seenNames = new Set<string>();
  
  data.forEach(model => {
    const normalizedId = normalizeModelIdentifier(model.id);
    const normalizedName = normalizeModelIdentifier(model.name);
    
    // Check if we've already seen this model ID or name
    if (!seenIds.has(normalizedId) && !seenNames.has(normalizedName)) {
      uniqueModels.push(model);
      seenIds.add(normalizedId);
      seenNames.add(normalizedName);
      console.log(`[BUILD] Added unique model: "${model.name}" (id: ${model.id})`);
    } else {
      console.log(`[BUILD] Skipping duplicate model: "${model.name}" (id: ${model.id})`);
    }
  });
  
  console.log(`[BUILD] After model deduplication: ${uniqueModels.length} unique models (removed ${data.length - uniqueModels.length} duplicates)`);
  
  return uniqueModels;
}