# Model Overview App - Projektplan

## Projektziele und Umfang

Die Model Overview App soll eine umfassende und benutzerfreundliche Plattform bieten, die es Nutzern ermöglicht, alle relevanten KI-Modelle zu finden, zu vergleichen und deren Details einzusehen. Die Anwendung integriert Daten aus mehreren Quellen und bietet robuste Caching-Mechanismen für eine zuverlässige Nutzung auch bei Nichtverfügbarkeit externer Datenquellen.

### Hauptziele

- [x] Entwicklung einer zentralen Plattform zur Anzeige aller verfügbaren KI-Modelle
- [x] Integration von Daten aus internen API-Endpunkten (`/model/info`) und externen Quellen (LiteLLM GitHub)
- [x] Implementierung eines robusten Caching-Mechanismus für externe Daten
- [x] Bereitstellung von Such-, Filter- und Vergleichsfunktionen für Modelle
- [x] Visualisierung von Modelldetails und Kostenstrukturen
- [x] Benutzerfreundliche Fehlerbehandlung bei API-Ausfällen
- [ ] Integration von Polyglot-Benchmark-Daten für erweiterte Modellvergleiche und Performance-Metriken

## Systemarchitektur

Die Anwendung basiert auf einer modernen Frontend-Architektur mit Next.js und ShadCN UI-Komponenten. Die Daten werden aus verschiedenen Quellen bezogen und im Frontend verarbeitet und angezeigt.

```
+----------------------------------+
|           Frontend               |
|  +----------------------------+  |
|  |        Next.js App         |  |
|  |                            |  |
|  |  +----------------------+  |  |
|  |  |  ModelDataContext    |  |  |
|  |  +----------------------+  |  |
|  |                            |  |
|  |  +----------------------+  |  |
|  |  |  UI-Komponenten      |  |  |
|  |  |  - ModelTable        |  |  |
|  |  |  - ModelComparison   |  |  |
|  |  |  - FilterOptions     |  |  |
|  |  +----------------------+  |  |
|  +----------------------------+  |
+----------------------------------+
           |           |           |
           v           v           v
+------------------+  +------------------+  +------------------+
| Interne API      |  | Externe API      |  | Lokale Datei     |
| /model/info      |  | LiteLLM GitHub   |  | Polyglot-Bench.  |
+------------------+  +------------------+  +------------------+
                        |
                        v
                 +------------------+
                 | Lokaler Cache    |
                 | (localStorage)   |
                 +------------------+
```

## Datenflussdiagramm

```
+------------------+     +------------------+     +------------------+
| Interne API      |     | Externe API      |     | Lokale Datei     |
| /model/info      |     | LiteLLM GitHub   |     | Polyglot-Bench.  |
+------------------+     +------------------+     +------------------+
        |                       |                        |
        v                       v                        v
+------------------------------------------------------------------+
|                          API Service                             |
|  +----------------+  +----------------+  +--------------------+  |
|  | fetchModelInfo |  | fetchModelCost |  | fetchBenchmarkData |  |
|  +----------------+  +----------------+  +--------------------+  |
|             |              |                      |              |
|             v              v                      v              |
|                 +-------------------------+                      |
|                 | fetchCombinedModelData  |                      |
|                 +-------------------------+                      |
+------------------------------------------------------------------+
                    |
                    v
+------------------------------------------+
|           ModelDataContext               |
|  +----------------+  +----------------+  |
|  | models         |  | filteredModels |  |
|  +----------------+  +----------------+  |
|  +----------------+  +----------------+  |
|  | selectedModels |  | searchTerm     |  |
|  +----------------+  +----------------+  |
+------------------------------------------+
                    |
                    v
+------------------------------------------+
|              UI-Komponenten              |
|  +----------------+  +----------------+  |
|  | ModelTable     |  | ModelComparison|  |
|  +----------------+  +----------------+  |
|  +----------------+  +----------------+  |
|  | FilterOptions  |  | ExportOptions  |  |
|  +----------------+  +----------------+  |
+------------------------------------------+
```

## API-Integrationsstrategie

### Interne API (`/model/info`)

1. **Authentifizierung**:
   - Bearer-Token-Authentifizierung über HTTP-Header
   - API-Schlüssel wird aus Umgebungsvariablen geladen

2. **Datenabruf**:
   - Endpunkt: `https://api.iteragpt.iteratec.de/v2/model/info`
   - HTTP-Methode: GET
   - Fehlerbehandlung für verschiedene HTTP-Statuscodes (401, 403, 429, 500)

3. **Daten-Mapping**:
   - Transformation der API-Antwort in das interne `ModelInfo`-Format
   - Extraktion relevanter Felder wie ID, Name, Provider, Capabilities, etc.
   - Behandlung von verschiedenen API-Antwortformaten (Array, verschachtelte Objekte)

### Externe API (LiteLLM GitHub)

1. **Datenabruf**:
   - URL: `https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json`
   - HTTP-Methode: GET
   - Keine Authentifizierung erforderlich (öffentliche Daten)

2. **Daten-Mapping**:
   - Transformation der JSON-Daten in das interne `ModelCost`-Format
   - Extraktion von Kosteninformationen und Kontextfenstergrößen
   - Behandlung verschiedener Datenformate (Objekt mit Modellnamen als Schlüssel oder Array von Modelldaten)

3. **Fehlerbehandlung**:
   - Robuste Fehlerbehandlung für Netzwerkprobleme
   - Fallback auf lokalen Cache bei Nichterreichbarkeit
   - Logging von Fehlern für Debugging-Zwecke

### Externe Daten (Polyglot-Benchmark)

1. **Datenabruf**:
   - Quelle: Lokale Datei `static/polyglot-benchmark.json`
   - Einbindung als statische Ressource in die Next.js-Anwendung
   - Keine Authentifizierung erforderlich (lokale Daten)

2. **Daten-Mapping**:
   - Transformation der JSON-Daten in das interne `ModelBenchmark`-Format
   - Extraktion von Performance-Metriken wie `pass_rate_2`, `percent_cases_well_formed`, `total_cost`
   - Zugriff auf detaillierte Testinformationen über das `details`-Objekt

3. **Aktualisierungsstrategie**:
   - Regelmäßige manuelle Aktualisierung der Benchmark-Datei
   - Versionierung der Benchmark-Daten für Nachvollziehbarkeit
   - Möglichkeit zur Anzeige des Datums der letzten Aktualisierung

### Datenkorrelation

1. **Matching-Strategie**:
   - Primärer Schlüssel für Matching: `modelID` (einheitlich über alle Datenquellen)
   - Fallback-Matching über Modellnamen, wenn keine modelID verfügbar ist
   - Normalisierung von Modell-IDs und -Namen für besseres Matching (Kleinbuchstaben, Entfernung von Präfixen)
   - Spezielle Mapping-Tabelle für nicht-standardisierte Modellbezeichnungen

2. **Daten-Zusammenführung**:
   - Kombination von Modellinformationen, Kostendaten und Benchmark-Daten in ein einheitliches `ModelData`-Format
   - Priorisierung von Daten bei Konflikten (z.B. Kontextfenstergröße aus LiteLLM, wenn verfügbar)
   - Kennzeichnung der Datenquelle für Transparenz
   - Anreicherung mit Benchmark-Metriken, wenn verfügbar

## Fallback-Cache-Logik

### Caching-Strategie

1. **Speicherort**:
   - Verwendung von `localStorage` für clientseitiges Caching
   - Schlüssel: `iteragpt-model-data-cache`
   - Struktur: `{ timestamp: number, data: ModelCost[] }`

2. **Cache-Aktualisierung**:
   - Automatische Aktualisierung bei erfolgreichem Abruf von LiteLLM-Daten
   - Manuelle Aktualisierung über Refresh-Button in der UI
   - Speicherung von Zeitstempel für Cache-Invalidierung

3. **Cache-Invalidierung**:
   - Ablauf nach 24 Stunden (konfigurierbar)
   - Automatische Löschung abgelaufener Daten
   - Möglichkeit zur manuellen Cache-Löschung

4. **Fallback-Logik**:
   1. Versuch, Daten von LiteLLM-API abzurufen
   2. Bei Fehler: Versuch, Daten aus lokalem Cache zu laden
   3. Bei leerem/abgelaufenem Cache: Verwendung von Fallback-Mockdaten
   4. Benachrichtigung des Benutzers über die verwendete Datenquelle

## UI/UX-Konzept

### Hauptansichten

1. **Übersichtsansicht (Tabelle)**:
   - Tabellarische Darstellung aller Modelle
   - Sortierbare und filterbare Spalten
   - Schnellzugriff auf wichtige Informationen (Name, Anbieter, Kosten)
   - Visuelle Kennzeichnung der Datenquelle (Live/Cache)
   - Pagination für große Datensätze

2. **Detailansicht**:
   - Modal/Dialog mit vollständigen Modellinformationen
   - Gruppierung von Informationen in logische Abschnitte
   - Hervorhebung wichtiger Metriken
   - Anzeige von Capabilities und Unterstützung für verschiedene Funktionen

3. **Vergleichsansicht**:
   - Side-by-Side-Vergleich ausgewählter Modelle
   - Hervorhebung von Unterschieden und Gemeinsamkeiten
   - Visuelle Kennzeichnung der besten Werte (niedrigste Kosten, größtes Kontextfenster)
   - Möglichkeit zum Export der Vergleichsdaten

### Interaktionselemente

1. **Filterung und Suche**:
   - Textsuche über alle relevanten Felder
   - Dropdown-Filter für Modellanbieter und -gruppen
   - Schnellfilter für häufig verwendete Kriterien

2. **Fehlerbehandlung und Feedback**:
   - Toast-Benachrichtigungen für Statusmeldungen
   - Klare Fehlermeldungen bei API-Problemen
   - Ladezustände für asynchrone Operationen
   - Hinweise zur Datenquelle (Live/Cache/Fallback)

## Technologie-Stack

### Frontend

- **Framework**: Next.js (App Router)
- **UI-Komponenten**: ShadCN UI
- **State Management**: React Context API
- **Styling**: Tailwind CSS
- **HTTP-Client**: Native Fetch API
- **Caching**: localStorage für clientseitiges Caching
- **Benachrichtigungen**: Sonner Toast-Komponente

### Entwicklungswerkzeuge

- **Paketmanager**: npm/yarn
- **Typensystem**: TypeScript
- **Linting**: ESLint
- **Formatierung**: Prettier
- **Versionskontrolle**: Git

## Aufgabenplanung mit Checklisten

### 1. Projektsetup und Grundstruktur

- [x] Next.js-Projekt initialisieren
- [x] ShadCN UI-Komponenten einrichten
- [x] Projektstruktur definieren (Verzeichnisse, Komponenten)
- [x] TypeScript-Typen für Datenmodelle definieren
- [x] Grundlegende Layout-Komponenten erstellen

### 2. API-Integration

- [x] API-Service für interne API (`/model/info`) implementieren
  - [x] Authentifizierung einrichten
  - [x] Fehlerbehandlung implementieren
  - [x] Daten-Mapping definieren
- [x] API-Service für externe API (LiteLLM) implementieren
  - [x] Abruf der JSON-Daten
  - [x] Daten-Mapping und -Transformation
  - [x] Fehlerbehandlung
- [x] Datenkorrelation zwischen beiden Quellen implementieren
  - [x] Matching-Logik definieren
  - [x] Zusammenführung der Daten
- [ ] API-Service für Polyglot-Benchmark-Daten implementieren
  - [ ] Einbindung der lokalen JSON-Datei
  - [ ] Daten-Mapping und -Transformation
  - [ ] Integration in die bestehende Datenstruktur

### 3. Caching-Implementierung

- [x] localStorage-basiertes Caching implementieren
  - [x] Speichern von Daten im Cache
  - [x] Laden von Daten aus dem Cache
  - [x] Cache-Invalidierung nach Zeitablauf
- [x] Fallback-Logik für Offline-Nutzung
  - [x] Mockdaten für Notfälle bereitstellen
  - [x] Statusmeldungen für Benutzer

### 4. UI-Entwicklung: Übersichtsansicht

- [x] Tabellen-Komponente für Modellübersicht implementieren
  - [x] Sortierbare Spalten
  - [x] Pagination
  - [x] Zeilenauswahl für Vergleich
- [x] Filter- und Suchfunktionen implementieren
  - [x] Textsuche
  - [x] Dropdown-Filter für Kategorien
  - [x] Filterlogik im Context

### 5. UI-Entwicklung: Detailansicht

- [x] Dialog/Modal für Modelldetails implementieren
  - [x] Strukturierte Anzeige aller Modellinformationen
  - [x] Gruppierung in logische Abschnitte
- [x] Visuelle Darstellung von Metriken
  - [x] Formatierung von Kosten und Zahlen
  - [x] Anzeige von Capabilities
  - [ ] Integration von Benchmark-Metriken
  - [ ] Visualisierung von Performance-Daten

### 6. UI-Entwicklung: Vergleichsansicht

- [x] Vergleichstabelle für ausgewählte Modelle implementieren
  - [x] Side-by-Side-Vergleich
  - [x] Hervorhebung von Unterschieden
- [x] Logik für Modellauswahl und -vergleich
  - [x] Auswahl/Abwahl von Modellen
  - [x] Speicherung der Auswahl im Context
  - [ ] Erweiterter Vergleich mit Benchmark-Daten
  - [ ] Filterung nach Performance-Metriken

### 7. Fehlerbehandlung und Benutzerfeedback

- [x] Toast-Benachrichtigungen implementieren
  - [x] Erfolgs- und Fehlermeldungen
  - [x] Hinweise zur Datenquelle
- [x] Ladezustände und Fallback-UI
  - [x] Skeleton-Loader für Tabellen
  - [x] Fehlermeldungen bei API-Problemen

### 8. Testen und Optimierung

- [ ] Manuelle Tests aller Funktionen
  - [ ] API-Integration
  - [ ] Caching-Mechanismus
  - [ ] UI-Komponenten
  - [ ] Benchmark-Daten-Integration
- [ ] Performance-Optimierung
  - [ ] Memoization für teure Berechnungen
  - [ ] Lazy Loading für große Datensätze
- [ ] Responsive Design überprüfen
  - [ ] Mobile-Ansicht
  - [ ] Tablet-Ansicht
  - [ ] Desktop-Ansicht

### 9. Dokumentation

- [x] Code-Dokumentation
  - [x] JSDoc-Kommentare für Funktionen
  - [x] Typdefinitionen
- [x] Benutzerhandbuch
  - [x] Beschreibung der Funktionen
  - [x] Erklärung der Datenquellen
- [x] Technische Dokumentation
  - [x] Architekturübersicht
  - [x] API-Integration
  - [x] Caching-Mechanismus

## Annahmen und Abhängigkeiten

### Annahmen

1. Die interne API (`/model/info`) ist verfügbar und liefert Daten im erwarteten Format.
2. Die externe API (LiteLLM GitHub) ist öffentlich zugänglich und ändert ihr Datenformat nicht wesentlich.
3. Die Polyglot-Benchmark-Daten werden regelmäßig aktualisiert und im konsistenten Format bereitgestellt.
4. Die Benutzer haben moderne Browser, die `localStorage` unterstützen.
5. Die Anwendung wird hauptsächlich auf Desktop-Geräten verwendet, ist aber auch auf mobilen Geräten nutzbar.

### Abhängigkeiten

1. Verfügbarkeit der internen API und entsprechende Zugriffsrechte.
2. Stabilität der externen LiteLLM-Datenquelle.
3. Aktualität und Korrektheit der Polyglot-Benchmark-Daten.
4. Korrekte Konfiguration der Umgebungsvariablen für API-Schlüssel und Endpunkte.

## Definition of Done

### Gesamtprojekt

- [x] Alle geplanten Funktionen sind implementiert und funktionieren wie erwartet.
- [x] Die Anwendung ist responsiv und funktioniert auf verschiedenen Geräten und Browsern.
- [x] Der Code ist gut dokumentiert und folgt den Best Practices.
- [x] Die Anwendung ist performant und reagiert schnell auf Benutzerinteraktionen.
- [x] Alle bekannten Fehler sind behoben.
- [x] Die Anwendung wurde manuell getestet und validiert.

### Meilensteine

1. **API-Integration abgeschlossen**
   - [x] Beide externen Datenquellen können erfolgreich abgerufen werden.
   - [x] Die Daten werden korrekt zusammengeführt.
   - [x] Fehlerbehandlung ist implementiert.
   - [ ] Polyglot-Benchmark-Daten sind integriert und werden korrekt mit anderen Daten verknüpft.

2. **Caching-Mechanismus implementiert**
   - [x] Daten werden im localStorage gespeichert und abgerufen.
   - [x] Cache-Invalidierung funktioniert wie erwartet.
   - [x] Fallback-Logik ist implementiert.

3. **UI-Komponenten fertiggestellt**
   - [x] Alle geplanten Ansichten sind implementiert.
   - [x] Die Komponenten sind responsiv und benutzerfreundlich.
   - [x] Die Interaktionselemente funktionieren wie erwartet.

4. **Dokumentation abgeschlossen**
   - [x] Code-Dokumentation ist vollständig.
   - [x] Benutzerhandbuch ist erstellt.
   - [x] Technische Dokumentation ist verfügbar.