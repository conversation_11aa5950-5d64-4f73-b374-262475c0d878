import * as React from "react";
import {
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  InfoIcon,
  Search,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Input } from "../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { useTranslation, t } from "../../contexts/TranslationContext";
import type { Translations } from "../../contexts/TranslationContext";

// Define global window interface
declare global {
  interface Window {
    __TRANSLATIONS__?: Translations;
    __CURRENT_LANG__?: string;
  }
}

// Simple Badge component since it's not available in ui
const Badge: React.FC<{
  variant?: "outline" | "default";
  className?: string;
  children: React.ReactNode;
}> = ({ variant = "default", className = "", children }) => {
  const baseClasses =
    "inline-flex items-center px-2 py-1 rounded text-xs font-medium";
  const variantClasses =
    variant === "outline"
      ? "border border-input bg-background text-foreground"
      : "bg-muted text-muted-foreground";

  return (
    <span className={`${baseClasses} ${variantClasses} ${className}`}>
      {children}
    </span>
  );
};
import type { ModelCard } from "@/types/model-cards";
import type {
  UseCaseRecommendations,
  ModelRecommendation,
} from "@/types/model-recommendations";
import {
  getAllUseCaseRecommendations,
  STANDARD_USE_CASES,
} from "@/services/model-recommendations";

interface UseCaseOverviewDashboardProps {
  modelCards: ModelCard[];
  onModelSelect?: (modelId: string) => void;
}

interface UseCaseCardProps {
  useCaseRecommendations: UseCaseRecommendations;
  modelCards: ModelCard[];
  onModelSelect?: (modelId: string) => void;
}

const SuitabilityIcon: React.FC<{
  suitability: string;
  className?: string;
}> = ({ suitability, className = "w-4 h-4" }) => {
  switch (suitability) {
    case "excellent":
      return (
        <Star
          className={`${className} text-green-600 dark:text-green-400 fill-current`}
        />
      );
    case "good":
      return (
        <CheckCircle
          className={`${className} text-blue-600 dark:text-blue-400`}
        />
      );
    case "acceptable":
      return (
        <Clock
          className={`${className} text-yellow-600 dark:text-yellow-400`}
        />
      );
    case "limited":
      return (
        <AlertCircle
          className={`${className} text-red-600 dark:text-red-400`}
        />
      );
    default:
      return <InfoIcon className={`${className} text-muted-foreground`} />;
  }
};

const ModelRecommendationItem: React.FC<{
  recommendation: ModelRecommendation;
  modelCard: ModelCard;
  onModelSelect?: (modelId: string) => void;
}> = ({ recommendation, modelCard, onModelSelect }) => {
  const { translations } = useTranslation();

  // Use global translations as fallback if context translations are empty
  const getTranslation = (key: string, replacements: Record<string, string | number> = {}) => {
    // Check if context translations have data
    if (translations && Object.keys(translations).length > 0) {
      return t(translations, key, replacements);
    }
    
    // Fallback to global translations if available
    if (typeof window !== 'undefined' && window.__TRANSLATIONS__) {
      return t(window.__TRANSLATIONS__ as Translations, key, replacements);
    }
    
    // Last resort - return the key itself
    return key;
  };

  const getScoreColor = (score: number) => {
    if (score >= 80)
      return "text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/30";
    if (score >= 65)
      return "text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/30";
    if (score >= 50)
      return "text-yellow-600 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/30";
    return "text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/30";
  };

  const getCostEffectivenessColor = (effectiveness: string) => {
    switch (effectiveness) {
      case "high":
        return "text-green-700 bg-green-100 dark:text-green-300 dark:bg-green-900/30";
      case "medium":
        return "text-blue-700 bg-blue-100 dark:text-blue-300 dark:bg-blue-900/30";
      case "low":
        return "text-orange-700 bg-orange-100 dark:text-orange-300 dark:bg-orange-900/30";
      default:
        return "text-muted-foreground bg-muted";
    }
  };

  const getCostEffectivenessText = (effectiveness: string) => {
    switch (effectiveness) {
      case "high":
        return getTranslation('use_case_dashboard.cost_effective');
      case "medium":
        return getTranslation('use_case_dashboard.standard_cost');
      case "low":
        return getTranslation('use_case_dashboard.expensive');
      default:
        return effectiveness;
    }
  };

  return (
    <div
      className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent cursor-pointer transition-colors"
      onClick={() => onModelSelect?.(recommendation.modelId)}
    >
      <div className="flex items-center gap-3 flex-1">
        <SuitabilityIcon suitability={recommendation.suitability} />
        <div>
          <div className="font-medium text-foreground">
            {modelCard.basicInfo.displayName}
          </div>
          <div className="text-sm text-muted-foreground">
            {modelCard.basicInfo.provider}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Badge
          variant="outline"
          className={getCostEffectivenessColor(
            recommendation.costEffectiveness
          )}
        >
          {getCostEffectivenessText(recommendation.costEffectiveness)}
        </Badge>
        <div
          className={`px-2 py-1 rounded text-sm font-bold ${getScoreColor(
            recommendation.score
          )}`}
        >
          {recommendation.score}
        </div>
      </div>
    </div>
  );
};

const UseCaseCard: React.FC<UseCaseCardProps> = ({
  useCaseRecommendations,
  modelCards,
  onModelSelect,
}) => {
  const { translations } = useTranslation();
  const { useCase, recommendedModels, alternativeModels } =
    useCaseRecommendations;

  // Use global translations as fallback if context translations are empty
  const getTranslation = (key: string, replacements: Record<string, string | number> = {}) => {
    // Check if context translations have data
    if (translations && Object.keys(translations).length > 0) {
      return t(translations, key, replacements);
    }
    
    // Fallback to global translations if available
    if (typeof window !== 'undefined' && window.__TRANSLATIONS__) {
      return t(window.__TRANSLATIONS__ as Translations, key, replacements);
    }
    
    // Last resort - return the key itself
    return key;
  };

  // Get model cards for display
  const getModelCard = (modelId: string): ModelCard | null => {
    return (
      modelCards.find((card) => card.basicInfo.modelId === modelId) || null
    );
  };

  // Show top 3 recommended models + top 2 alternatives
  const topModels = [
    ...recommendedModels.slice(0, 5),
    ...alternativeModels.slice(0, 3),
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          {useCase.name}
          <Badge variant="outline" className="ml-auto">
            {recommendedModels.length} {getTranslation('use_case_dashboard.recommended_count')}
          </Badge>
        </CardTitle>
        <CardDescription>{useCase.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {topModels.length > 0 ? (
            topModels.map((rec) => {
              const modelCard = getModelCard(rec.modelId);
              return modelCard ? (
                <ModelRecommendationItem
                  key={rec.modelId}
                  recommendation={rec}
                  modelCard={modelCard}
                  onModelSelect={onModelSelect}
                />
              ) : null;
            })
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              <AlertCircle className="w-8 h-8 mx-auto mb-2" />
              <p>{getTranslation('use_case_dashboard.no_suitable_models')}</p>
            </div>
          )}
        </div>

        {recommendedModels.length + alternativeModels.length > 5 && (
          <div className="mt-3 pt-3 border-t">
            <p className="text-sm text-muted-foreground text-center">
              +{recommendedModels.length + alternativeModels.length - 5} {getTranslation('use_case_dashboard.additional_models')}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export const UseCaseOverviewDashboard: React.FC<
  UseCaseOverviewDashboardProps
> = ({ modelCards, onModelSelect }) => {
  const { translations } = useTranslation();
  const [searchTerm, setSearchTerm] = React.useState("");
  const [selectedCategory, setSelectedCategory] = React.useState<string>("all");
  const [sortBy, setSortBy] = React.useState<"name" | "recommended">("name");

  // Use global translations as fallback if context translations are empty
  const getTranslation = (key: string, replacements: Record<string, string | number> = {}) => {
    // Check if context translations have data
    if (translations && Object.keys(translations).length > 0) {
      return t(translations, key, replacements);
    }
    
    // Fallback to global translations if available
    if (typeof window !== 'undefined' && window.__TRANSLATIONS__) {
      return t(window.__TRANSLATIONS__ as Translations, key, replacements);
    }
    
    // Last resort - return the key itself
    return key;
  };

  // Generate all use case recommendations
  const allRecommendations = React.useMemo(() => {
    return getAllUseCaseRecommendations(modelCards);
  }, [modelCards]);

  // Filter and sort use cases
  const filteredRecommendations = React.useMemo(() => {
    let filtered = allRecommendations;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        (rec) =>
          rec.useCase.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          rec.useCase.description
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          rec.useCase.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter(
        (rec) => rec.useCase.category === selectedCategory
      );
    }

    // Sort
    if (sortBy === "recommended") {
      filtered = filtered.sort(
        (a, b) => b.recommendedModels.length - a.recommendedModels.length
      );
    } else {
      filtered = filtered.sort((a, b) =>
        a.useCase.name.localeCompare(b.useCase.name)
      );
    }

    return filtered;
  }, [allRecommendations, searchTerm, selectedCategory, sortBy]);

  // Get unique categories from use cases
  const categories = React.useMemo(() => {
    const cats = new Set<string>();
    STANDARD_USE_CASES.forEach((uc) => cats.add(uc.category));
    return Array.from(cats).sort();
  }, []);

  // Calculate summary statistics
  const totalRecommendations = allRecommendations.reduce(
    (sum, rec) => sum + rec.recommendedModels.length,
    0
  );

  const avgRecommendationsPerUseCase =
    totalRecommendations / allRecommendations.length;

  return (
    <div className="space-y-6">
      {/* Header and Summary */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-foreground mb-4">
          {getTranslation('use_case_dashboard.title')}
        </h2>
        <p className="text-muted-foreground mb-4">
          {getTranslation('use_case_dashboard.description')}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-card rounded-lg p-4 border">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {allRecommendations.length}
            </div>
            <div className="text-sm text-muted-foreground">{getTranslation('use_case_dashboard.use_cases')}</div>
          </div>
          <div className="bg-card rounded-lg p-4 border">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {modelCards.length}
            </div>
            <div className="text-sm text-muted-foreground">
              {getTranslation('use_case_dashboard.available_models')}
            </div>
          </div>
          <div className="bg-card rounded-lg p-4 border">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {totalRecommendations}
            </div>
            <div className="text-sm text-muted-foreground">
              {getTranslation('use_case_dashboard.total_recommendations')}
            </div>
          </div>
          <div className="bg-card rounded-lg p-4 border">
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
              {avgRecommendationsPerUseCase.toFixed(1)}
            </div>
            <div className="text-sm text-muted-foreground">
              {getTranslation('use_case_dashboard.avg_recommendations')}
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={getTranslation('use_case_dashboard.search_placeholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder={getTranslation('use_case_dashboard.select_category')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{getTranslation('use_case_dashboard.all_categories')}</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={sortBy}
          onValueChange={(value: "name" | "recommended") => setSortBy(value)}
        >
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name">{getTranslation('use_case_dashboard.sort_by_name')}</SelectItem>
            <SelectItem value="recommended">{getTranslation('use_case_dashboard.sort_by_recommended')}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Use Case Grid */}
      {filteredRecommendations.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredRecommendations.map((recommendation) => (
            <UseCaseCard
              key={recommendation.useCase.id}
              useCaseRecommendations={recommendation}
              modelCards={modelCards}
              onModelSelect={onModelSelect}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Search className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            {getTranslation('use_case_dashboard.no_use_cases_found')}
          </h3>
          <p className="text-muted-foreground">
            {getTranslation('use_case_dashboard.try_different_search')}
          </p>
        </div>
      )}
    </div>
  );
};
