// Types for model-cards.json structure
export interface ModelCardBenchmark {
  benchmarkName: string;
  category: string;
  score: number;
  alternativeScores?: {
    [key: string]: number;
  };
  metric: string;
  attemptType?: string;
  toolsUsed?: boolean;
  date: string;
  notes?: string;
  contextLength?: string;
}

export interface ModelCard {
  basicInfo: {
    modelId: string;
    displayName: string;
    provider: string;
    modelFamily: string;
    version: string;
    description: string;
    releaseDate: string;
    status: string;
    knowledgeCutoff: string;
  };
  technicalSpecs: {
    contextWindow: number;
    maxOutputTokens: number;
    maxReasoningTokens?: number;
    architecture?: string;
    parameterCount?: string;
    supportedInputTypes: string[];
    supportedOutputTypes: string[];
    inputLimitations?: {
      maxImages?: number;
      maxImageSize?: string;
      maxAudioLength?: string;
      maxVideoLength?: string;
      supportedMimeTypes?: {
        [key: string]: string[];
      };
    };
  };
  capabilities: {
    functionCalling: boolean;
    vision: boolean;
    pdfSupport: boolean;
    audioInput: boolean;
    audioOutput: boolean;
    imageGeneration: boolean;
    codeExecution: boolean;
    systemInstructions: boolean;
    promptCaching: boolean;
    batchProcessing: boolean;
    reasoning: boolean;
    thinking: boolean;
    grounding: boolean;
    multilingualSupport: boolean;
    embeddingImageInput: boolean;
    structuredOutputs?: boolean;
    webBrowsing?: boolean;
    codeInterpreter?: boolean;
    dalleIntegration?: boolean;
    realTimeAPI?: boolean;
  };
  performance: {
    latency: string;
    rateLimits: {
      queriesPerMinute?: number;
      tokensPerMinute?: number;
      inputTokensPerMinute?: number;
      outputTokensPerMinute?: number;
      reasoningTokensPerMinute?: number;
      contextLength?: number;
      batchQueueLimit?: number;
    };
    temperature: {
      min: number;
      max: number;
      default: number;
    };
    topP?: number;
    topK?: number;
    reasoningPerformance?: {
      averageReasoningTime?: string;
      maxReasoningTime?: string;
      reasoningEfficiency?: 'High' | 'Medium' | 'Low';
    };
  };
  pricing: {
    inputCostPer1MTokens: number;
    outputCostPer1MTokens: number;
    cachingCosts?: {
      cacheWrites?: number;
      cacheHits: number;
    };
    currency: string;
    reasoningCosts?: {
      reasoningTokensPerMillion: number;
      completionTokensPerMillion: number;
    };
    batchProcessingCosts?: {
      inputCostPer1MTokens: number;
      outputCostPer1MTokens: number;
    };
  };
  availability: {
    regions?: Array<{
      region: string;
      availability: string;
    }>;
    dataProcessingRegions?: string[];
    supportedPlatforms: string[];
    platformSpecificIds?: {
      [key: string]: string;
    };
  };
  security?: {
    dataResidency?: boolean;
    cmekSupport?: boolean;
    vpcSupport?: boolean;
    accessTransparency?: boolean;
    complianceStandards?: string[];
  };
  usageTypes?: {
    dynamicSharedQuota?: boolean;
    provisionedThroughput?: boolean;
    fixedQuota?: boolean;
  };
  benchmarks: ModelCardBenchmark[];
  metadata: {
    lastUpdated: string;
    dataSource: string;
    version: string;
    variants?: Array<{
      modelId: string;
      description: string;
      [key: string]: unknown;
    }>;
  };
}

export interface ModelCardsData {
  modelCards: ModelCard[];
  lastUpdated: string;
  source?: string;
}