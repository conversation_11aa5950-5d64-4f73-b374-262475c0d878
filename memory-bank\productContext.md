# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon README.md and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.

## Project Goal

Create a comprehensive LLM Browser application that serves as a demo for evaluating and comparing different language models using data from iteraGPT, LiteLLM-Public-Data, and Aider-Benchmark-Data sources.

## Key Features

- **Model Comparison Interface**: Browse and compare different LLM models with detailed performance metrics
- **Benchmark Visualization**: Display Aider benchmark data showing model performance on coding tasks
- **Multiple View Modes**: 
  - Benchmark view for performance analysis
  - Models overview for general comparison
  - Model detail view for in-depth information
- **Export Functionality**: Export model data and benchmark results
- **Modern UI/UX**: Built with Next.js 15, TypeScript, Tailwind CSS, and ShadCN UI components
- **Dark Mode Support**: Full theme switching capabilities via next-themes
- **Responsive Design**: Mobile-friendly interface using modern web standards

## Overall Architecture

- **Frontend**: Next.js 15 with App Router, TypeScript, and React 19
- **Styling**: Tailwind CSS 4 with ShadCN UI component library (New York style, neutral theme)
- **Data Management**: Context-based state management with fallback data utilities
- **API Integration**: Service layer for handling iteraGPT/LiteLLM data access
- **Benchmark Data**: Static JSON files from Aider project for model performance evaluation
- **Theme System**: next-themes for dark/light mode with Geist font family

2025-06-05 11:44:03 - Initial Memory Bank creation during UMB operation with project context from README.md and package.json analysis.