import * as React from "react";
import { useTranslation, t } from "../../contexts/TranslationContext";
import type { Translations } from "../../contexts/TranslationContext";

// Define global window interface
declare global {
  interface Window {
    __TRANSLATIONS__?: Translations;
    __CURRENT_LANG__?: string;
  }
}
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "../ui/tooltip";
import { HelpCircle, ExternalLink } from "lucide-react";
import type { ModelData } from "../../types/api";

// Helper function to enrich model data with model card information
import { enrichModelWithCard } from "./utils";

// Import benchmark descriptions
import benchmarkDescriptions from "../../data/benchmarks/benchmark-descriptions.json";

interface ModelComparisonProps {
  models: ModelData[];
  selectedModelIds: string[];
  onClearSelection: () => void;
  formatCurrency: (value: number | null | undefined) => string;
}

export function ModelComparison({ models, selectedModelIds: _selectedModelIds, onClearSelection, formatCurrency }: ModelComparisonProps) {
  // Get translations from context
  const { translations, currentLang } = useTranslation();
  
  // Debug translations
  React.useEffect(() => {
    console.log('ModelComparison - Current language:', currentLang);
    console.log('ModelComparison - Translations available:', !!translations);
    console.log('ModelComparison - Translations type:', typeof translations);
    console.log('ModelComparison - Is translations an array?', Array.isArray(translations));
    
    // Check if global translations are available
    if (typeof window !== 'undefined' && window.__TRANSLATIONS__) {
      console.log('ModelComparison - Global translations available:', !!window.__TRANSLATIONS__);
      console.log('ModelComparison - Global translations type:', typeof window.__TRANSLATIONS__);
      console.log('ModelComparison - Global translations root keys:', Object.keys(window.__TRANSLATIONS__));
      
      // Compare with context translations
      const sameInstance = translations === window.__TRANSLATIONS__;
      console.log('ModelComparison - Using same instance as global:', sameInstance);
      
      if (!sameInstance && window.__TRANSLATIONS__ && typeof window.__TRANSLATIONS__ === 'object') {
        const globalTranslations = window.__TRANSLATIONS__ as Translations;
        const models = globalTranslations.models;
        
        if (models && typeof models === 'object') {
          const comparison = models.comparison;
          if (comparison && typeof comparison === 'object') {
            console.log('ModelComparison - Global translations has models.comparison with keys:',
              Object.keys(comparison));
          }
        }
      }
    } else {
      console.log('ModelComparison - No global translations available');
    }
    
    // Log the entire translations object for debugging
    try {
      console.log('ModelComparison - Full translations object:', JSON.stringify(translations, null, 2));
    } catch (error) {
      console.error('ModelComparison - Error stringifying translations:', error);
    }
    
    if (translations) {
      console.log('ModelComparison - Root translation keys:', Object.keys(translations));
      
      // Check if models exists and is an object
      const models = translations.models;
      console.log('ModelComparison - models exists:', !!models);
      console.log('ModelComparison - models type:', typeof models);
      
      if (models && typeof models === 'object' && !Array.isArray(models)) {
        console.log('ModelComparison - models keys:', Object.keys(models));
        
        // Check if comparison exists and is an object
        const comparison = models.comparison;
        console.log('ModelComparison - comparison exists:', !!comparison);
        console.log('ModelComparison - comparison type:', typeof comparison);
        
        if (comparison && typeof comparison === 'object' && !Array.isArray(comparison)) {
          console.log('ModelComparison - models.comparison keys:', Object.keys(comparison));
          
          // Test a specific translation key
          const testKey = 'title';
          console.log(`ModelComparison - Testing key "models.comparison.${testKey}":`,
            comparison[testKey],
            `(typeof: ${typeof comparison[testKey]})`
          );
          
          // Test the t function directly
          const translatedTitle = getTranslation('models.comparison.title', { count: '2' });
          console.log('ModelComparison - Direct t function test for title:', translatedTitle);
          
          // Test direct property access vs t function
          Object.keys(comparison).forEach(key => {
            const directAccess = comparison[key];
            const tFunctionAccess = getTranslation(`models.comparison.${key}`);
            console.log(`ModelComparison - Key "${key}": direct=${directAccess}, t()=${tFunctionAccess}`);
          });
        } else {
          console.warn('ModelComparison - models.comparison is not an object:', comparison);
          
          // Check for case-insensitive match
          if (models) {
            const keys = Object.keys(models);
            const comparisonKey = keys.find(k => k.toLowerCase() === 'comparison');
            if (comparisonKey) {
              console.log(`ModelComparison - Found case-sensitive key "${comparisonKey}" instead of "comparison"`);
            }
          }
        }
      } else {
        console.warn('ModelComparison - models is not an object:', models);
        
        // Check for case-insensitive match
        const keys = Object.keys(translations);
        const modelsKey = keys.find(k => k.toLowerCase() === 'models');
        if (modelsKey) {
          console.log(`ModelComparison - Found case-sensitive key "${modelsKey}" instead of "models"`);
        }
      }
      
      // Check if components exists and is an object
      const components = translations.components;
      if (components && typeof components === 'object' && !Array.isArray(components)) {
        console.log('ModelComparison - components keys:', Object.keys(components));
      }
      
      // Test a few translation keys directly
      const testKeys = [
        'models.comparison.title',
        'models.comparison.reset_selection',
        'models.comparison.property',
        'models.comparison.not_available'
      ];
      
      testKeys.forEach(key => {
        console.log(`ModelComparison - Testing key "${key}":`, getTranslation(key, { count: '2' }));
      });
      
      // Test with hardcoded translations object
      const hardcodedTranslations = {
        models: {
          comparison: {
            title: "Model Comparison ({{count}} models)",
            reset_selection: "Reset Selection",
            property: "Property",
            not_available: "N/A"
          }
        }
      };
      
      console.log('ModelComparison - Testing with hardcoded translations:');
      testKeys.forEach(key => {
        // For hardcoded tests, we still use the original t function since getTranslation uses the global object
        console.log(`ModelComparison - Hardcoded test "${key}":`, t(hardcodedTranslations, key, { count: '2' }));
      });
    }
  }, [translations, currentLang]);
  // Check if values have variance (are different from each other)
  const hasVariance = (models: ModelData[], field: keyof ModelData) => {
    const values = models
      .map(model => model[field] as number | undefined)
      .filter(value => value !== undefined && value !== null);
    
    if (values.length <= 1) return false;
    
    const firstValue = values[0];
    return values.some(value => value !== firstValue);
  };
  
  // Find best value (lowest cost, highest context window) - only if values vary
  const getBestValue = (models: ModelData[], field: keyof ModelData, isLowerBetter: boolean = true) => {
    if (models.length === 0) return null;
    
    // Only highlight if there's variance in the values
    if (!hasVariance(models, field)) return null;
    
    // Find first model with a valid value for the field
    const firstValidModel = models.find(model =>
      model[field] !== undefined && model[field] !== null
    );
    
    if (!firstValidModel) return null;
    
    let bestModelId = firstValidModel.id;
    let bestValue = firstValidModel[field] as number;
    
    models.forEach(model => {
      // Skip models without IDs
      if (!model.id) return;
      
      const value = model[field] as number | undefined;
      if (value === undefined || value === null) return;
      
      if (isLowerBetter) {
        if (value < (bestValue as number)) {
          bestValue = value;
          bestModelId = model.id;
        }
      } else {
        if (value > (bestValue as number)) {
          bestValue = value;
          bestModelId = model.id;
        }
      }
    });
    
    return bestModelId;
  };
  
  // Get best models
  const bestInputCost = getBestValue(models, "inputCostPerToken", true);
  const bestOutputCost = getBestValue(models, "outputCostPerToken", true);
  const bestContextWindow = getBestValue(models, "contextWindow", false);
  
  
  // Get best benchmark score for a specific benchmark
  const getBestBenchmarkScore = (benchmarkName: string) => {
    const enrichedModels = models.map(model => enrichModelWithCard(model));
    const modelsWithBenchmark = enrichedModels.filter(model =>
      model.modelCard?.benchmarks?.some(b => b.benchmarkName === benchmarkName)
    );
    
    if (modelsWithBenchmark.length <= 1) return null;
    
    let bestModelId: string | null = null;
    let bestScore = -1;
    
    modelsWithBenchmark.forEach(model => {
      if (!model.id) return;
      
      const benchmark = model.modelCard?.benchmarks.find(b => b.benchmarkName === benchmarkName);
      if (benchmark && benchmark.score > bestScore) {
        bestScore = benchmark.score;
        bestModelId = model.id;
      }
    });
    
    return bestModelId;
  };
  

  // Get benchmark info from descriptions
  const getBenchmarkInfo = (benchmarkName: string) => {
    return benchmarkDescriptions.benchmarks[benchmarkName as keyof typeof benchmarkDescriptions.benchmarks];
  };

  // Get category info
  const getCategoryInfo = (categoryName: string) => {
    return benchmarkDescriptions.categories[categoryName as keyof typeof benchmarkDescriptions.categories];
  };

  // Get difficulty info
  const getDifficultyInfo = (difficulty: string) => {
    return benchmarkDescriptions.difficultyLevels[difficulty as keyof typeof benchmarkDescriptions.difficultyLevels];
  };

  // Get all unique benchmark names from model cards grouped by category
  const getBenchmarksByCategory = () => {
    const enrichedModels = models.map(model => enrichModelWithCard(model));
    const benchmarksByCategory: Record<string, string[]> = {};
    
    // Add the special Aider benchmark from benchmarkData if any model has it
    const hasAiderBenchmarkData = models.some(model =>
      model.benchmarkData?.pass_rate_2 !== undefined
    );
    
    if (hasAiderBenchmarkData) {
      const aiderCategory = "Code editing";
      if (!benchmarksByCategory[aiderCategory]) {
        benchmarksByCategory[aiderCategory] = [];
      }
      benchmarksByCategory[aiderCategory].push("Aider-Polyglot (benchmarkData)");
    }
    
    enrichedModels.forEach(model => {
      if (model.modelCard?.benchmarks) {
        model.modelCard.benchmarks.forEach(benchmark => {
          const benchmarkInfo = getBenchmarkInfo(benchmark.benchmarkName);
          
          // Special handling for Aider-Polyglot from model cards - force to Code editing category
          if (benchmark.benchmarkName.toLowerCase().includes('aider')) {
            const category = "Code editing";
            if (!benchmarksByCategory[category]) {
              benchmarksByCategory[category] = [];
            }
            if (!benchmarksByCategory[category].includes(benchmark.benchmarkName)) {
              benchmarksByCategory[category].push(benchmark.benchmarkName);
            }
          } else {
            const category = benchmarkInfo?.category || getTranslation('models.comparison.other_benchmarks');
            if (!benchmarksByCategory[category]) {
              benchmarksByCategory[category] = [];
            }
            if (!benchmarksByCategory[category].includes(benchmark.benchmarkName)) {
              benchmarksByCategory[category].push(benchmark.benchmarkName);
            }
          }
        });
      }
    });
    
    // Sort benchmarks within each category
    Object.keys(benchmarksByCategory).forEach(category => {
      benchmarksByCategory[category].sort();
    });
    
    return benchmarksByCategory;
  };

  const benchmarksByCategory = getBenchmarksByCategory();

  // Use global translations as fallback if context translations are empty
  const getTranslation = (key: string, replacements: Record<string, string> = {}) => {
    // Check if context translations have data
    if (translations && Object.keys(translations).length > 0) {
      return t(translations, key, replacements);
    }
    
    // Fallback to global translations if available
    if (typeof window !== 'undefined' && window.__TRANSLATIONS__) {
      return t(window.__TRANSLATIONS__ as Translations, key, replacements);
    }
    
    // Last resort - return the key itself
    return key;
  };

  return (
    <Card className="mt-6">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>{getTranslation('models.comparison.title', { count: models.length.toString() })}</CardTitle>
        <Button variant="outline" size="sm" onClick={onClearSelection}>
          {getTranslation('models.comparison.reset_selection')}
        </Button>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">{getTranslation('models.comparison.property')}</TableHead>
                {models.map((model, index) => (
                  <TableHead key={model.id || `model-head-${index}`}>{model.name}</TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell className="font-medium">{getTranslation('models.comparison.provider')}</TableCell>
                {models.map((model, index) => (
                  <TableCell key={model.id || `model-provider-${index}`}>{model.provider}</TableCell>
                ))}
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">{getTranslation('models.comparison.litellm_availability')}</TableCell>
                {models.map((model, index) => (
                  <TableCell key={model.id || `model-litellm-${index}`}>
                    {model['liteLLM-provisioning'] === true ? (
                      <span className="text-green-600 font-medium">✓ {getTranslation('models.comparison.available')}</span>
                    ) : model['liteLLM-provisioning'] === false ? (
                      <span className="text-muted-foreground">✗ {getTranslation('models.comparison.model_card_only')}</span>
                    ) : (
                      <span className="text-muted-foreground">{getTranslation('models.comparison.not_available')}</span>
                    )}
                  </TableCell>
                ))}
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">{getTranslation('models.comparison.context_window')}</TableCell>
                {models.map((model, index) => {
                  const value = model.contextWindow?.toLocaleString() || getTranslation('models.comparison.not_available');
                  const isNA = value === getTranslation('models.comparison.not_available');
                  const isBest = model.id && model.id === bestContextWindow;
                  
                  return (
                    <TableCell
                      key={model.id || `model-context-${index}`}
                      className={
                        isBest ? "font-bold text-green-600" :
                        isNA ? "text-muted-foreground" : ""
                      }
                    >
                      {value}
                    </TableCell>
                  );
                })}
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">{getTranslation('models.comparison.max_output_tokens')}</TableCell>
                {models.map((model, index) => {
                  const value = model.maxOutputTokens?.toLocaleString() || model.maxTokens?.toLocaleString() || getTranslation('models.comparison.not_available');
                  const isNA = value === getTranslation('models.comparison.not_available');
                  
                  return (
                    <TableCell
                      key={model.id || `model-tokens-${index}`}
                      className={isNA ? "text-muted-foreground" : ""}
                    >
                      {value}
                    </TableCell>
                  );
                })}
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">{getTranslation('models.comparison.input_cost')}</TableCell>
                {models.map((model, index) => {
                  const value = formatCurrency(model.inputCostPerToken);
                  const isNA = value === "N/A";
                  const isBest = model.id && model.id === bestInputCost;
                  
                  return (
                    <TableCell
                      key={model.id || `model-input-cost-${index}`}
                      className={
                        isBest ? "font-bold text-green-600" :
                        isNA ? "text-muted-foreground" : ""
                      }
                    >
                      {value}
                    </TableCell>
                  );
                })}
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">{getTranslation('models.comparison.output_cost')}</TableCell>
                {models.map((model, index) => {
                  const value = formatCurrency(model.outputCostPerToken);
                  const isNA = value === "N/A";
                  const isBest = model.id && model.id === bestOutputCost;
                  
                  return (
                    <TableCell
                      key={model.id || `model-output-cost-${index}`}
                      className={
                        isBest ? "font-bold text-green-600" :
                        isNA ? "text-muted-foreground" : ""
                      }
                    >
                      {value}
                    </TableCell>
                  );
                })}
              </TableRow>
              
              
              {/* Dynamic Model Card Benchmarks - Grouped by Category */}
              {Object.keys(benchmarksByCategory).length > 0 &&
                Object.entries(benchmarksByCategory)
                  .sort(([a], [b]) => a.localeCompare(b))
                  .map(([category, benchmarks]) => {
                    const categoryInfo = getCategoryInfo(category);
                    
                    return (
                      <React.Fragment key={`category-${category}`}>
                        {/* Category Header */}
                        <TableRow className="bg-muted/20">
                          <TableCell className="font-medium text-sm text-muted-foreground" colSpan={models.length + 1}>
                            <div className="flex items-center gap-2 py-1">
                              <span className="text-sm">{categoryInfo?.icon}</span>
                              <span>{category}</span>
                              {categoryInfo && (
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="h-3 w-3 text-muted-foreground cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-sm p-4">
                                    <div className="space-y-2">
                                      <div className="font-semibold text-sm">{category}</div>
                                      <div className="text-xs text-muted-foreground">{categoryInfo.description}</div>
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                        
                        {/* Benchmarks in this Category */}
                        {benchmarks.map(benchmarkName => {
                          // Special handling for Aider benchmark from benchmarkData
                          if (benchmarkName === "Aider-Polyglot (benchmarkData)") {
                            return (
                              <TableRow key={`benchmark-${benchmarkName}`}>
                                <TableCell className="font-medium pl-8">
                                  <div className="flex items-center gap-2">
                                    <span>{getTranslation('models.comparison.aider_polyglot_short')}</span>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                                      </TooltipTrigger>
                                      <TooltipContent className="max-w-sm p-4">
                                        <div className="space-y-2">
                                          <div className="font-semibold text-sm">{getTranslation('models.comparison.aider_benchmark.title')}</div>
                                          <div className="text-xs text-muted-foreground">
                                            {getTranslation('models.comparison.aider_benchmark.description')}
                                          </div>
                                          <div className="text-xs">
                                            <span className="font-medium">{getTranslation('models.comparison.metric')}:</span> {getTranslation('models.comparison.aider_benchmark.metric')}
                                            <br />
                                            <span className="font-medium">{getTranslation('models.comparison.range')}:</span> {getTranslation('models.comparison.aider_benchmark.range')}
                                          </div>
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  </div>
                                </TableCell>
                                {models.map((model, index) => {
                                  const passRate = model.benchmarkData?.pass_rate_2;
                                  const totalCost = model.benchmarkData?.total_cost;
                                  const wellFormed = model.benchmarkData?.percent_cases_well_formed;
                                  
                                  let value = getTranslation('models.comparison.not_available');
                                  if (passRate !== undefined) {
                                    value = `${passRate.toFixed(1)}%`;
                                    if (totalCost !== undefined && wellFormed !== undefined) {
                                      value += ` (${getTranslation('models.comparison.at')} ${totalCost.toFixed(0)}€ ${getTranslation('models.comparison.and')} ${wellFormed.toFixed(0)}% ${getTranslation('models.comparison.well_formed_code')})`;
                                    }
                                  }
                                  const isNA = value === getTranslation('models.comparison.not_available');
                                  
                                  // Find best pass rate for highlighting
                                  const allPassRates = models
                                    .map(m => m.benchmarkData?.pass_rate_2)
                                    .filter(rate => rate !== undefined);
                                  const bestPassRate = allPassRates.length > 1 ? Math.max(...allPassRates) : null;
                                  const isBest = passRate !== undefined && bestPassRate !== null && passRate === bestPassRate && allPassRates.length > 1;
                                  
                                  return (
                                    <TableCell
                                      key={model.id || `model-aider-${index}`}
                                      className={
                                        isBest ? "font-bold text-green-600" :
                                        isNA ? "text-muted-foreground" : ""
                                      }
                                    >
                                      {value}
                                    </TableCell>
                                  );
                                })}
                              </TableRow>
                            );
                          }
                          
                          // Special handling for Aider benchmarks from model cards
                          if (benchmarkName.toLowerCase().includes('aider')) {
                            const benchmarkInfo = getBenchmarkInfo(benchmarkName);
                            
                            return (
                              <TableRow key={`benchmark-${benchmarkName}`}>
                                <TableCell className="font-medium pl-8">
                                  <div className="flex items-center gap-2">
                                    <span>{benchmarkName}</span>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                                      </TooltipTrigger>
                                      <TooltipContent className="max-w-sm p-4">
                                        <div className="space-y-2">
                                          <div className="font-semibold text-sm">{benchmarkInfo?.fullName || benchmarkName}</div>
                                          <div className="text-xs text-muted-foreground">
                                            {benchmarkInfo?.description || getTranslation('models.comparison.aider_benchmark.fallback_description')}
                                          </div>
                                          <div className="text-xs">
                                            <span className="font-medium">{getTranslation('models.comparison.metric')}:</span> {benchmarkInfo?.metric || "Score"}
                                            <br />
                                            <span className="font-medium">{getTranslation('models.comparison.range')}:</span> {benchmarkInfo?.scoreRange || "0-100%"}
                                          </div>
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  </div>
                                </TableCell>
                                {models.map((model, index) => {
                                  const enrichedModel = enrichModelWithCard(model);
                                  const benchmark = enrichedModel.modelCard?.benchmarks?.find(b => b.benchmarkName === benchmarkName);
                                  const bestBenchmarkId = getBestBenchmarkScore(benchmarkName);
                                  const value = benchmark ? `${benchmark.score.toFixed(1)}%` : getTranslation('models.comparison.not_available');
                                  const isNA = value === getTranslation('models.comparison.not_available');
                                  const isBest = model.id && model.id === bestBenchmarkId;
                                  
                                  return (
                                    <TableCell
                                      key={model.id || `model-${benchmarkName}-${index}`}
                                      className={
                                        isBest ? "font-bold text-green-600" :
                                        isNA ? "text-muted-foreground" : ""
                                      }
                                    >
                                      {value}
                                    </TableCell>
                                  );
                                })}
                              </TableRow>
                            );
                          }
                          
                          // Regular benchmarks from model cards
                          const benchmarkInfo = getBenchmarkInfo(benchmarkName);
                          const difficultyInfo = benchmarkInfo ? getDifficultyInfo(benchmarkInfo.difficulty) : null;
                          
                          return (
                            <TableRow key={`benchmark-${benchmarkName}`}>
                              <TableCell className="font-medium pl-8">
                                <div className="flex items-center gap-2">
                                  <span>{benchmarkName}</span>
                                  {benchmarkInfo && (
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                                      </TooltipTrigger>
                                      <TooltipContent className="max-w-sm p-4">
                                        <div className="space-y-2">
                                          <div className="font-semibold text-sm">{benchmarkInfo.fullName}</div>
                                          <div className="text-xs text-muted-foreground">{benchmarkInfo.description}</div>
                                          
                                          <div className="grid grid-cols-2 gap-2 text-xs">
                                            <div>
                                              <span className="font-medium">{getTranslation('models.comparison.category')}:</span>
                                              <div className="flex items-center gap-1">
                                                {categoryInfo?.icon} {benchmarkInfo.category}
                                              </div>
                                            </div>
                                            <div>
                                              <span className="font-medium">{getTranslation('models.comparison.difficulty')}:</span>
                                              <div
                                                className={`inline-block px-1 py-0.5 rounded text-xs text-white ${
                                                  benchmarkInfo.difficulty === 'Easy' ? 'bg-green-500' :
                                                  benchmarkInfo.difficulty === 'Medium' ? 'bg-yellow-500' :
                                                  benchmarkInfo.difficulty === 'Hard' ? 'bg-red-500' :
                                                  'bg-gray-500'
                                                }`}
                                              >
                                                {benchmarkInfo.difficulty}
                                              </div>
                                            </div>
                                          </div>
                                          
                                          <div className="text-xs">
                                            <span className="font-medium">{getTranslation('models.comparison.metric')}:</span> {benchmarkInfo.metric}
                                            <br />
                                            <span className="font-medium">{getTranslation('models.comparison.range')}:</span> {benchmarkInfo.scoreRange}
                                          </div>
                                          
                                          {benchmarkInfo.variants && benchmarkInfo.variants.length > 0 && (
                                            <div className="text-xs">
                                              <span className="font-medium">{getTranslation('models.comparison.variants')}:</span> {benchmarkInfo.variants.join(", ")}
                                            </div>
                                          )}
                                          
                                          {(benchmarkInfo.website || benchmarkInfo.paperUrl) && (
                                            <div className="flex gap-2 pt-1">
                                              {benchmarkInfo.website && (
                                                <a
                                                  href={benchmarkInfo.website}
                                                  target="_blank"
                                                  rel="noopener noreferrer"
                                                  className="text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1"
                                                >
                                                  {getTranslation('models.comparison.website')} <ExternalLink className="h-3 w-3" />
                                                </a>
                                              )}
                                              {benchmarkInfo.paperUrl && (
                                                <a
                                                  href={benchmarkInfo.paperUrl}
                                                  target="_blank"
                                                  rel="noopener noreferrer"
                                                  className="text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1"
                                                >
                                                  {getTranslation('models.comparison.paper')} <ExternalLink className="h-3 w-3" />
                                                </a>
                                              )}
                                            </div>
                                          )}
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  )}
                                  {!benchmarkInfo && (
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                                      </TooltipTrigger>
                                      <TooltipContent className="max-w-sm p-4">
                                        <div className="space-y-2">
                                          <div className="font-semibold text-sm">{benchmarkName}</div>
                                          <div className="text-xs text-muted-foreground">
                                            {getTranslation('models.comparison.no_details_available')}
                                          </div>
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  )}
                                </div>
                              </TableCell>
                              {models.map((model, index) => {
                                const enrichedModel = enrichModelWithCard(model);
                                const benchmark = enrichedModel.modelCard?.benchmarks?.find(b => b.benchmarkName === benchmarkName);
                                const bestBenchmarkId = getBestBenchmarkScore(benchmarkName);
                                const value = benchmark ? `${benchmark.score.toFixed(1)}%` : getTranslation('models.comparison.not_available');
                                const isNA = value === getTranslation('models.comparison.not_available');
                                const isBest = model.id && model.id === bestBenchmarkId;
                                
                                return (
                                  <TableCell
                                    key={model.id || `model-${benchmarkName}-${index}`}
                                    className={
                                      isBest ? "font-bold text-green-600" :
                                      isNA ? "text-muted-foreground" : ""
                                    }
                                  >
                                    {value}
                                  </TableCell>
                                );
                              })}
                            </TableRow>
                          );
                        })}
                      </React.Fragment>
                    );
                  })
              }
              
              <TableRow>
                <TableCell className="font-medium">{getTranslation('models.comparison.available')}</TableCell>
                {models.map((model, index) => (
                  <TableCell key={model.id || `model-available-${index}`}>
                    {model.isAvailable ? getTranslation('models.comparison.yes') : getTranslation('models.comparison.no')}
                  </TableCell>
                ))}
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">{getTranslation('models.comparison.capabilities')}</TableCell>
                {models.map((model, index) => (
                  <TableCell key={model.id || `model-capabilities-${index}`}>
                    <div className="flex flex-wrap gap-1">
                      {[
                        model.supportsVision && getTranslation('models.capabilities.vision'),
                        model.supportsPdfInput && getTranslation('models.capabilities.pdf_input'),
                        model.supportsAudioInput && getTranslation('models.capabilities.audio_input'),
                        model.supportsAudioOutput && getTranslation('models.capabilities.audio_output'),
                        model.supportsEmbeddingImageInput && getTranslation('models.capabilities.embedding_image'),
                        model.supportsFunctionCalling && getTranslation('models.capabilities.function_calling'),
                        model.supportsPromptCaching && getTranslation('models.capabilities.prompt_caching'),
                        model.supportsReasoning && getTranslation('models.capabilities.reasoning'),
                        model.supportsSystemMessages && getTranslation('models.capabilities.system_messages')
                      ].filter(Boolean).map((capability, _capIndex) => (
                        <span key={`${model.id}-capability-${capability}`} className="bg-muted px-1.5 py-0.5 rounded text-xs">
                          {capability}
                        </span>
                      ))}
                    </div>
                  </TableCell>
                ))}
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">{getTranslation('models.comparison.supported_platforms')}</TableCell>
                {models.map((model, index) => {
                  const enrichedModel = enrichModelWithCard(model);
                  const platforms = enrichedModel.modelCard?.availability?.supportedPlatforms;
                  const value = platforms && platforms.length > 0 ? platforms.join(", ") : getTranslation('models.comparison.not_available');
                  const isNA = value === getTranslation('models.comparison.not_available');
                  
                  return (
                    <TableCell
                      key={model.id || `model-platforms-${index}`}
                      className={isNA ? "text-muted-foreground" : ""}
                    >
                      {value}
                    </TableCell>
                  );
                })}
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
