import React, { useEffect } from "react";

function getCookie(name) {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(";").shift();
  return null;
}

export default function LanguageSelector() {
  useEffect(() => {
    const languageSelect = document.getElementById("language-select");
    let lang =
      localStorage.getItem("preferredLanguage") ||
      getCookie("preferredLanguage") ||
      "de";
    if (languageSelect) {
      languageSelect.value = lang;
      languageSelect.addEventListener("change", async (e) => {
        const target = e.target;
        const newLang = target.value;
        const mod = await import("../utils/i18n");
        await mod.changeLanguage(newLang);
        window.location.href = window.location.pathname;
      });
    }
  }, []);

  return (
    <div className="language-selector">
      <select id="language-select" className="language-select">
        <option value="de">Deutsch</option>
        <option value="en">English</option>
        <option value="pl"><PERSON>ski</option>
      </select>
      <style>{`
        .language-selector {
          position: fixed;
          top: 1rem;
          right: 1rem;
          z-index: 1000;
        }
        .language-select {
          padding: 0.5rem;
          border: 1px solid #e2e8f0;
          border-radius: 0.375rem;
          background-color: white;
          font-size: 0.875rem;
          color: #1e293b;
          cursor: pointer;
          transition: border-color 0.2s;
        }
        .language-select:hover {
          border-color: #94a3b8;
        }
        .language-select:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
      `}</style>
    </div>
  );
}
