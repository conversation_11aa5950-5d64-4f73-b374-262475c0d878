import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "../../ui/dialog";
import type { BenchmarkData } from "../../../types/api";

interface BenchmarkDetailDialogProps {
  benchmark: BenchmarkData | null;
  isOpen: boolean;
  onClose: () => void;
}

export function BenchmarkDetailDialog({ benchmark, isOpen, onClose }: BenchmarkDetailDialogProps) {
  if (!benchmark) return null;

  // Get performance color class
  const getPerformanceColor = (score: number) => {
    if (score >= 80) return "text-green-600 dark:text-green-400 font-bold";
    if (score >= 60) return "text-blue-600 dark:text-blue-400 font-semibold";
    if (score >= 40) return "text-orange-600 dark:text-orange-400";
    return "text-red-600 dark:text-red-400";
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">{benchmark.model}</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          {/* Ergebnisse */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold text-lg mb-2">Benchmark Ergebnisse</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Score:</span>
                  <span className={`font-bold ${getPerformanceColor(benchmark.pass_rate_2)}`}>
                    {benchmark.pass_rate_2.toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Bestanden:</span>
                  <span>{benchmark.details.pass_num_2} / {benchmark.details.test_cases || 225}</span>
                </div>
                <div className="flex justify-between">
                  <span>Wohlgeformt:</span>
                  <span>{benchmark.percent_cases_well_formed.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Gesamtkosten:</span>
                  <span>${benchmark.total_cost?.toFixed(2) || "N/A"}</span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-2">Weitere Details</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Edit Format:</span>
                  <span>{benchmark.edit_format || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span>Pass Rate 1:</span>
                  <span>{benchmark.details.pass_rate_1?.toFixed(1) || "N/A"}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Bestanden (1):</span>
                  <span>{benchmark.details.pass_num_1 || "N/A"} / {benchmark.details.test_cases || 225}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Command */}
          <div>
            <h3 className="font-semibold text-lg mb-2">Verwendeter Befehl</h3>
            <code className="block bg-muted p-3 rounded text-sm overflow-x-auto">
              {benchmark.command}
            </code>
          </div>

          {/* Weitere Details falls vorhanden */}
          {benchmark.details && (
            <div>
              <h3 className="font-semibold text-lg mb-2">Detaillierte Statistiken</h3>
              <div className="bg-muted p-4 rounded">
                <pre className="text-sm overflow-x-auto">
                  {JSON.stringify(benchmark.details, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}