## UI/UX Verbesserung - Benchmark und Capability Anzeige

### Aufgabe
Aktualisierung der Benutzeroberfläche zur direkten Anzeige der verwendeten Benchmarks und Capabilities pro Use Case.

```
Aktualisiere in der 'src/pages/empfehlungen/index.astro' (see below for file content) die Anzeige so, dass pro Use-Case auch immer die verwendeten Benchmarks und benötigen Capabilities angezeigt werden, so dass die Erklärungen unten deutlich eingekürzt werden können!
```

**Ziel:** Transparenz verbessern und Erklärungen verkürzen durch direkte Anzeige der relevanten Informationen in der UI.

## Benchmark-Erweiterung für Coding Use Cases

Ergänzung zusätzlicher Benchmarks für coding-spezifische Anwendungsfälle.

```
Für Code-Generierung sollte ergänzend zu den Benchmarks auch "SWE-bench", "Code editing", "Agentic coding" und "Terminal-Bench" verwendet werden. Genauso für "DEbugging & Optimierung", "Troubleshooting", "Test-Entwicklung"
'src/services/model-recommendations.ts' (see below for file content)
```

**Ziel:** Umfassendere Evaluierung von Coding-Modellen durch erweiterte Benchmark-Sets.

## Kostenberechnung - Fehlerbehebung

### Problem
Fehlerhafte Kostenkategorisierung von Claude-Modellen im Empfehlungssystem.

```
Prüfe die Kostenberechnungen. claude-opus wird mit low cost angegeben und claude sonnet 4 mit medium cost, was so nicht stimmt! 'src/data/models/claude-opus-4.json' (see below for file content) , 'src/data/models/claude-sonnet-4.json' (see below for file content)
```

**Kontext:** 
- Claude Opus 4: $15.0/1M Tokens → sollte teuer sein
- Claude Sonnet 4: $3.0/1M Tokens → sollte günstiger sein

**Ziel:** Korrekte Kostenkategorisierung und Cost-Effectiveness-Berechnung.

