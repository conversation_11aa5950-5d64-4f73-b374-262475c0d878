import { describe, it, expect } from 'vitest';
import { transformModelInfo, extractModelId, processBenchmarkData } from './transformations';
import type { BenchmarkData } from '@/types/api';

describe('transformations utilities', () => {
  describe('transformModelInfo', () => {
    it('transforms basic API response correctly', () => {
      const apiResponse = {
        model_name: 'test-model',
        model_info: {
          id: 'test-id',
          litellm_provider: 'test-provider',
          description: 'Test description',
          max_tokens: 4096,
          input_cost_per_token: 0.001,
          output_cost_per_token: 0.002,
          supports_function_calling: true,
          supports_vision: false,
        }
      };

      const result = transformModelInfo([apiResponse]);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 'test-id',
        name: 'test-model',
        provider: 'test-provider',
        description: 'Test description',
        maxTokens: 4096,
        inputCostPerToken: 0.001,
        outputCostPerToken: 0.002,
        supportsFunctionCalling: true,
        supportsVision: false,
      });
      // Separate assertion for supportsVision to handle null vs false
      expect(result[0].supportsVision).toBe(false);
    });

    it('handles missing model_info gracefully', () => {
      const apiResponse = {
        model_name: 'test-model',
        // model_info is missing
      };

      const result = transformModelInfo([apiResponse]);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        name: 'test-model',
        provider: 'Unknown',
        description: '',
      });
    });

    it('handles empty array', () => {
      const result = transformModelInfo([]);
      expect(result).toEqual([]);
    });

    it('generates fallback ID when missing', () => {
      const apiResponse = {
        model_name: 'test-model',
        model_info: {
          // id is missing
          litellm_provider: 'test-provider',
        }
      };

      const result = transformModelInfo([apiResponse]);

      expect(result[0].id).toBeDefined();
      expect(result[0].id).toMatch(/^test-model|model-/);
    });

    it('calculates cost per 1k tokens correctly', () => {
      const apiResponse = {
        model_name: 'test-model',
        model_info: {
          input_cost_per_token: 0.001,
          output_cost_per_token: 0.002,
        }
      };

      const result = transformModelInfo([apiResponse]);

      expect(result[0].inputCostPer1kTokens).toBe(1.0);
      expect(result[0].outputCostPer1kTokens).toBe(2.0);
    });

    it('handles boolean capabilities correctly', () => {
      const apiResponse = {
        model_name: 'test-model',
        model_info: {
          supports_function_calling: true,
          supports_vision: false,
          supports_tool_choice: true,
          supports_reasoning: false,
        }
      };

      const result = transformModelInfo([apiResponse]);

      expect(result[0].supportsFunctionCalling).toBe(true);
      expect(result[0].supportsVision).toBe(false);
      expect(result[0].supportsToolChoice).toBe(true);
      expect(result[0].supportsReasoning).toBe(false);
    });

    it('sets context window from multiple sources', () => {
      const apiResponse1 = {
        model_name: 'test-model-1',
        model_info: {
          context_window: 8192,
        }
      };

      const apiResponse2 = {
        model_name: 'test-model-2',
        model_info: {
          max_input_tokens: 4096,
        }
      };

      const apiResponse3 = {
        model_name: 'test-model-3',
        model_info: {
          max_tokens: 2048,
        }
      };

      const result1 = transformModelInfo([apiResponse1]);
      const result2 = transformModelInfo([apiResponse2]);
      const result3 = transformModelInfo([apiResponse3]);

      expect(result1[0].contextWindow).toBe(8192);
      expect(result2[0].contextWindow).toBe(4096);
      expect(result3[0].contextWindow).toBe(2048);
    });
  });

  describe('extractModelId', () => {
    it('extracts clean model ID from name', () => {
      expect(extractModelId('GPT-4 Turbo')).toBe('gpt-4-turbo');
      expect(extractModelId('Claude 3.5 Sonnet')).toBe('claude-3-5-sonnet');
      expect(extractModelId('Model Name (Preview)')).toBe('model-name');
    });

    it('handles special characters', () => {
      expect(extractModelId('Model@Name#123')).toBe('modelname123');
      expect(extractModelId('Test_Model-v2')).toBe('test_model-v2');
    });

    it('handles empty and whitespace', () => {
      expect(extractModelId('')).toBe('');
      expect(extractModelId('   ')).toBe('');
      expect(extractModelId('  Test  Model  ')).toBe('test-model');
    });
  });

  describe('processBenchmarkData', () => {
    it('processes benchmark data correctly', () => {
      const benchmarkData = [
        {
          model: 'Test Model',
          pass_rate_2: 85.5,
          benchmark: 'test-benchmark',
        }
      ];

      const result = processBenchmarkData(benchmarkData as unknown as BenchmarkData[]);

      expect(result).toHaveLength(1);
      expect(result[0]).toHaveProperty('modelid');
      expect(result[0].modelid).toBe('test-model');
    });

    it('preserves existing modelid', () => {
      const benchmarkData = [
        {
          model: 'Test Model',
          modelid: 'existing-id',
          pass_rate_2: 85.5,
          benchmark: 'test-benchmark',
        }
      ];

      const result = processBenchmarkData(benchmarkData as unknown as BenchmarkData[]);

      expect(result[0].modelid).toBe('existing-id');
    });
  });
});