#!/usr/bin/env node

import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';
import { parse } from 'yaml';

/**
 * Fetches the polyglot leaderboard from <PERSON><PERSON>'s repository and saves it as JSON
 */
async function fetchPolyglotLeaderboard() {
  const url = 'https://raw.githubusercontent.com/Aider-AI/aider/refs/heads/main/aider/website/_data/polyglot_leaderboard.yml';
  
  try {
    console.log('Fetching polyglot leaderboard from:', url);
    
    // Fetch the YAML content
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const yamlContent = await response.text();
    console.log('Successfully fetched YAML content');
    
    // Parse YAML to JavaScript object
    const rawData = parse(yamlContent);
    console.log('Successfully parsed YAML content');
    
    // Transform data to match the expected format
    const transformedData = rawData.map(item => {
      // Generate modelid from model name
      const modelid = item.model
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/--+/g, '-')
        .replace(/^-+|-+$/g, '');
      
      // Extract main fields that should be at the top level
      const {
        dirname,
        test_cases,
        commit_hash,
        pass_rate_1,
        pass_num_1,
        pass_num_2,
        error_outputs,
        num_malformed_responses,
        num_with_malformed_responses,
        user_asks,
        lazy_comments,
        syntax_errors,
        indentation_errors,
        exhausted_context_windows,
        test_timeouts,
        total_tests,
        date,
        versions,
        seconds_per_case,
        thinking_tokens,
        prompt_tokens,
        completion_tokens,
        editor_model,
        editor_edit_format,
        ...topLevelFields
      } = item;
      
      return {
        model: item.model,
        modelid: modelid,
        pass_rate_2: item.pass_rate_2,
        percent_cases_well_formed: item.percent_cases_well_formed,
        total_cost: item.total_cost || 0,
        command: item.command,
        edit_format: item.edit_format,
        details: {
          dirname,
          test_cases,
          commit_hash,
          pass_rate_1,
          pass_num_1,
          pass_num_2,
          error_outputs,
          num_malformed_responses,
          num_with_malformed_responses,
          user_asks,
          lazy_comments,
          syntax_errors,
          indentation_errors,
          exhausted_context_windows,
          test_timeouts,
          total_tests,
          date,
          versions,
          seconds_per_case,
          ...(thinking_tokens !== undefined && { thinking_tokens }),
          ...(prompt_tokens !== undefined && { prompt_tokens }),
          ...(completion_tokens !== undefined && { completion_tokens }),
          ...(editor_model !== undefined && { editor_model }),
          ...(editor_edit_format !== undefined && { editor_edit_format })
        }
      };
    });
    
    console.log('Successfully transformed data to expected format');
    
    // Generate filename with current month (format: YYYY-MM)
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const filename = `polyglot-leaderboard-${year}-${month}.json`;
    
    // Create output path
    const outputDir = join(process.cwd(), 'src', 'data', 'benchmarks');
    const outputPath = join(outputDir, filename);
    
    // Ensure directory exists
    mkdirSync(outputDir, { recursive: true });
    
    // Write JSON file with proper formatting
    writeFileSync(outputPath, JSON.stringify(transformedData, null, 2), 'utf8');
    
    console.log(`✅ Successfully saved polyglot leaderboard to: ${outputPath}`);
    console.log(`📊 Total entries: ${Array.isArray(transformedData) ? transformedData.length : 'N/A'}`);
    
  } catch (error) {
    console.error('❌ Error fetching polyglot leaderboard:', error);
    process.exit(1);
  }
}

// Run the script
fetchPolyglotLeaderboard();