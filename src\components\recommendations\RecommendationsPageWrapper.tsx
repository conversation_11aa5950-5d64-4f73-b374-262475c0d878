import React from 'react';
import { TranslationProvider } from '@/contexts/TranslationContext';
import { RecommendationsPage } from './RecommendationsPage';
import type { ModelCard } from '@/types/model-cards';

interface RecommendationsPageWrapperProps {
  modelCards: ModelCard[];
  onModelSelect?: (modelId: string) => void;
}

export function RecommendationsPageWrapper({ 
  modelCards, 
  onModelSelect 
}: RecommendationsPageWrapperProps) {
  return (
    <TranslationProvider>
      <RecommendationsPage 
        modelCards={modelCards} 
        onModelSelect={onModelSelect} 
      />
    </TranslationProvider>
  );
}