# GitLab Pages Migration: Von Next.js zu Astro

## Überblick

Dieses Dokument beschreibt die Migrationsstrategie für den LLM Browser von der aktuellen Next.js 15 Architektur zu einer GitLab Pages kompatiblen Lösung basierend auf dem Astro Framework.

## Aktuelle Architektur (Next.js 15)

### Kern-Technologien
- **Framework**: Next.js 15 mit App Router
- **Sprache**: TypeScript
- **Styling**: Tailwind CSS 4
- **UI-Komponenten**: ShadCN UI (New York Style, Neutral Theme)
- **State Management**: React Context API
- **Theme System**: next-themes mit Geist Font Family

### Datenquellen
- **API Integration**: /info Endpoint für Modelldaten
- **Benchmark Daten**: Statische JSON-Dateien (Aider-Benchmark-Data)
- **Modellvergleich**: Dynamische Datenverarbeitung mit Deduplication-Algorithmen

### Herausforderungen für GitLab Pages
1. **Server-Side Rendering**: Next.js App Router verwendet SSR/SSG
2. **API Calls**: Runtime API-Aufrufe an /info Endpoint
3. **Dynamische Datenverarbeitung**: Client-seitige Datentransformation
4. **Build-Komplexität**: Next.js Build-Pipeline nicht GitLab Pages optimiert

## Astro Framework als Lösung

### Warum Astro?

#### Vorteile für GitLab Pages
- **Zero-JavaScript by Default**: Optimale Performance für statische Inhalte
- **Islands Architecture**: Selective Hydration für interaktive Komponenten
- **Built-in Static Site Generation**: Perfekt für GitLab Pages
- **Framework Agnostic**: Kann React-Komponenten wiederverwenden
- **Excellent Performance**: Optimierte Bundle-Größen

#### Technische Benefits
- **File-based Routing**: Ähnlich zu Next.js App Router
- **TypeScript Support**: Native TypeScript-Integration
- **Tailwind CSS**: Direkte Unterstützung ohne Konfiguration
- **Component Islands**: React-Komponenten nur wo nötig
- **Build Optimizations**: Automatische Performance-Optimierungen

## Migrationsstrategie

### Phase 1: Projektstruktur Migration

#### Neue Astro Projektstruktur
```
src/
├── components/           # React Components (Islands)
│   ├── ui/              # ShadCN UI Komponenten
│   ├── models/          # Model-spezifische Komponenten
│   └── layout/          # Layout-Komponenten
├── layouts/             # Astro Layout-Komponenten
├── pages/               # File-based Routing
│   ├── index.astro      # Homepage
│   ├── models/          # Model-Seiten
│   └── benchmark/       # Benchmark-Seiten
├── styles/              # Global CSS & Tailwind
├── utils/               # Utility-Funktionen
└── data/                # Statische Daten
    ├── models/          # Model JSON-Dateien
    └── benchmarks/      # Benchmark JSON-Dateien
```

#### Datenstrategie
1. **Build-Time Data Fetching**: API-Calls während des Builds
2. **Static JSON Generation**: Erstelle statische JSON-Dateien
3. **Pre-computed Model Data**: Alle Berechnungen zur Build-Zeit

### Phase 2: Komponenten-Migration

#### React zu Astro Component Islands
```typescript
// Vorher: Next.js React Komponente
'use client'
import { useState } from 'react'

// Nachher: Astro Component Island
---
// Astro Frontmatter für Build-Time Logic
const models = await fetchModelData()
---
<ModelTable client:load models={models} />
```

#### ShadCN UI Integration
- **Beibehaltung**: Alle ShadCN UI Komponenten können als React Islands verwendet werden
- **Optimierung**: Nur interaktive Komponenten werden hydratiert
- **Performance**: Statische Komponenten werden als HTML gerendert

#### Styling Migration
```astro
---
// styles/global.css Import
import '../styles/globals.css'
---
<!-- Tailwind CSS funktioniert identisch -->
<div class="bg-background text-foreground">
  <!-- Content -->
</div>
```

### Phase 3: Datenarchitektur

#### Build-Time Data Processing
```typescript
// astro.config.mjs
export default defineConfig({
  integrations: [
    // Custom Integration für Data Processing
    {
      name: 'model-data-processor',
      hooks: {
        'astro:build:start': async () => {
          // Fetch und Process Model Data
          await generateStaticModelData()
          await processBenchmarkData()
        }
      }
    }
  ]
})
```

#### Static Data Generation Script
```typescript
// scripts/generate-static-data.ts
import { fetchModelInfo, fetchCombinedModelData } from '../src/services/api'

async function generateStaticModelData() {
  // Fetch alle Model-Daten zur Build-Zeit
  const models = await fetchCombinedModelData()
  
  // Schreibe statische JSON-Dateien
  await fs.writeFile(
    'src/data/models/all-models.json', 
    JSON.stringify(models, null, 2)
  )
  
  // Generiere individuelle Model-Seiten
  for (const model of models) {
    await generateModelPage(model)
  }
}
```

### Phase 4: GitLab CI/CD Integration

#### .gitlab-ci.yml Konfiguration
```yaml
stages:
  - build
  - deploy

variables:
  NODE_VERSION: "20"

build:
  stage: build
  image: node:${NODE_VERSION}
  script:
    # Dependencies installieren
    - npm ci
    
    # Statische Daten generieren
    - npm run generate:data
    
    # Astro Build
    - npm run build
    
    # Build-Artefakte für GitLab Pages
    - cp -r dist/ public/
  artifacts:
    paths:
      - public/
  only:
    - main

pages:
  stage: deploy
  script:
    - echo "Deploying to GitLab Pages"
  artifacts:
    paths:
      - public/
  only:
    - main
```

#### Package.json Scripts
```json
{
  "scripts": {
    "dev": "astro dev",
    "build": "astro build",
    "preview": "astro preview",
    "generate:data": "tsx scripts/generate-static-data.ts",
    "prebuild": "npm run generate:data"
  }
}
```

## Implementierungsdetails

### Routing Migration

#### Next.js App Router → Astro Pages
```typescript
// Vorher: src/app/models/[id]/page.tsx
export default function ModelPage({ params }: { params: { id: string } }) {
  return <ModelDetails id={params.id} />
}

// Nachher: src/pages/models/[id].astro
---
export async function getStaticPaths() {
  const models = await import('../../data/models/all-models.json')
  return models.default.map(model => ({
    params: { id: model.id }
  }))
}

const { id } = Astro.params
const models = await import('../../data/models/all-models.json')
const model = models.default.find(m => m.id === id)
---
<Layout title={`Model: ${model.name}`}>
  <ModelDetails client:load model={model} />
</Layout>
```

### State Management

#### Context API → Astro Stores
```typescript
// Vorher: React Context
const ModelContext = createContext()

// Nachher: Nanostores für globalen State
import { atom, map } from 'nanostores'

export const selectedModels = map({})
export const compareMode = atom(false)
```

### Theme System

#### next-themes → Astro Theme Integration
```astro
---
// layouts/Layout.astro
---
<html lang="de">
<head>
  <script>
    // Theme-Detection Script (Inline für Performance)
    const theme = localStorage.getItem('theme') || 
                 (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
    document.documentElement.classList.add(theme)
  </script>
</head>
<body>
  <ThemeToggle client:load />
  <slot />
</body>
</html>
```

## Performance-Optimierungen

### Bundle Optimierung
- **Tree Shaking**: Astro entfernt ungenutzten Code automatisch
- **Code Splitting**: Automatic splitting für JavaScript Islands
- **CSS Optimization**: Tailwind CSS Purging zur Build-Zeit
- **Asset Optimization**: Automatische Bild- und Font-Optimierung

### Lighthouse Scores (Erwartung)
- **Performance**: 95+ (vs. ~80 mit Next.js SSR)
- **Accessibility**: 100 (ShadCN UI Standards beibehalten)
- **Best Practices**: 100
- **SEO**: 100 (Statische HTML-Generation)

## Zeitschätzung & Roadmap

**Konsistent mit detailliertem Implementierungsplan: 7-8 Wochen Gesamtdauer**

### Phase 1: Setup & Grundstruktur (1 Woche)
- [ ] Astro Projekt-Setup im `/astro` Unterverzeichnis
- [ ] Build-Pipeline Konfiguration
- [ ] Basis-Layout Migration
- [ ] GitLab CI/CD Setup

### Phase 2: Daten-Migration (1 Woche)
- [ ] API-Service für Build-Time Fetching
- [ ] Static JSON Generation
- [ ] Deduplication-Algorithmen Migration
- [ ] Typdefinitionen Migration

### Phase 3: UI-Komponenten Migration (2 Wochen)
- [ ] ShadCN UI Integration mit korrekter Installation (`npx shadcn@latest init`)
- [ ] ModelTable als React Island
- [ ] BenchmarkDetails Komponente
- [ ] Layout- und Navigationskomponenten

### Phase 4: Seiten & State Management (1 Woche)
- [ ] Astro-Seiten mit getStaticPaths
- [ ] Nanostores für Client-State
- [ ] Theme-System Migration

### Phase 5: Testing & Optimierung (1 Woche)
- [ ] Unit Tests einrichten
- [ ] Performance-Optimierung
- [ ] Lighthouse-Tests

### Phase 6: Deployment & Integration (3 Tage)
- [ ] GitLab CI/CD-Pipeline konfigurieren
- [ ] Staging-Deployment
- [ ] Performance Validation

### Phase 7: Cutover & Cleanup (2 Tage)
- [ ] Feature-Parität validieren
- [ ] Produktive Umschaltung
- [ ] Dokumentation aktualisieren

## Risiken & Mitigation

### Technische Risiken
1. **API Abhängigkeiten**: 
   - *Risiko*: /info Endpoint zur Build-Zeit nicht verfügbar
   - *Mitigation*: Fallback auf gecachte Daten, Error Handling

2. **Interaktivität**: 
   - *Risiko*: Verlust von Client-seitiger Funktionalität
   - *Mitigation*: Strategische React Islands für interaktive Features

3. **Build-Performance**: 
   - *Risiko*: Lange Build-Zeiten durch Datenverarbeitung
   - *Mitigation*: Incremental Builds, Caching-Strategien

### Projektrisiken
1. **Funktionalitätsverlust**: 
   - *Risiko*: Features gehen bei Migration verloren
   - *Mitigation*: Detaillierte Feature-Mapping, schrittweise Migration

2. **Performance-Regression**: 
   - *Risiko*: Schlechtere UX trotz besserer Lighthouse Scores
   - *Mitigation*: Performance-Testing, User Experience Validation

## Fazit

Die Migration zu Astro bietet signifikante Vorteile für GitLab Pages Deployment:

### Pro
- **Performance**: Deutlich bessere Lighthouse Scores
- **Deployment**: Einfache GitLab Pages Integration
- **Wartbarkeit**: Reduzierte Komplexität durch statische Generation
- **Kosten**: Keine Server-Infrastruktur erforderlich
- **Entwicklung**: Beibehaltung von React-Komponenten wo nötig

### Contra
- **Migrationsaufwand**: 7-8 Wochen Entwicklungszeit
- **Lernkurve**: Team muss Astro-Konzepte erlernen
- **API-Limitationen**: Keine Runtime API-Calls möglich

### Empfehlung
Die Migration ist empfehlenswert, da die Vorteile (Performance, Deployment-Einfachheit, Wartbarkeit) den Migrationsaufwand überwiegen. Das Projekt ist gut für statische Generation geeignet, da die Hauptinteraktionen (Modelvergleich, Filterung) client-seitig implementiert werden können.