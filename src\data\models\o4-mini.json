{"basicInfo": {"modelId": "o4-mini-2025-04-16", "displayName": "o4-mini", "provider": "OpenAI", "modelFamily": "o4", "version": "2025-04-16", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> von o3-mini mit deutlich verbesserter Performance. Faster, more affordable reasoning model mit Vision-Support und Web-Browsing.", "releaseDate": "2025-04-16", "status": "GA", "knowledgeCutoff": "Juni 2024"}, "technicalSpecs": {"contextWindow": 200000, "maxOutputTokens": 100000, "supportedInputTypes": ["text", "image"], "supportedOutputTypes": ["text"]}, "capabilities": {"functionCalling": true, "vision": true, "pdfSupport": true, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": false, "promptCaching": true, "batchProcessing": true, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": true, "structuredOutputs": true, "webBrowsing": true, "codeInterpreter": true, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Moderately Fast", "rateLimits": {"queriesPerMinute": 1000, "tokensPerMinute": 100000, "batchQueueLimit": 1000000}}, "pricing": {"inputCostPer1MTokens": 1.1, "outputCostPer1MTokens": 4.4, "cachingCosts": {"cacheHits": 0.275}, "batchProcessingCosts": {"inputCostPer1MTokens": 0.55, "outputCostPer1MTokens": 2.2}, "currency": "USD"}, "availability": {"supportedPlatforms": ["OpenAI API", "OpenAI Batch API"]}, "benchmarks": [{"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 93.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "No tools verwendet. Übertrifft o3 (91.6%), o3-mini (86.5%) und o1 (74.3%). Ersetzt o3-mini ab 12.06.2025."}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 92.7, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "No tools verwendet. Übertrifft o3 (88.9%), o3-mini (86.5%) und o1 (79.2%). Ersetzt o3-mini ab 12.06.2025."}, {"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 83.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "AIME 2024 and 2025 combined benchmark from vals.ai"}, {"benchmarkName": "SWE-bench Verified", "category": "Agentic coding", "score": 68.1, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Knapp hinter o3 (69.1%), aber deutlich über o1 (48.9%) und o3-mini (49.3%)"}, {"benchmarkName": "MMMU", "category": "Visual reasoning", "score": 79.7, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-30", "notes": "MMMU benchmark from vals.ai"}, {"benchmarkName": "MathVista", "category": "Visual reasoning", "score": 84.3, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Hinter o3 (86.8%), aber deutlich über o1 (71.8%)"}, {"benchmarkName": "CharXiv", "category": "Visual reasoning", "score": 72.0, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Hinter o3 (78.6%), aber über o1 (55.1%)"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 81.4, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Hinter o3 (83.3%), aber über o1 (78.0%) und o3-mini (77.0%)"}, {"benchmarkName": "Terminal-bench", "category": "Agentic coding", "score": 27.5, "alternativeScores": {"terminus": 18.5, "codexCli": 20.0}, "metric": "Accuracy", "attemptType": "single attempt", "date": "2025-05-18", "notes": "Goose agent: 27.5% ±1.3%, Terminus: 18.5% ±1.4%, Codex CLI: 20.0% ±1.5%"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 72.0, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Aider polyglot benchmark with diff edit format (high reasoning effort)"}, {"benchmarkName": "Aider-Polyglot-Wellformated", "category": "Code editing", "score": 90.7, "metric": "Well-formed Rate", "attemptType": "single attempt", "date": "2025-04-16", "notes": "Percentage of well-formed responses in Aider polyglot benchmark"}, {"benchmarkName": "WebDev-Arena", "category": "Agentic coding", "score": 1099.59, "metric": "Arena Score", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Real-time AI coding competition in web development challenges. Rank #19"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 68.3, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-09", "notes": "Holistische und kontaminationsfreie Code-Generierung mit 454 ausgewählten Problemen"}, {"benchmarkName": "Humanity's Last Exam", "category": "Reasoning & Knowledge", "score": 18.08, "alternativeScores": {"high": 18.08, "medium": 14.28}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-09", "notes": "o4-mini (high): 18.08±1.51, o4-mini (medium): 14.28±1.37"}], "metadata": {"lastUpdated": "2025-06-09T12:47:00Z", "dataSource": "DataCamp o4-mini Artikel, OpenAI Platform Documentation, vals.ai AIME Benchmark, vals.ai MMMU Benchmark, Terminal-Bench Leaderboard", "version": "1.4"}}