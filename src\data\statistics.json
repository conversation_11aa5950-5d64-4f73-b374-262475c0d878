{"models": {"totalModels": 40, "totalProviders": 15, "providers": ["vertex_ai-anthropic_models", "anthropic", "bedrock_converse", "Anthropic", "bedrock", "vertex_ai-language-models", "openai", "azure", "OpenAI", "vertex_ai", "hosted_vllm", "azure_ai", "DeepSeek", "xAI", "<PERSON><PERSON><PERSON>"], "modelsWithVision": 29, "modelsWithFunctionCalling": 32, "modelsWithBenchmarks": 31, "contextWindowStats": {"min": 8191, "max": 1048576, "average": 349334}, "costStats": {"min": 1e-07, "max": 2e-05, "average": 3.437972972972973e-06}}, "benchmarks": {"totalBenchmarks": 58, "averagePassRate": 44.3, "topPerformers": [{"model": "gemini-2.5-pro-preview-06-05 (32k think)", "modelid": "gemini-25-pro-preview-06-05-32k-think", "pass_rate_2": 83.1, "percent_cases_well_formed": 99.6, "total_cost": 49.8822, "command": "aider --model gemini/gemini-2.5-pro-preview-06-05 --thinking-tokens 32k", "edit_format": "diff-fenced", "details": {"dirname": "2025-06-06-16-36-21--gemini0605-32k-think-diff-fenced", "test_cases": 225, "commit_hash": "f827f22", "pass_rate_1": 46.2, "pass_num_1": 104, "pass_num_2": 187, "error_outputs": 1, "num_malformed_responses": 1, "num_with_malformed_responses": 1, "user_asks": 112, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 0, "total_tests": 225, "date": "2025-06-06", "versions": "0.84.1.dev", "seconds_per_case": 200.3, "thinking_tokens": 32768, "prompt_tokens": 2719961, "completion_tokens": 4648227}}, {"model": "o3 (high) + gpt-4.1", "modelid": "o3-high-gpt-41-architect", "pass_rate_2": 82.7, "percent_cases_well_formed": 100, "total_cost": 69.2921, "command": "aider --model o3 --architect", "edit_format": "architect", "details": {"dirname": "2025-04-17-01-20-35--o3-mini-high-diff-arch", "test_cases": 225, "commit_hash": "80909e1-dirty", "pass_rate_1": 36, "pass_num_1": 81, "pass_num_2": 186, "error_outputs": 9, "num_malformed_responses": 0, "num_with_malformed_responses": 0, "user_asks": 166, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 0, "total_tests": 225, "date": "2025-04-17", "versions": "0.82.2.dev", "seconds_per_case": 110, "editor_model": "gpt-4.1", "editor_edit_format": "editor-diff"}}, {"model": "o3", "modelid": "o3", "pass_rate_2": 79.6, "percent_cases_well_formed": 95.1, "total_cost": 111.0325, "command": "aider --model o3", "edit_format": "diff", "details": {"dirname": "2025-04-16-21-20-55--o3-high-diff-temp0-exsys", "test_cases": 225, "commit_hash": "24805ff-dirty", "pass_rate_1": 36.9, "pass_num_1": 83, "pass_num_2": 179, "error_outputs": 11, "num_malformed_responses": 11, "num_with_malformed_responses": 11, "user_asks": 110, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 2, "total_tests": 225, "date": "2025-04-16", "versions": "0.82.1.dev", "seconds_per_case": 113.8}}, {"model": "o3 (high)", "modelid": "o3-high", "pass_rate_2": 79.6, "percent_cases_well_formed": 95.1, "total_cost": 111.0325, "command": "aider --model o3", "edit_format": "diff", "details": {"dirname": "2025-04-16-21-20-55--o3-high-diff-temp0-exsys", "test_cases": 225, "commit_hash": "24805ff-dirty", "pass_rate_1": 36.9, "pass_num_1": 83, "pass_num_2": 179, "error_outputs": 11, "num_malformed_responses": 11, "num_with_malformed_responses": 11, "user_asks": 110, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 2, "total_tests": 225, "date": "2025-04-16", "versions": "0.82.1.dev", "seconds_per_case": 113.8}}, {"model": "gemini-2.5-pro-preview-06-05 (default think)", "modelid": "gemini-25-pro-preview-06-05-default-think", "pass_rate_2": 79.1, "percent_cases_well_formed": 100, "total_cost": 45.5961, "command": "aider --model gemini/gemini-2.5-pro-preview-06-05", "edit_format": "diff-fenced", "details": {"dirname": "2025-06-06-18-38-56--gemini0605-diff-fenced", "test_cases": 225, "commit_hash": "4c161f9-dirty", "pass_rate_1": 44.9, "pass_num_1": 101, "pass_num_2": 178, "error_outputs": 4, "num_malformed_responses": 0, "num_with_malformed_responses": 0, "user_asks": 105, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 4, "test_timeouts": 1, "total_tests": 225, "date": "2025-06-06", "versions": "0.84.1.dev", "seconds_per_case": 175.2, "prompt_tokens": 2751296, "completion_tokens": 4142197}}, {"model": "Gemini 2.5 Pro Preview 05-06", "modelid": "gemini-2.5-pro-preview-05-06", "pass_rate_2": 76.9, "percent_cases_well_formed": 97.3, "total_cost": 37.4104, "command": "aider --model gemini/gemini-2.5-pro-preview-05-06", "edit_format": "diff-fenced", "details": {"dirname": "2025-05-07-19-32-40--gemini0506-diff-fenced-completion_cost", "test_cases": 225, "commit_hash": "3b08327-dirty", "pass_rate_1": 36.4, "pass_num_1": 82, "pass_num_2": 173, "error_outputs": 15, "num_malformed_responses": 7, "num_with_malformed_responses": 6, "user_asks": 105, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 2, "total_tests": 225, "date": "2025-05-07", "versions": "0.82.4.dev", "seconds_per_case": 165.3}}, {"model": "Gemini 2.5 Pro Preview 03-25", "modelid": "gemini-2.5-pro-preview-03-25", "pass_rate_2": 72.9, "percent_cases_well_formed": 92.4, "total_cost": 0, "command": "aider --model gemini/gemini-2.5-pro-preview-03-25", "edit_format": "diff-fenced", "details": {"dirname": "2025-04-12-04-55-50--gemini-25-pro-diff-fenced", "test_cases": 225, "commit_hash": "0282574", "pass_rate_1": 40.9, "pass_num_1": 92, "pass_num_2": 164, "error_outputs": 21, "num_malformed_responses": 21, "num_with_malformed_responses": 17, "user_asks": 69, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 2, "total_tests": 225, "date": "2025-04-12", "versions": "0.81.3.dev", "seconds_per_case": 45.3}}, {"model": "o4-mini (high)", "modelid": "o4-mini-high", "pass_rate_2": 72, "percent_cases_well_formed": 90.7, "total_cost": 19.6399, "command": "aider --model o4-mini", "edit_format": "diff", "details": {"dirname": "2025-04-16-22-01-58--o4-mini-high-diff-exsys", "test_cases": 225, "commit_hash": "b66901f-dirty", "pass_rate_1": 19.6, "pass_num_1": 44, "pass_num_2": 162, "error_outputs": 26, "num_malformed_responses": 24, "num_with_malformed_responses": 21, "user_asks": 66, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 1, "test_timeouts": 2, "total_tests": 225, "date": "2025-04-16", "versions": "0.82.1.dev", "seconds_per_case": 176.5}}, {"model": "claude-opus-4-20250514 (32k thinking)", "modelid": "claude-opus-4-20250514-32k-thinking", "pass_rate_2": 72, "percent_cases_well_formed": 97.3, "total_cost": 65.7484, "command": "aider --model claude-opus-4-20250514", "edit_format": "diff", "details": {"dirname": "2025-05-25-20-40-51--opus4-diff-exuser", "test_cases": 225, "commit_hash": "9ef3211", "pass_rate_1": 37.3, "pass_num_1": 84, "pass_num_2": 162, "error_outputs": 10, "num_malformed_responses": 6, "num_with_malformed_responses": 6, "user_asks": 97, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 4, "total_tests": 225, "date": "2025-05-25", "versions": "0.83.3.dev", "seconds_per_case": 44.1, "thinking_tokens": 32000, "prompt_tokens": 2567514, "completion_tokens": 363142}}, {"model": "DeepSeek R1 (0528)", "modelid": "deepseek-r1-0528", "pass_rate_2": 71.4, "percent_cases_well_formed": 94.6, "total_cost": 4.8016, "command": "aider --model deepseek/deepseek-reasoner", "edit_format": "diff", "details": {"dirname": "2025-06-06-16-47-07--r1-diff", "test_cases": 224, "commit_hash": "4c161f9-dirty", "pass_rate_1": 34.4, "pass_num_1": 77, "pass_num_2": 160, "error_outputs": 28, "num_malformed_responses": 15, "num_with_malformed_responses": 12, "user_asks": 105, "lazy_comments": 0, "syntax_errors": 0, "indentation_errors": 0, "exhausted_context_windows": 0, "test_timeouts": 2, "total_tests": 225, "date": "2025-06-06", "versions": "0.84.1.dev", "seconds_per_case": 716.6, "prompt_tokens": 2644169, "completion_tokens": 1842168}}], "combinationModels": 3, "singleModels": 55}, "sources": {"apiModels": 34, "modelCards": 6, "totalModelCards": 22, "modelsJsonExcludesModelCards": true, "enrichedModelsIncludesModelCards": true, "enrichedModelsExcludesModelCards": false}, "enrichment": {"totalModels": 40, "modelsWithBenchmarks": 31, "modelsWithoutBenchmarks": 9, "apiModelsWithLiteLLM": 34, "modelCardsWithoutLiteLLM": 6}, "mappings": {"totalMappings": 65, "mappedModels": 65}, "lastGenerated": "2025-06-21T15:50:21.873Z"}