import type { ModelData, BenchmarkData } from "@/types/api";

// Dynamisches Laden aller polyglot*.json <PERSON>ien aus dem static-Verzeichnis
async function discoverBenchmarkFiles(): Promise<string[]> {
  try {
    // Rufe den API-Endpunkt auf, der alle Benchmark-Dateien im static-Verzeichnis auflistet
    console.log("Fetching benchmark files from API endpoint");
    const response = await fetch('/api/benchmark-files');
    
    if (!response.ok) {
      throw new Error(`Failed to fetch benchmark files: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    const files = data.files || [];
    
    console.log(`API returned ${files.length} benchmark files: ${files.join(', ')}`);
    
    return files;
  } catch (error) {
    console.error("Error discovering benchmark files:", error);
    return [];
  }
}

// Hilfsfunktion zum Laden einer einzelnen Benchmark-Datei
async function loadBenchmarkFile(url: string): Promise<BenchmarkData[]> {
  try {
    console.log(`Fetching benchmark data from ${url}`);
    const response = await fetch(url);
    if (!response.ok) {
      console.warn(`Failed to fetch benchmark data from ${url}: ${response.status} ${response.statusText}`);
      return [];
    }
    const data = await response.json();
    console.log(`Loaded ${data.length} benchmark entries from ${url}`);
    return data;
  } catch (error) {
    console.error(`Error fetching benchmark data from ${url}:`, error);
    return [];
  }
}

// Hilfsfunktion zur Normalisierung von Modellnamen für besseren Vergleich
function normalizeModelIdentifier(identifier: string): string {
  return identifier
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')           // Leerzeichen zu Bindestrichen
    .replace(/[^\w-]/g, '')         // Nur Buchstaben, Zahlen und Bindestriche
    .replace(/-+/g, '-')            // Mehrfache Bindestriche zu einem
    .replace(/^-|-$/g, '');         // Führende/nachgestellte Bindestriche entfernen
}

// Hilfsfunktion zur Prüfung, ob ein Benchmark-Eintrag bereits existiert
function findExistingBenchmark(benchmarks: BenchmarkData[], newItem: BenchmarkData): BenchmarkData | null {
  const newModelId = normalizeModelIdentifier(newItem.modelid);
  const newModelName = normalizeModelIdentifier(newItem.model);
  
  return benchmarks.find(existing => {
    const existingModelId = normalizeModelIdentifier(existing.modelid);
    const existingModelName = normalizeModelIdentifier(existing.model);
    
    // Prüfe sowohl modelid als auch model name
    const modelIdMatch = existingModelId === newModelId;
    const modelNameMatch = existingModelName === newModelName;
    
    return modelIdMatch || modelNameMatch;
  }) || null;
}

// Hilfsfunktion zur Deduplizierung von Benchmark-Daten basierend auf modelid UND model name
function deduplicateBenchmarkData(data: BenchmarkData[]): BenchmarkData[] {
  const uniqueBenchmarks: BenchmarkData[] = [];
  
  data.forEach(item => {
    const existing = findExistingBenchmark(uniqueBenchmarks, item);
    
    if (!existing) {
      // Kein Duplikat gefunden, füge hinzu
      uniqueBenchmarks.push(item);
      console.log(`Added new benchmark: "${item.model}" (modelid: ${item.modelid})`);
    } else {
      // Duplikat gefunden - vergleiche Daten um zu entscheiden, welches behalten werden soll
      const shouldReplace = shouldReplaceExistingBenchmark(existing, item);
      
      if (shouldReplace) {
        // Ersetze das existierende Element
        const index = uniqueBenchmarks.indexOf(existing);
        uniqueBenchmarks[index] = item;
        console.log(`Replacing duplicate benchmark: "${existing.model}" (${existing.modelid}) -> "${item.model}" (${item.modelid})`);
      } else {
        console.log(`Keeping existing benchmark: "${existing.model}" (${existing.modelid}), skipping "${item.model}" (${item.modelid})`);
      }
    }
  });
  
  return uniqueBenchmarks;
}

// Hilfsfunktion um zu entscheiden, ob ein existierender Benchmark-Eintrag ersetzt werden soll
function shouldReplaceExistingBenchmark(existing: BenchmarkData, newItem: BenchmarkData): boolean {
  // Priorität 1: Vergleiche Datum (neueres Datum gewinnt)
  if (existing.details?.date && newItem.details?.date) {
    const existingDate = new Date(existing.details.date as string | number | Date);
    const newDate = new Date(newItem.details.date as string | number | Date);
    
    if (newDate > existingDate) {
      return true; // Neueres Datum, ersetze
    } else if (newDate < existingDate) {
      return false; // Älteres Datum, behalte existing
    }
    // Gleiches Datum, prüfe weitere Kriterien
  }
  
  // Priorität 2: Vergleiche Anzahl Testfälle (mehr Testfälle = besser)
  const existingTestCases = existing.details?.test_cases || 0;
  const newTestCases = newItem.details?.test_cases || 0;
  
  if (newTestCases > existingTestCases) {
    return true; // Mehr Testfälle, ersetze
  } else if (newTestCases < existingTestCases) {
    return false; // Weniger Testfälle, behalte existing
  }
  
  // Priorität 3: Vergleiche Pass-Rate (höhere Pass-Rate = besser)
  const existingPassRate = existing.pass_rate_2 || 0;
  const newPassRate = newItem.pass_rate_2 || 0;
  
  if (newPassRate > existingPassRate) {
    return true; // Höhere Pass-Rate, ersetze
  } else if (newPassRate < existingPassRate) {
    return false; // Niedrigere Pass-Rate, behalte existing
  }
  
  // Priorität 4: Vergleiche Vollständigkeit der Daten (mehr Details = besser)
  const existingDetailsCount = Object.keys(existing.details || {}).length;
  const newDetailsCount = Object.keys(newItem.details || {}).length;
  
  if (newDetailsCount > existingDetailsCount) {
    return true; // Mehr Details, ersetze
  }
  
  // Standard: Behalte existing (erstes gefundenes)
  return false;
}

// Function to fetch benchmark data from all available sources
export async function fetchBenchmarkData(): Promise<BenchmarkData[]> {
  try {
    // Finde alle verfügbaren Benchmark-Dateien
    const benchmarkFiles = await discoverBenchmarkFiles();
    
    // Lade alle Benchmark-Dateien parallel
    const dataPromises = benchmarkFiles.map(file => loadBenchmarkFile(file));
    const dataArrays = await Promise.all(dataPromises);
    
    // Kombiniere alle Daten und filtere leere Arrays heraus
    const combinedData = dataArrays.flat();
    console.log(`Combined ${combinedData.length} benchmark entries from ${benchmarkFiles.length} files`);
    
    // Wenn keine Daten gefunden wurden, gib eine Warnung aus
    if (combinedData.length === 0) {
      console.warn('No benchmark data found in any of the files');
    }
    
    // Stelle sicher, dass alle Einträge eine modelid haben
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const processedData = combinedData.map((item: any) => {
      // Wenn modelid bereits vorhanden ist, verwende diese
      if (item.modelid) {
        return item;
      }
      
      // Ansonsten extrahiere eine modelid aus dem Modellnamen
      // Entferne Versionen und Spezifikationen für eine generische ID
      const extractModelId = (modelName: string): string => {
        // Entferne Klammern und deren Inhalt
        let cleanName = modelName.replace(/\s*\([^)]*\)/g, '');
        
        // Konvertiere zu Kleinbuchstaben und ersetze Leerzeichen durch Bindestriche
        cleanName = cleanName.toLowerCase().trim().replace(/\s+/g, '-');
        
        // Entferne spezielle Zeichen
        cleanName = cleanName.replace(/[^\w-]/g, '');
        
        return cleanName;
      };
      
      const generatedModelId = extractModelId(item.model);
      console.log(`Generated modelid "${generatedModelId}" for model "${item.model}"`);
      
      return {
        ...item,
        modelid: generatedModelId
      };
    });
    
    // Deduplizierung basierend auf modelid
    // Wenn mehrere Einträge mit derselben modelid existieren, behalte den neuesten (basierend auf Datum)
    const deduplicatedData = deduplicateBenchmarkData(processedData);
    console.log(`After deduplication: ${deduplicatedData.length} unique benchmark entries (removed ${processedData.length - deduplicatedData.length} duplicates)`);
    
    return deduplicatedData;
  } catch (error) {
    console.error("Error fetching benchmark data:", error);
    return [];
  }
}

// Function to match model data with benchmark data
export function matchModelWithBenchmark(model: ModelData, benchmarkData: BenchmarkData[]): BenchmarkData | null {
  if (!benchmarkData || benchmarkData.length === 0) {
    console.log("No benchmark data available for matching");
    return null;
  }
  
  // Normalisiere IDs für besseres Matching
  const normalizeId = (id: string): string => {
    return id.toLowerCase().trim();
  };
  
  // Extract base model name without provider prefix
  const extractBaseModelName = (fullName: string): string => {
    const parts = fullName.split('/');
    return parts.length > 1 ? parts[parts.length - 1] : fullName;
  };
  
  // Normalisiere Modellnamen für besseres Matching
  const normalizeModelName = (name: string): string => {
    // Entferne Datumsangaben (z.B. 20250219)
    let normalized = name.replace(/[-_]?\d{8}$/g, '');
    
    // Ersetze Punkte durch Bindestriche (z.B. 3.7 -> 3-7)
    normalized = normalized.replace(/\./g, '-');
    
    // Entferne trailing Versionen und Varianten
    normalized = normalized.replace(/-preview.*$/g, '');
    
    return normalized.toLowerCase().trim();
  };
  
  const modelId = model.id?.toLowerCase() || '';
  const modelName = model.name?.toLowerCase() || '';
  const baseModelName = extractBaseModelName(modelName);
  
  console.log(`Matching model: "${model.name}" (id: ${model.id}), baseModelName: "${baseModelName}"`);
  
  // Keine direkten Mappings oder Aliase mehr verwenden
  // Wir suchen nur direkt nach dem Modellnamen in den Benchmark-Daten
  
  // Hilfsfunktion um zu prüfen, ob ein Benchmark ein Kombinationsmodell ist
  const isCombinationModel = (benchmarkModel: string): boolean => {
    return benchmarkModel.includes(' + ') || benchmarkModel.includes(' & ') || benchmarkModel.includes(' and ');
  };
  
  // Filtere zuerst nicht-Kombinationsmodelle für bessere Matches
  const singleModels = benchmarkData.filter(b => !isCombinationModel(b.model));
  const _combinationModels = benchmarkData.filter(b => isCombinationModel(b.model));
  
  // Priorität 1: Exakter Match über modelID in Einzelmodellen
  let match = singleModels.find(benchmark => {
    const normalizedBenchmarkId = normalizeId(benchmark.modelid);
    const normalizedModelId = normalizeId(modelId);
    
    const exactMatch = normalizedBenchmarkId === normalizedModelId;
    
    if (exactMatch) {
      console.log(`Found exact modelID match: "${benchmark.model}" (id: ${benchmark.modelid})`);
    }
    
    return exactMatch;
  });
  
  // Priorität 2: Exakter Match über Modellnamen in Einzelmodellen
  if (!match) {
    const normalizedModelName = normalizeModelName(modelName);
    const normalizedBaseModelName = normalizeModelName(baseModelName);
    
    match = singleModels.find(benchmark => {
      const normalizedBenchmarkName = normalizeModelName(benchmark.model);
      
      const exactMatch =
        normalizedBenchmarkName === normalizedModelName ||
        normalizedBenchmarkName === normalizedBaseModelName;
      
      if (exactMatch) {
        console.log(`Found exact model name match: "${benchmark.model}" (normalized: "${normalizedBenchmarkName}" vs "${normalizedModelName}")`);
      }
      
      return exactMatch;
    });
  }
  
  // Priorität 3: Partielle Matches in Einzelmodellen (nur wenn notwendig)
  if (!match) {
    const normalizedModelName = normalizeModelName(modelName);
    const normalizedBaseModelName = normalizeModelName(baseModelName);
    
    match = singleModels.find(benchmark => {
      const normalizedBenchmarkName = normalizeModelName(benchmark.model);
      
      // Nur partielle Matches, die sinnvoll sind (vermeidet falsche Matches)
      const partialMatch =
        (normalizedModelName.length > 3 && normalizedBenchmarkName.includes(normalizedModelName)) ||
        (normalizedBaseModelName.length > 3 && normalizedBenchmarkName.includes(normalizedBaseModelName));
      
      if (partialMatch) {
        console.log(`Found partial model name match: "${benchmark.model}" (normalized: "${normalizedBenchmarkName}" contains "${normalizedModelName}")`);
      }
      
      return partialMatch;
    });
  }
  
  // Alias-Matching wurde entfernt, da wir nur direkte Matches verwenden sollen
  
  // Priorität 4: Als letzter Ausweg, erweiterte Matching-Logik mit Priorisierung von Einzelmodellen
  if (!match) {
    const normalizedModelName = normalizeModelName(modelName);
    const normalizedBaseModelName = normalizeModelName(baseModelName);
    
    // Sammle alle möglichen Matches mit einer Bewertung
    const possibleMatches = benchmarkData.map(benchmark => {
      const benchmarkModelName = benchmark.model.toLowerCase();
      const benchmarkModelId = benchmark.modelid?.toLowerCase() || '';
      const normalizedBenchmarkName = normalizeModelName(benchmarkModelName);
      
      let score = 0;
      let matchReason = '';
      
      // Starke Abwertung für Kombinationsmodelle
      if (isCombinationModel(benchmark.model)) {
        score -= 50;
      }
      
      // ID-Matches (höchste Priorität)
      if (benchmarkModelId && modelId) {
        if (benchmarkModelId === modelId) {
          score += 100;
          matchReason = 'exact ID match';
        } else if (modelId.length > 3 && benchmarkModelId.includes(modelId)) {
          score += 30;
          matchReason = 'benchmark ID contains model ID';
        } else if (benchmarkModelId.length > 3 && modelId.includes(benchmarkModelId)) {
          score += 25;
          matchReason = 'model ID contains benchmark ID';
        }
      }
      
      // Name-Matches (zweite Priorität)
      if (normalizedBenchmarkName === normalizedModelName) {
        score += 90;
        matchReason = matchReason || 'exact normalized name match';
      } else if (normalizedBenchmarkName === normalizedBaseModelName) {
        score += 80;
        matchReason = matchReason || 'exact normalized base name match';
      } else if (normalizedModelName.length > 3 && normalizedBenchmarkName.includes(normalizedModelName)) {
        score += 20;
        matchReason = matchReason || 'normalized benchmark name contains model name';
      } else if (normalizedBaseModelName.length > 3 && normalizedBenchmarkName.includes(normalizedBaseModelName)) {
        score += 15;
        matchReason = matchReason || 'normalized benchmark name contains base name';
      }
      
      // Fallback auf nicht-normalisierte Namen (niedrigste Priorität)
      else if (benchmarkModelName === modelName.toLowerCase()) {
        score += 70;
        matchReason = matchReason || 'exact lowercase name match';
      } else if (benchmarkModelName === baseModelName.toLowerCase()) {
        score += 60;
        matchReason = matchReason || 'exact lowercase base name match';
      }
      
      return { benchmark, score, matchReason };
    });
    
    // Sortiere nach Score und wähle den besten Match (nur positive Scores)
    const sortedMatches = possibleMatches
      .filter(m => m.score > 0)
      .sort((a, b) => b.score - a.score);
    
    if (sortedMatches.length > 0) {
      const bestMatch = sortedMatches[0];
      console.log(`Fallback match for "${model.name}": "${bestMatch.benchmark.model}" (score: ${bestMatch.score}, reason: ${bestMatch.matchReason})`);
      match = bestMatch.benchmark;
      
      // Log alternative Matches für Debugging
      if (sortedMatches.length > 1) {
        console.log(`Alternative fallback matches for "${model.name}":`);
        sortedMatches.slice(1, 3).forEach(m => {
          console.log(`- "${m.benchmark.model}" (score: ${m.score}, reason: ${m.matchReason})`);
        });
      }
    }
  }
  
  if (match) {
    console.log(`Final match for "${model.name}": "${match.model}" with score ${match.pass_rate_2.toFixed(1)}%`);
  } else {
    console.log(`No match found for "${model.name}"`);
  }
  
  return match || null;
}

// Function to enrich model data with benchmark data
export function enrichModelDataWithBenchmark(models: ModelData[], benchmarkData: BenchmarkData[]): ModelData[] {
  console.log(`Enriching ${models.length} models with ${benchmarkData.length} benchmark entries`);
  
  const enrichedModels = models.map(model => {
    const benchmarkMatch = matchModelWithBenchmark(model, benchmarkData);
    if (benchmarkMatch) {
      console.log(`Found benchmark match for model "${model.name}" (id: ${model.id}): "${benchmarkMatch.model}" (id: ${benchmarkMatch.modelid})`);
      return {
        ...model,
        benchmarkData: benchmarkMatch
      };
    } else {
      console.log(`No benchmark match found for model "${model.name}" (id: ${model.id})`);
    }
    return model;
  });
  
  const modelsWithBenchmarkData = enrichedModels.filter(model => model.benchmarkData);
  console.log(`${modelsWithBenchmarkData.length} models enriched with benchmark data`);
  
  return enrichedModels;
}