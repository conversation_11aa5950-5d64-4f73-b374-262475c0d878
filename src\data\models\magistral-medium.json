{"basicInfo": {"modelId": "magistral-medium-2506", "displayName": "Magistral Medium", "provider": "<PERSON><PERSON><PERSON>", "modelFamily": "Magistral", "version": "2506", "description": "Das erste Reasoning-<PERSON><PERSON> von Mistral AI mit überlegenen domänenspezifischen, transparenten und mehrsprachigen Reasoning-Fähigkeiten. Magistral Medium ist die leistungsstärkere Enterprise-Version.", "releaseDate": "2025-06-10", "status": "Preview", "knowledgeCutoff": "Nicht spezifiziert"}, "technicalSpecs": {"contextWindow": 128000, "maxOutputTokens": 40000, "architecture": "Transformer", "supportedInputTypes": ["text"], "supportedOutputTypes": ["text"], "inputLimitations": {"maxContextEffective": "40k (Performance kann nach 40k degradieren)"}}, "capabilities": {"functionCalling": false, "vision": false, "pdfSupport": false, "audioInput": false, "audioOutput": false, "imageGeneration": false, "codeExecution": false, "systemInstructions": true, "promptCaching": false, "batchProcessing": false, "reasoning": true, "thinking": true, "grounding": false, "multilingualSupport": true, "embeddingImageInput": false, "structuredOutputs": false, "webBrowsing": false, "codeInterpreter": false, "dalleIntegration": false, "realTimeAPI": false, "liteLLM-provisioning": true}, "performance": {"latency": "Fast", "rateLimits": {"queriesPerMinute": 50, "tokensPerMinute": 500000}, "reasoningPerformance": {"averageReasoningTime": "Variable", "reasoningEfficiency": "High"}, "temperature": {"min": 0, "max": 1, "default": 0.7}}, "pricing": {"inputCostPer1MTokens": 3.0, "outputCostPer1MTokens": 15.0, "currency": "USD"}, "availability": {"regions": [{"region": "global", "availability": "Preview"}], "dataProcessingRegions": ["Multi-region"], "supportedPlatforms": ["La Plateforme", "<PERSON>", "Amazon SageMaker", "IBM WatsonX", "Azure AI", "Google Cloud Marketplace"]}, "security": {"dataResidency": false, "cmekSupport": false, "vpcSupport": false, "accessTransparency": false, "complianceStandards": ["Apache 2.0"]}, "usageTypes": {"dynamicSharedQuota": true, "provisionedThroughput": false, "fixedQuota": false}, "benchmarks": [{"benchmarkName": "AIME 2024", "category": "Mathematics", "score": 73.6, "alternativeScores": {"multipleAttempts": 90.0}, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-10", "notes": "73.6% mit single attempt, 90% mit majority voting @64"}, {"benchmarkName": "AIME 2025", "category": "Mathematics", "score": 64.9, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-10", "notes": "Magistral Medium Performance auf AIME 2025"}, {"benchmarkName": "GPQA Diamond", "category": "Science", "score": 70.8, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-10", "notes": "Graduate-level naturwissenschaftliche Fragen"}, {"benchmarkName": "LiveCodeBench v2025", "category": "Code generation", "score": 59.4, "metric": "Pass@1", "attemptType": "single attempt", "date": "2025-06-10", "notes": "Aktuelle Live-Code-Generierung ohne Kontamination"}, {"benchmarkName": "Aider-Polyglot", "category": "Code editing", "score": 47.1, "metric": "Pass Rate", "attemptType": "single attempt", "date": "2025-06-10", "notes": "Mehrsprachige Code-Bearbeitung Performance"}], "metadata": {"lastUpdated": "2025-06-15T11:25:00Z", "dataSource": "Mistral AI Blog Post, Ollama Documentation", "version": "1.0"}}