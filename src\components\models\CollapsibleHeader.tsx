import * as React from "react";
import { useState } from "react";
import { Button } from "../ui/button";
import { ChevronUp, ChevronDown } from "lucide-react";
import { useTranslation, t } from "../../contexts/TranslationContext";

// Define global window interface
declare global {
  interface Window {
    translations?: any;
    currentLang?: string;
  }
}

interface CollapsibleHeaderProps {
  children: React.ReactNode;
  titleKey?: string;
  descriptionKey?: string;
  totalModels?: number;
  totalProviders?: number;
  totalBenchmarks?: number;
  title?: string; // fallback for legacy usage
  description?: string; // fallback for legacy usage
  isCollapsedByDefault?: boolean;
}

export function CollapsibleHeader({
  children,
  titleKey = "models.header",
  descriptionKey = "models.description",
  totalModels = 0,
  totalProviders = 0,
  totalBenchmarks = 0,
  title,
  description,
  isCollapsedByDefault = false
}: CollapsibleHeaderProps) {
  const [isCollapsed, setIsCollapsed] = useState(isCollapsedByDefault);
  const { translations } = useTranslation();

  // Helper function with fallback logic
  const getTranslation = (key: string, replacements?: Record<string, string | number>) => {
    // Try context translations first
    let result = t(translations, key, replacements);
    
    // Fallback to global window translations
    if (result === key && typeof window !== 'undefined' && window.translations) {
      result = t(window.translations, key, replacements);
    }
    
    return result;
  };

  // Use translation keys if provided, otherwise fall back to static props
  const displayTitle = titleKey ? getTranslation(titleKey) : title || "Models";
  const displayDescription = descriptionKey
    ? getTranslation(descriptionKey, { count: totalModels, totalModels, totalProviders, totalBenchmarks, modelCount: totalModels, benchmarkCount: totalBenchmarks })
    : description || "";

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <div className="flex-1">
          <h1 className="text-xl font-bold text-foreground mb-2">
            {displayTitle}
          </h1>
          {!isCollapsed && (
            <p className="text-sm text-muted-foreground">
              {displayDescription}
            </p>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="flex items-center gap-2 ml-4"
        >
          {isCollapsed ? (
            <>
              <ChevronDown className="w-4 h-4" />
              {getTranslation('components.collapsible_header.show_info')}
            </>
          ) : (
            <>
              <ChevronUp className="w-4 h-4" />
              {getTranslation('components.collapsible_header.hide_info')}
            </>
          )}
        </Button>
      </div>
      
      {!isCollapsed && (
        <div className="transition-all duration-300 ease-in-out">
          {children}
        </div>
      )}
    </div>
  );
}